using System.Diagnostics;
using Microsoft.Win32;

namespace AlphaPM.Services.Window
{
    /// <summary>
    /// Windows 文件操作服务
    /// 封装常用的文件和文件夹操作功能
    /// </summary>
    public class WindowsFileOperationService
    {
        #region 文件对话框操作

        /// <summary>
        /// 打开文件对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="filter">文件过滤器</param>
        /// <param name="initialDirectory">初始目录</param>
        /// <param name="multiSelect">是否允许多选</param>
        /// <returns>选择的文件路径</returns>
        public static FileDialogResult OpenFileDialog(
            string title = "选择文件",
            string filter = "所有文件|*.*",
            string? initialDirectory = null,
            bool multiSelect = false)
        {
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = title,
                    Filter = filter,
                    Multiselect = multiSelect,
                    CheckFileExists = true,
                    CheckPathExists = true
                };

                if (!string.IsNullOrEmpty(initialDirectory) && Directory.Exists(initialDirectory))
                {
                    dialog.InitialDirectory = initialDirectory;
                }

                var result = dialog.ShowDialog();
                return new FileDialogResult
                {
                    Success = result == true,
                    SelectedFiles = result == true ? dialog.FileNames.ToList() : new List<string>(),
                    SelectedFile = result == true ? dialog.FileName : string.Empty
                };
            }
            catch (Exception ex)
            {
                return new FileDialogResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 保存文件对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="filter">文件过滤器</param>
        /// <param name="defaultFileName">默认文件名</param>
        /// <param name="initialDirectory">初始目录</param>
        /// <returns>保存文件路径</returns>
        public static FileDialogResult SaveFileDialog(
            string title = "保存文件",
            string filter = "所有文件|*.*",
            string defaultFileName = "",
            string? initialDirectory = null)
        {
            try
            {
                var dialog = new SaveFileDialog
                {
                    Title = title,
                    Filter = filter,
                    FileName = defaultFileName,
                    CheckPathExists = true,
                    OverwritePrompt = true
                };

                if (!string.IsNullOrEmpty(initialDirectory) && Directory.Exists(initialDirectory))
                {
                    dialog.InitialDirectory = initialDirectory;
                }

                var result = dialog.ShowDialog();
                return new FileDialogResult
                {
                    Success = result == true,
                    SelectedFile = result == true ? dialog.FileName : string.Empty
                };
            }
            catch (Exception ex)
            {
                return new FileDialogResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 选择文件夹对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="initialDirectory">初始目录</param>
        /// <returns>选择的文件夹路径</returns>
        public static FolderDialogResult SelectFolderDialog(
            string title = "选择文件夹",
            string? initialDirectory = null)
        {
            try
            {
                var dialog = new OpenFolderDialog
                {
                    Title = title,
                    Multiselect = false
                };

                if (!string.IsNullOrEmpty(initialDirectory) && Directory.Exists(initialDirectory))
                {
                    dialog.InitialDirectory = initialDirectory;
                }

                var result = dialog.ShowDialog();
                return new FolderDialogResult
                {
                    Success = result == true,
                    SelectedPath = result == true ? dialog.FolderName : string.Empty
                };
            }
            catch (Exception ex)
            {
                return new FolderDialogResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region 文件操作

        /// <summary>
        /// 在资源管理器中打开文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>操作结果</returns>
        public static OperationResult OpenFileInExplorer(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"文件不存在: {filePath}"
                    };
                }

                Process.Start("explorer.exe", $"/select,\"{filePath}\"");
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 在资源管理器中打开文件夹
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <returns>操作结果</returns>
        public static OperationResult OpenFolderInExplorer(string folderPath)
        {
            try
            {
                if (!Directory.Exists(folderPath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"文件夹不存在: {folderPath}"
                    };
                }

                Process.Start("explorer.exe", $"\"{folderPath}\"");
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 使用默认程序打开文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>操作结果</returns>
        public static OperationResult OpenFileWithDefaultProgram(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"文件不存在: {filePath}"
                    };
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>操作结果</returns>
        public static OperationResult CopyFile(string sourcePath, string destinationPath, bool overwrite = false)
        {
            try
            {
                if (!File.Exists(sourcePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"源文件不存在: {sourcePath}"
                    };
                }

                var destinationDir = Path.GetDirectoryName(destinationPath);
                if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
                {
                    Directory.CreateDirectory(destinationDir);
                }

                File.Copy(sourcePath, destinationPath, overwrite);
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>操作结果</returns>
        public static OperationResult MoveFile(string sourcePath, string destinationPath, bool overwrite = false)
        {
            try
            {
                if (!File.Exists(sourcePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"源文件不存在: {sourcePath}"
                    };
                }

                var destinationDir = Path.GetDirectoryName(destinationPath);
                if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
                {
                    Directory.CreateDirectory(destinationDir);
                }

                if (File.Exists(destinationPath))
                {
                    if (overwrite)
                    {
                        File.Delete(destinationPath);
                    }
                    else
                    {
                        return new OperationResult
                        {
                            Success = false,
                            ErrorMessage = $"目标文件已存在: {destinationPath}"
                        };
                    }
                }

                File.Move(sourcePath, destinationPath);
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>操作结果</returns>
        public static OperationResult DeleteFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"文件不存在: {filePath}"
                    };
                }

                File.Delete(filePath);
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region 文件夹操作

        /// <summary>
        /// 创建文件夹
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <returns>操作结果</returns>
        public static OperationResult CreateFolder(string folderPath)
        {
            try
            {
                if (Directory.Exists(folderPath))
                {
                    return new OperationResult
                    {
                        Success = true,
                        Message = "文件夹已存在"
                    };
                }

                Directory.CreateDirectory(folderPath);
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 复制文件夹
        /// </summary>
        /// <param name="sourcePath">源文件夹路径</param>
        /// <param name="destinationPath">目标文件夹路径</param>
        /// <param name="recursive">是否递归复制子文件夹</param>
        /// <returns>操作结果</returns>
        public static OperationResult CopyFolder(string sourcePath, string destinationPath, bool recursive = true)
        {
            try
            {
                if (!Directory.Exists(sourcePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"源文件夹不存在: {sourcePath}"
                    };
                }

                if (!Directory.Exists(destinationPath))
                {
                    Directory.CreateDirectory(destinationPath);
                }

                // 复制文件
                foreach (var file in Directory.GetFiles(sourcePath))
                {
                    var fileName = Path.GetFileName(file);
                    var destFile = Path.Combine(destinationPath, fileName);
                    File.Copy(file, destFile, true);
                }

                // 递归复制子文件夹
                if (recursive)
                {
                    foreach (var directory in Directory.GetDirectories(sourcePath))
                    {
                        var dirName = Path.GetFileName(directory);
                        var destDir = Path.Combine(destinationPath, dirName);
                        var result = CopyFolder(directory, destDir, recursive);
                        if (!result.Success)
                        {
                            return result;
                        }
                    }
                }

                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 删除文件夹
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="recursive">是否递归删除</param>
        /// <returns>操作结果</returns>
        public static OperationResult DeleteFolder(string folderPath, bool recursive = true)
        {
            try
            {
                if (!Directory.Exists(folderPath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = $"文件夹不存在: {folderPath}"
                    };
                }

                Directory.Delete(folderPath, recursive);
                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion

        #region 异步操作

        /// <summary>
        /// 异步复制文件
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>操作结果</returns>
        public static async Task<OperationResult> CopyFileAsync(string sourcePath, string destinationPath, bool overwrite = false)
        {
            return await Task.Run(() => CopyFile(sourcePath, destinationPath, overwrite));
        }

        /// <summary>
        /// 异步复制文件夹
        /// </summary>
        /// <param name="sourcePath">源文件夹路径</param>
        /// <param name="destinationPath">目标文件夹路径</param>
        /// <param name="recursive">是否递归复制</param>
        /// <returns>操作结果</returns>
        public static async Task<OperationResult> CopyFolderAsync(string sourcePath, string destinationPath, bool recursive = true)
        {
            return await Task.Run(() => CopyFolder(sourcePath, destinationPath, recursive));
        }

        #endregion

        #region 实用工具方法

        /// <summary>
        /// 获取文件大小（格式化）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>格式化的文件大小</returns>
        public static string GetFormattedFileSize(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "文件不存在";

                var fileInfo = new FileInfo(filePath);
                return FormatFileSize(fileInfo.Length);
            }
            catch
            {
                return "无法获取";
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的大小</returns>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 检查路径是否安全（防止路径遍历攻击）
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="basePath">基础路径</param>
        /// <returns>是否安全</returns>
        public static bool IsPathSafe(string path, string basePath)
        {
            try
            {
                var fullPath = Path.GetFullPath(path);
                var fullBasePath = Path.GetFullPath(basePath);
                return fullPath.StartsWith(fullBasePath, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }

    #region 结果类

    /// <summary>
    /// 文件对话框结果
    /// </summary>
    public class FileDialogResult
    {
        public bool Success { get; set; }
        public string SelectedFile { get; set; } = string.Empty;
        public List<string> SelectedFiles { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 文件夹对话框结果
    /// </summary>
    public class FolderDialogResult
    {
        public bool Success { get; set; }
        public string SelectedPath { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 操作结果
    /// </summary>
    public class OperationResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? ErrorMessage { get; set; }
    }

    #endregion
}
