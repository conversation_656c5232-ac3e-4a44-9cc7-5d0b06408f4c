#if false
using System;
using System.Collections.Generic;
using System.IO;
using AlphaPM.Models.DWG;
using Prism.Services.Dialogs;
using Zylo.WPF.Controls.TreeView;
using Wpf.Ui.Controls;

namespace AlphaPM.Extensions;

// ==================== 备份代码开始 ====================
// 重新规划前的原始代码，备份于此
// 日期: 2024年
// 说明: 功能重新规划前的完整代码备份

/// <summary>
/// TreeNodeData扩展方法 - DWG业务数据操作
/// </summary>
/// <remarks>
/// 🎯 功能特点：
/// - 为TreeNodeData提供DWG业务数据的便捷访问
/// - 自动处理UserData的类型转换和空值检查
/// - 提供类型安全的业务数据操作方法
/// - 支持链式调用和流畅的API设计
/// 
/// 💡 使用示例：
/// ```csharp
/// var yearNode = new TreeNodeData("2024");
/// 
/// // 设置DWG业务数据
/// yearNode.SetDWGData(DWGBusinessData.CreateYearData("张总监", 50000000m));
/// 
/// // 获取DWG业务数据
/// var dwgData = yearNode.GetDWGData();
/// if (dwgData != null)
/// {
///     Console.WriteLine($"负责人: {dwgData.Manager}");
/// }
/// 
/// // 检查是否有DWG数据
/// if (yearNode.HasDWGData())
/// {
///     var manager = yearNode.GetDWGManager();
///     var budget = yearNode.GetDWGBudget();
/// }
/// 
/// // 链式设置
/// projectNode.SetDWGManager("李经理")
///           .SetDWGBudget(8000000m)
///           .SetDWGProjectPath(@"C:\Projects\2024\商业综合体");
/// ```
/// </remarks>
public static class TreeNodeDataExtensions
{
    #region DWG业务数据基础操作

    /// <summary>
    /// 获取DWG业务数据
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>DWG业务数据，如果不存在则返回null</returns>
    public static DWGBusinessData? GetDWGData(this TreeNodeData node)
    {
        return node.UserData as DWGBusinessData;
    }

    /// <summary>
    /// 设置DWG业务数据
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="dwgData">DWG业务数据</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGData(this TreeNodeData node, DWGBusinessData dwgData)
    {
        node.UserData = dwgData;
        return node;
    }

    /// <summary>
    /// 检查是否有DWG业务数据
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否有DWG业务数据</returns>
    public static bool HasDWGData(this TreeNodeData node)
    {
        return node.UserData is DWGBusinessData;
    }

    /// <summary>
    /// 确保有DWG业务数据，如果没有则创建
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>DWG业务数据</returns>
    public static DWGBusinessData EnsureDWGData(this TreeNodeData node)
    {
        if (node.UserData is not DWGBusinessData dwgData)
        {
            dwgData = new DWGBusinessData();
            node.UserData = dwgData;
        }

        return dwgData;
    }

    /// <summary>
    /// 清除DWG业务数据
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData ClearDWGData(this TreeNodeData node)
    {
        node.UserData = null;
        return node;
    }

    #endregion

    #region 项目管理属性访问

    /// <summary>
    /// 获取DWG预算
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>预算金额</returns>
    public static decimal GetDWGBudget(this TreeNodeData node)
    {
        return node.GetDWGData()?.Budget ?? 0;
    }

    /// <summary>
    /// 设置DWG预算
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="budget">预算金额</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGBudget(this TreeNodeData node, decimal budget)
    {
        node.EnsureDWGData().Budget = budget;
        return node;
    }


    /// <summary>
    /// 获取DWG项目开始时间
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>开始时间</returns>
    public static DateTime? GetDWGStartDate(this TreeNodeData node)
    {
        return node.GetDWGData()?.StartDate;
    }

    /// <summary>
    /// 设置DWG项目开始时间
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="startDate">开始时间</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGStartDate(this TreeNodeData node, DateTime? startDate)
    {
        node.EnsureDWGData().StartDate = startDate;
        return node;
    }

    /// <summary>
    /// 获取DWG项目结束时间
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>结束时间</returns>
    public static DateTime? GetDWGEndDate(this TreeNodeData node)
    {
        return node.GetDWGData()?.EndDate;
    }

    /// <summary>
    /// 设置DWG项目结束时间
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGEndDate(this TreeNodeData node, DateTime? endDate)
    {
        node.EnsureDWGData().EndDate = endDate;
        return node;
    }

    #endregion

    #region 动态路径生成

    /// <summary>
    /// 获取动态生成的项目路径
    /// 基于全局项目根路径和节点层级结构生成
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="baseProjectPath">基础项目路径，如果为空则使用默认路径</param>
    /// <returns>动态生成的项目路径</returns>
    public static string GetDynamicProjectPath(this TreeNodeData node, string baseProjectPath = "")
    {
        if (string.IsNullOrEmpty(baseProjectPath))
        {
            // 使用默认的项目根路径
            baseProjectPath = AlphaPM.Data.YDataModel.Current.YdwgData.ProjectPath;
        }

        // 构建路径：基础路径/年份/项目名/栋号
        var pathParts = new List<string> { baseProjectPath };

        // 向上遍历找到完整路径
        var pathNodes = new List<TreeNodeData>();
        var current = node;
        while (current != null)
        {
            pathNodes.Insert(0, current);
            current = current.Parent;
        }

        // 添加路径部分
        foreach (var pathNode in pathNodes)
        {
            pathParts.Add(pathNode.Name);
        }

        return Path.Combine(pathParts.ToArray());
    }

    /// <summary>
    /// 获取项目级别的动态路径（到项目层级为止）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="baseProjectPath">基础项目路径</param>
    /// <returns>项目级别的路径</returns>
    public static string GetProjectLevelPath(this TreeNodeData node, string baseProjectPath = "")
    {
        if (string.IsNullOrEmpty(baseProjectPath))
        {
            baseProjectPath = AlphaPM.Data.YDataModel.Current.YdwgData.ProjectPath;
        }

        // 找到项目级别的节点
        var projectNode = node.Level switch
        {
            1 => node, // 年份节点，返回年份路径
            2 => node, // 项目节点，返回项目路径
            3 => node.Parent, // 栋号节点，返回父项目路径
            _ => node
        };

        if (projectNode == null) return baseProjectPath;

        var pathParts = new List<string> { baseProjectPath };

        // 向上遍历到项目级别
        var pathNodes = new List<TreeNodeData>();
        var current = projectNode;
        while (current != null && current.Level <= 2)
        {
            pathNodes.Insert(0, current);
            current = current.Parent;
        }

        foreach (var pathNode in pathNodes)
        {
            pathParts.Add(pathNode.Name);
        }

        return Path.Combine(pathParts.ToArray());
    }

    #endregion

    #region 建筑属性访问

    /// <summary>
    /// 获取DWG建筑面积
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>建筑面积</returns>
    public static decimal GetDWGBuildingArea(this TreeNodeData node)
    {
        return node.GetDWGData()?.BuildingArea ?? 0;
    }

    /// <summary>
    /// 设置DWG建筑面积
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="buildingArea">建筑面积</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGBuildingArea(this TreeNodeData node, decimal buildingArea)
    {
        node.EnsureDWGData().BuildingArea = buildingArea;
        return node;
    }

    /// <summary>
    /// 获取DWG楼层数量
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>楼层数量</returns>
    public static int GetDWGFloorCount(this TreeNodeData node)
    {
        return node.GetDWGData()?.FloorCount ?? 0;
    }

    /// <summary>
    /// 设置DWG楼层数量
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="floorCount">楼层数量</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGFloorCount(this TreeNodeData node, int floorCount)
    {
        node.EnsureDWGData().FloorCount = floorCount;
        return node;
    }

    /// <summary>
    /// 获取DWG建筑类型
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>建筑类型</returns>
    public static string GetDWGBuildingType(this TreeNodeData node)
    {
        return node.GetDWGData()?.BuildingType ?? string.Empty;
    }

    /// <summary>
    /// 设置DWG建筑类型
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="buildingType">建筑类型</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGBuildingType(this TreeNodeData node, string buildingType)
    {
        node.EnsureDWGData().BuildingType = buildingType;
        return node;
    }

    /// <summary>
    /// 获取DWG建筑状态
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>建筑状态</returns>
    public static string GetDWGBuildingStatus(this TreeNodeData node)
    {
        return node.GetDWGData()?.BuildingStatus ?? string.Empty;
    }

    /// <summary>
    /// 设置DWG建筑状态
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="buildingStatus">建筑状态</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGBuildingStatus(this TreeNodeData node, string buildingStatus)
    {
        node.EnsureDWGData().BuildingStatus = buildingStatus;
        return node;
    }

    #endregion

    #region 计算属性访问

    /// <summary>
    /// 获取DWG项目进度百分比
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>进度百分比</returns>
    public static double GetDWGProgressPercentage(this TreeNodeData node)
    {
        return node.GetDWGData()?.ProgressPercentage ?? 0;
    }

    /// <summary>
    /// 检查DWG项目是否已过期
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否已过期</returns>
    public static bool IsDWGOverdue(this TreeNodeData node)
    {
        return node.GetDWGData()?.IsOverdue ?? false;
    }

    /// <summary>
    /// 获取DWG项目剩余天数
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>剩余天数</returns>
    public static int GetDWGRemainingDays(this TreeNodeData node)
    {
        return node.GetDWGData()?.RemainingDays ?? 0;
    }

    #endregion

    #region 业务逻辑方法

    /// <summary>
    /// 获取DWG状态描述
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>状态描述</returns>
    public static string GetDWGStatusDescription(this TreeNodeData node)
    {
        var dwgData = node.GetDWGData();
        if (dwgData == null) return node.Description;

        return node.Level switch
        {
            1 =>
                $"年度项目 | 总预算: {node.CalculateDWGYearTotalBudget():N0}万元 | 项目数: {node.Children.Count} | 负责人: {dwgData.Manager}",
            2 =>
                $"项目进度: {dwgData.ProgressPercentage:F1}% | 预算: {dwgData.Budget:N0}万元 | 经理: {dwgData.Manager} | 状态: {dwgData.ProjectStatus}",
            3 =>
                $"{dwgData.BuildingType} | {dwgData.BuildingArea:N1}㎡ | {dwgData.FloorCount}层 | {dwgData.BuildingStatus} | 负责人: {dwgData.Manager} | 设计师: {dwgData.Designer}",
            _ => dwgData.ToString()
        };
    }

    /// <summary>
    /// 验证DWG业务数据
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>验证结果</returns>
    public static bool ValidateDWGData(this TreeNodeData node)
    {
        var dwgData = node.GetDWGData();
        if (dwgData == null) return true; // 没有DWG数据时认为有效

        return node.Level switch
        {
            1 => dwgData.ValidateYearData(),
            2 => dwgData.ValidateProjectData(),
            3 => dwgData.ValidateBuildingData(),
            _ => true
        };
    }

    #endregion

    #region DWG节点类型判断和管理

    /// <summary>
    /// 判断是否为DWG年份节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为年份节点</returns>
    public static bool IsDWGYearNode(this TreeNodeData node)
    {
        return node.Level == 1 ||
               node.NodeType?.ToLower() == DWGNodeTypes.Year ||
               (node.Parent == null && int.TryParse(node.Name, out _));
    }

    /// <summary>
    /// 判断是否为DWG项目节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为项目节点</returns>
    public static bool IsDWGProjectNode(this TreeNodeData node)
    {
        return node.Level == 2 ||
               node.NodeType?.ToLower() == DWGNodeTypes.Project ||
               (node.Parent != null && node.Parent.IsDWGYearNode());
    }

    /// <summary>
    /// 判断是否为DWG建筑节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为建筑节点</returns>
    public static bool IsDWGBuildingNode(this TreeNodeData node)
    {
        return node.Level == 3 ||
               node.NodeType?.ToLower() == DWGNodeTypes.Building ||
               (node.Parent != null && node.Parent.IsDWGProjectNode());
    }

    /// <summary>
    /// 判断是否为文件夹类型节点（年份或项目）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为文件夹类型</returns>
    public static bool IsDWGFolderNode(this TreeNodeData node)
    {
        return node.IsDWGYearNode() || node.IsDWGProjectNode() ||
               node.NodeType?.ToLower() == DWGNodeTypes.Year;
    }

    /// <summary>
    /// 获取DWG节点层级（智能判断）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>节点层级（1=年份，2=项目，3=栋号）</returns>
    public static int GetDWGNodeLevel(this TreeNodeData node)
    {
        // 优先使用Level属性
        if (node.Level >= 1 && node.Level <= 3)
            return node.Level;

        // 根据父子关系判断
        if (node.Parent == null) return 1; // 顶层年份
        if (node.Parent.Parent == null) return 2; // 二级项目
        return 3; // 三级栋号
    }

    /// <summary>
    /// 获取DWG节点类型的显示名称
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>显示名称</returns>
    public static string GetDWGNodeDisplayName(this TreeNodeData node)
    {
        return node.NodeType?.ToLower() switch
        {
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Building => "建筑",
            _ => node.GetDWGNodeLevel() switch
            {
                1 => "年份",
                2 => "项目",
                3 => "建筑",
                _ => "未知"
            }
        };
    }

    /// <summary>
    /// 获取节点类型的显示名称（静态方法，兼容原有代码）
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>显示名称</returns>
    public static string GetDisplayName(string nodeType)
    {
        return nodeType?.ToLower() switch
        {
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Building => "建筑",
            _ => "未知"
        };
    }

    /// <summary>
    /// 构建DWG节点的完整路径
    /// </summary>
    /// <param name="node">目标节点</param>
    /// <returns>节点的完整路径</returns>
    /// <remarks>
    /// 🔍 路径构建逻辑：
    /// - 从当前节点开始，向上遍历到根节点
    /// - 收集所有父节点的名称
    /// - 按层级顺序组合成完整路径
    ///
    /// 📋 路径示例：
    /// - 根节点(年份)："/2024"
    /// - 二级节点(项目)："/2024/示例项目"
    /// - 三级节点(栋号)："/2024/示例项目/1#楼"
    /// </remarks>
    public static string BuildDWGNodePath(this TreeNodeData node)
    {
        if (node == null)
            return string.Empty;

        var pathParts = new List<string>();
        var currentNode = node;

        // 从当前节点向上遍历到根节点
        while (currentNode != null)
        {
            pathParts.Insert(0, currentNode.Name); // 插入到开头，保持正确的层级顺序
            currentNode = currentNode.Parent;
        }

        // 组合成完整路径，以 "/" 开头
        return "/" + string.Join("/", pathParts);
    }

    /// <summary>
    /// 设置DWG节点类型（根据层级自动设置）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="level">节点层级</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGNodeType(this TreeNodeData node, int level)
    {
        node.Level = level;
        node.NodeType = level switch
        {
            1 => DWGNodeTypes.Year,
            2 => DWGNodeTypes.Project,
            3 => DWGNodeTypes.Building,
        };
        return node;
    }

    /// <summary>
    /// 获取DWG节点的年份（向上查找年份节点）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>年份字符串，如果找不到则返回空字符串</returns>
    public static string GetDWGYear(this TreeNodeData node)
    {
        var current = node;
        while (current != null)
        {
            if (current.IsDWGYearNode())
            {
                return current.Name;
            }

            current = current.Parent;
        }

        return string.Empty;
    }

    /// <summary>
    /// 获取DWG节点的项目名称（向上查找项目节点）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>项目名称，如果找不到则返回空字符串</returns>
    public static string GetDWGProjectName(this TreeNodeData node)
    {
        var current = node;
        while (current != null)
        {
            if (current.IsDWGProjectNode())
            {
                return current.Name;
            }

            current = current.Parent;
        }

        return string.Empty;
    }

    /// <summary>
    /// 检查DWG节点是否可以添加子节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否可以添加子节点</returns>
    public static bool CanAddDWGChild(this TreeNodeData node)
    {
        return node.GetDWGNodeLevel() switch
        {
            1 => true, // 年份节点可以添加项目
            2 => true, // 项目节点可以添加栋号
            3 => false, // 栋号节点不能添加子节点
            _ => false
        };
    }

    /// <summary>
    /// 获取DWG节点可以添加的子节点类型
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>子节点类型名称</returns>
    public static string GetDWGChildNodeTypeName(this TreeNodeData node)
    {
        return node.GetDWGNodeLevel() switch
        {
            1 => "项目",
            2 => "栋号",
            _ => "无"
        };
    }

    #endregion

    #region DWG预算计算

    /// <summary>
    /// 计算年份节点的总预算（所有子项目预算之和）
    /// </summary>
    /// <param name="yearNode">年份节点</param>
    /// <returns>总预算金额</returns>
    public static decimal CalculateDWGYearTotalBudget(this TreeNodeData yearNode)
    {
        if (!yearNode.IsDWGYearNode()) return 0;

        decimal totalBudget = 0;
        foreach (var projectNode in yearNode.Children)
        {
            if (projectNode.IsDWGProjectNode())
            {
                totalBudget += projectNode.GetDWGBudget();
            }
        }

        return totalBudget;
    }

    /// <summary>
    /// 计算项目节点的总预算（所有子栋号预算之和，如果栋号没有预算则返回项目预算）
    /// </summary>
    /// <param name="projectNode">项目节点</param>
    /// <returns>总预算金额</returns>
    public static decimal CalculateDWGProjectTotalBudget(this TreeNodeData projectNode)
    {
        if (!projectNode.IsDWGProjectNode()) return 0;

        // 如果有子栋号，计算栋号预算之和
        if (projectNode.Children.Count > 0)
        {
            decimal totalBudget = 0;
            foreach (var buildingNode in projectNode.Children)
            {
                if (buildingNode.IsDWGBuildingNode())
                {
                    totalBudget += buildingNode.GetDWGBudget();
                }
            }

            // 如果栋号有预算，返回栋号预算之和；否则返回项目预算
            return totalBudget > 0 ? totalBudget : projectNode.GetDWGBudget();
        }

        return projectNode.GetDWGBudget();
    }

    /// <summary>
    /// 更新年份节点的预算为所有子项目预算之和
    /// </summary>
    /// <param name="yearNode">年份节点</param>
    /// <returns>年份节点本身，支持链式调用</returns>
    public static TreeNodeData UpdateDWGYearBudget(this TreeNodeData yearNode)
    {
        if (yearNode.IsDWGYearNode())
        {
            var totalBudget = yearNode.CalculateDWGYearTotalBudget();
            yearNode.SetDWGBudget(totalBudget);
        }

        return yearNode;
    }

    /// <summary>
    /// 递归更新整个树的预算（从栋号到项目到年份）
    /// </summary>
    /// <param name="rootNode">根节点</param>
    /// <returns>根节点本身，支持链式调用</returns>
    public static TreeNodeData UpdateDWGTreeBudgets(this TreeNodeData rootNode)
    {
        // 递归更新所有子节点
        foreach (var child in rootNode.Children)
        {
            child.UpdateDWGTreeBudgets();
        }

        // 更新当前节点预算
        if (rootNode.IsDWGYearNode())
        {
            rootNode.UpdateDWGYearBudget();
        }

        return rootNode;
    }

    #endregion

    #region DWG节点创建模板

    #region 年

    /// <summary>
    /// 创建年份节点模板
    /// </summary>
    /// <param name="Name">年份</param>
    /// <param name="description">说明</param>
    /// <returns>年份节点</returns>
    public static TreeNodeData CreateDWGYearTemplate(string Name, string description = null)
    {
        if (description == null)
        {
            description = $"{Name}年度项目";
        }

        var yearNode = new TreeNodeData(Name.ToString())
        {
            Id = 0,
            Description = description,
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            WpfUiSymbol = SymbolRegular.CalendarLtr24,

            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        // 设置DWG业务数据（年份节点预算由子项目汇总）
        yearNode.SetDWGData(DWGBusinessData.CreateYearData());

        return yearNode;
    }

    /// <summary>
    /// 创建当前年份节点模板
    /// </summary>
    /// <returns>当前年份节点</returns>
    public static TreeNodeData CreateDWGCurrentYearTemplate()
    {
        return CreateDWGYearTemplate(DateTime.Now.Year.ToString());
    }

    /// <summary>
    /// 从对话框结果创建年份节点
    /// </summary>
    /// <param name="dialogResult">对话框结果参数</param>
    /// <returns>创建的年份节点</returns>
    public static TreeNodeData CreateDWGYearFromDialogResult(IDialogParameters dialogResult)
    {
        var nodeName = dialogResult.GetValue<string>("NodeName");
        var nodeDescription = dialogResult.GetValue<string>("NodeDescription");

        return CreateDWGYearTemplate(nodeName, nodeDescription);
    }

    #endregion


    #region 项目

    /// <summary>
    /// 创建项目节点模板
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="parentId">父节点ID（年份节点ID）</param>
    /// <param name="manager">项目经理姓名</param>
    /// <param name="budget">项目预算(万元)</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>项目节点</returns>
    public static TreeNodeData CreateDWGProjectTemplate(
        string projectName,
        int parentId,
        string manager = "",
        decimal budget = 0,
        DateTime? startDate = null,
        DateTime? endDate = null)
    {
        var projectNode = new TreeNodeData(projectName)
        {
            Id = 0,
            Description = $"{projectName}项目",
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            ParentId = parentId,
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.BuildingBank20,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        // 设置DWG业务数据
        projectNode.SetDWGData(DWGBusinessData.CreateProjectData(
            manager, budget, startDate, endDate));

        return projectNode;
    }

    /// <summary>
    /// 创建简单项目节点模板（只需要名称和经理）
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="parentId">父节点ID（年份节点ID）</param>
    /// <param name="manager">项目经理姓名</param>
    /// <returns>项目节点</returns>
    public static TreeNodeData CreateDWGSimpleProjectTemplate(string projectName, int parentId, string manager = "")
    {
        return CreateDWGProjectTemplate(projectName, parentId, manager);
    }

    #endregion


    #region 栋

    /// <summary>
    /// 创建栋号节点模板
    /// </summary>
    /// <param name="buildingName">栋号名称</param>
    /// <param name="parentId">父节点ID（项目节点ID）</param>
    /// <param name="buildingType">建筑类型</param>
    /// <param name="buildingArea">建筑面积(平方米)</param>
    /// <param name="floorCount">楼层数</param>
    /// <param name="status">建筑状态</param>
    /// <param name="manager">建筑负责人姓名</param>
    /// <param name="designer">设计人员姓名</param>
    /// <returns>栋号节点</returns>
    public static TreeNodeData CreateDWGBuildingTemplate(
        string buildingName,
        int parentId,
        string buildingType = "",
        decimal buildingArea = 0,
        int floorCount = 0,
        string status = "规划中",
        string manager = "",
        string designer = "")
    {
        var buildingNode = new TreeNodeData(buildingName)
        {
            Id = 0,
            Description = string.IsNullOrEmpty(buildingType) ? buildingName : $"{buildingName} - {buildingType}",
            NodeType = DWGNodeTypes.Building,
            Level = 3,
            ParentId = parentId,
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.Building24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        // 设置DWG业务数据
        buildingNode.SetDWGData(DWGBusinessData.CreateBuildingData(
            buildingType, buildingArea, floorCount, status, manager, designer));

        return buildingNode;
    }

    #endregion

    #endregion

    #region DWG批量创建模板

    /// <summary>
    /// 创建完整项目结构模板
    /// </summary>
    /// <param name="year">年份</param>
    /// <param name="projectName">项目名称</param>
    /// <param name="buildingNames">栋号名称列表</param>
    /// <param name="buildingType">建筑类型</param>
    /// <param name="projectManager">项目经理姓名</param>
    /// <param name="buildingManager">建筑负责人姓名</param>
    /// <param name="designer">设计人员姓名</param>
    /// <returns>年份根节点</returns>
    public static TreeNodeData CreateDWGProjectStructureTemplate(
        string year,
        string projectName,
        string[] buildingNames,
        string buildingType = "住宅",
        string projectManager = "",
        string buildingManager = "",
        string designer = "")
    {
        // 创建年份节点（预算由子项目汇总）
        var yearNode = CreateDWGYearTemplate(year);

        // 创建项目节点
        var projectNode = CreateDWGProjectTemplate(projectName, yearNode.Id, projectManager);
        projectNode.SetParentNode(yearNode);

        // 创建栋号节点
        foreach (var buildingName in buildingNames)
        {
            var buildingNode = CreateDWGBuildingTemplate(buildingName, projectNode.Id, buildingType, 0, 0, "规划中",
                buildingManager, designer);
            buildingNode.SetParentNode(projectNode);
        }

        return yearNode;
    }

    /// <summary>
    /// 创建示例数据结构模板
    /// </summary>
    /// <returns>示例年份节点</returns>
    public static TreeNodeData CreateDWGSampleDataTemplate()
    {
        return CreateDWGProjectStructureTemplate(
            DateTime.Now.Year.ToString(),
            "示例商业项目",
            new[] { "A栋", "B栋", "C栋" },
            "商业"
        );
    }

    /// <summary>
    /// 设置父子关系的扩展方法
    /// </summary>
    /// <param name="child">子节点</param>
    /// <param name="parent">父节点</param>
    /// <returns>子节点本身，支持链式调用</returns>
    public static TreeNodeData SetParentNode(this TreeNodeData child, TreeNodeData parent)
    {
        child.Parent = parent;
        child.ParentId = parent.Id;
        child.Level = parent.Level + 1;
        parent.Children.Add(child);
        return child;
    }

    #endregion

    #region DWG人员管理扩展

    /// <summary>
    /// 获取节点关联的负责人姓名
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>负责人姓名</returns>
    public static string GetDWGManager(this TreeNodeData node)
    {
        return node.GetDWGData()?.Manager ?? string.Empty;
    }

    /// <summary>
    /// 设置节点关联的负责人姓名
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="manager">负责人姓名</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGManager(this TreeNodeData node, string manager)
    {
        node.EnsureDWGData().Manager = manager;
        return node;
    }

    /// <summary>
    /// 获取节点关联的设计人员姓名
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>设计人员姓名</returns>
    public static string GetDWGDesigner(this TreeNodeData node)
    {
        return node.GetDWGData()?.Designer ?? string.Empty;
    }

    /// <summary>
    /// 设置节点关联的设计人员姓名
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="designer">设计人员姓名</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGDesigner(this TreeNodeData node, string designer)
    {
        node.EnsureDWGData().Designer = designer;
        return node;
    }

    /// <summary>
    /// 获取节点所属的项目ID（向上查找项目节点）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>项目节点ID</returns>
    public static int? GetDWGProjectId(this TreeNodeData node)
    {
        var current = node;
        while (current != null)
        {
            if (current.IsDWGProjectNode())
            {
                return current.Id;
            }

            current = current.Parent;
        }

        return null;
    }

    /// <summary>
    /// 检查节点是否属于指定项目
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="projectId">项目ID</param>
    /// <returns>是否属于指定项目</returns>
    public static bool BelongsToProject(this TreeNodeData node, int projectId)
    {
        var nodeProjectId = node.GetDWGProjectId();
        return nodeProjectId.HasValue && nodeProjectId.Value == projectId;
    }

    #endregion

    #region DWG设计管理扩展

    /// <summary>
    /// 获取设计阶段
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>设计阶段</returns>
    public static string GetDWGDesignStage(this TreeNodeData node)
    {
        return node.GetDWGData()?.DesignStage ?? string.Empty;
    }

    /// <summary>
    /// 设置设计阶段
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="designStage">设计阶段</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGDesignStage(this TreeNodeData node, string designStage)
    {
        node.EnsureDWGData().DesignStage = designStage;
        return node;
    }

    /// <summary>
    /// 获取设计进度
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>设计进度百分比</returns>
    public static double GetDWGDesignProgress(this TreeNodeData node)
    {
        return node.GetDWGData()?.DesignProgress ?? 0;
    }

    /// <summary>
    /// 设置设计进度
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="designProgress">设计进度百分比</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGDesignProgress(this TreeNodeData node, double designProgress)
    {
        node.EnsureDWGData().DesignProgress = designProgress;
        return node;
    }

    /// <summary>
    /// 获取项目状态
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>项目状态</returns>
    public static string GetDWGProjectStatus(this TreeNodeData node)
    {
        return node.GetDWGData()?.ProjectStatus ?? string.Empty;
    }

    /// <summary>
    /// 设置项目状态
    /// </summary>
    /// <param name="node">树节点</param>
    /// <param name="projectStatus">项目状态</param>
    /// <returns>树节点本身，支持链式调用</returns>
    public static TreeNodeData SetDWGProjectStatus(this TreeNodeData node, string projectStatus)
    {
        node.EnsureDWGData().ProjectStatus = projectStatus;
        return node;
    }

    #endregion
}
// ==================== 备份代码结束 ====================
#endif
