using System;
using System.Collections.Generic;
using System.Linq;
using AlphaPM.Models.DWG;
using Zylo.WPF.Controls.TreeView;
using Wpf.Ui.Controls;

namespace AlphaPM.Extensions;

/// <summary>
/// TreeNodeData扩展方法 - AlphaPM专用扩展
/// </summary>
public static class TreeNodeDataExtensions
{
    #region 节点类型判断

    /// <summary>
    /// 判断是否为年份节点
    /// </summary>
    public static bool IsYearNode(this TreeNodeData node)
    {
        return node.NodeType == DWGNodeTypes.Year;
    }

    /// <summary>
    /// 判断是否为项目节点
    /// </summary>
    public static bool IsProjectNode(this TreeNodeData node)
    {
        return node.NodeType == DWGNodeTypes.Project;
    }

    /// <summary>
    /// 判断是否为栋号节点
    /// </summary>
    public static bool IsBuildingNode(this TreeNodeData node)
    {
        return node.NodeType == DWGNodeTypes.Building;
    }

    #endregion

    #region 节点创建工厂方法

    /// <summary>
    /// 创建年份节点
    /// </summary>
    public static TreeNodeData CreateYear(string yearName, string? manager = null)
    {
        return new TreeNodeData(yearName)
        {
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            Manager = manager,
            Description = $"{yearName}年度项目管理",
            WpfUiSymbol = SymbolRegular.CalendarLtr24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };
    }

    /// <summary>
    /// 创建项目节点
    /// </summary>
    public static TreeNodeData CreateProject(string projectName, int parentId,
        string? manager = null, decimal budget = 0, DateTime? startDate = null, DateTime? endDate = null)
    {
        return new TreeNodeData(projectName)
        {
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            ParentId = parentId,
            Manager = manager,
            Budget = budget,
            StartDate = startDate,
            EndDate = endDate,
            ProjectStatus = "规划中",
            WpfUiSymbol = SymbolRegular.BuildingBank24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };
    }

    #endregion

    #region 节点类型显示

    /// <summary>
    /// 获取节点类型的显示名称
    /// </summary>
    public static string GetDisplayName(this TreeNodeData node)
    {
        return node.NodeType switch
        {
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Building => "建筑",
            _ => "节点"
        };
    }

    /// <summary>
    /// 获取节点类型的显示名称（静态方法）
    /// </summary>
    public static string GetDisplayName(string nodeType)
    {
        return nodeType?.ToLower() switch
        {
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Building => "建筑",
            _ => "节点"
        };
    }

    #endregion

    #region 业务计算

    /// <summary>
    /// 计算年份节点的总预算
    /// </summary>
    public static decimal CalculateYearTotalBudget(this TreeNodeData yearNode)
    {
        if (!yearNode.IsYearNode()) return 0;

        return yearNode.YChildren
            .OfType<TreeNodeData>()
            .Where(p => p.IsProjectNode())
            .Sum(p => p.Budget);
    }

    #endregion

    #region 状态描述

    /// <summary>
    /// 获取节点的状态描述
    /// </summary>
    public static string GetStatusDescription(this TreeNodeData node)
    {
        return node.NodeType switch
        {
            DWGNodeTypes.Year => $"年度项目 | 总预算: {node.CalculateYearTotalBudget():N0}万元 | 项目数: {node.YChildren.Count} | 负责人: {node.Manager}",
            DWGNodeTypes.Project => $"项目进度: {node.ProgressPercentage:F1}% | 预算: {node.Budget:N0}万元 | 经理: {node.Manager} | 状态: {node.ProjectStatus}",
            DWGNodeTypes.Building => $"{node.BuildingType} | {node.BuildingArea:N1}㎡ | {node.FloorCount}层 | {node.BuildingStatus} | 负责人: {node.Manager} | 设计师: {node.Designer}",
            _ => node.Description ?? ""
        };
    }

    #endregion

    #region 路径构建

    /// <summary>
    /// 构建节点的完整路径
    /// </summary>
    public static string BuildNodePath(this TreeNodeData node)
    {
        if (node == null) return string.Empty;

        var pathParts = new List<string>();
        var current = node;

        while (current != null)
        {
            pathParts.Insert(0, current.Name);
            current = current.YParent as TreeNodeData;
        }

        return "/" + string.Join("/", pathParts);
    }

    #endregion

    #region 父子关系操作

    /// <summary>
    /// 设置父子关系
    /// </summary>
    public static TreeNodeData SetParent(this TreeNodeData child, TreeNodeData parent)
    {
        child.YParent = parent;
        child.ParentId = parent.Id;
        child.Level = parent.Level + 1;

        // 确保父节点的子集合包含此节点
        if (!parent.YChildren.Contains(child))
        {
            parent.YChildren.Add(child);
        }

        return child;
    }

    /// <summary>
    /// 添加子节点
    /// </summary>
    public static TreeNodeData AddChild(this TreeNodeData parent, TreeNodeData child)
    {
        child.SetParent(parent);
        return parent;
    }

    /// <summary>
    /// 获取强类型的父节点
    /// </summary>
    public static TreeNodeData? GetTypedParent(this TreeNodeData node)
    {
        return node.YParent as TreeNodeData;
    }

    /// <summary>
    /// 获取强类型的子节点集合
    /// </summary>
    public static IEnumerable<TreeNodeData> GetTypedChildren(this TreeNodeData node)
    {
        return node.YChildren.OfType<TreeNodeData>();
    }

    /// <summary>
    /// 获取所有后代节点（递归）
    /// </summary>
    public static IEnumerable<TreeNodeData> GetAllDescendants(this TreeNodeData node)
    {
        var descendants = new List<TreeNodeData>();

        foreach (var child in node.GetTypedChildren())
        {
            descendants.Add(child);
            descendants.AddRange(child.GetAllDescendants());
        }

        return descendants;
    }

    /// <summary>
    /// 获取所有祖先节点（向上遍历）
    /// </summary>
    public static IEnumerable<TreeNodeData> GetAllAncestors(this TreeNodeData node)
    {
        var ancestors = new List<TreeNodeData>();
        var current = node.GetTypedParent();

        while (current != null)
        {
            ancestors.Add(current);
            current = current.GetTypedParent();
        }

        return ancestors;
    }

    #endregion

    #region DWG节点类型判断

    /// <summary>
    /// 判断是否为DWG年份节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为年份节点</returns>
    public static bool IsDWGYearNode(this TreeNodeData node)
    {
        return node.Level == 1 ||
               node.NodeType?.ToLower() == DWGNodeTypes.Year ||
               (node.GetTypedParent() == null && int.TryParse(node.Name, out _));
    }

    /// <summary>
    /// 判断是否为DWG项目节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为项目节点</returns>
    public static bool IsDWGProjectNode(this TreeNodeData node)
    {
        return node.Level == 2 ||
               node.NodeType?.ToLower() == DWGNodeTypes.Project ||
               (node.GetTypedParent() != null && node.GetTypedParent()!.IsDWGYearNode());
    }

    /// <summary>
    /// 判断是否为DWG建筑节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为建筑节点</returns>
    public static bool IsDWGBuildingNode(this TreeNodeData node)
    {
        return node.Level == 3 ||
               node.NodeType?.ToLower() == DWGNodeTypes.Building ||
               (node.GetTypedParent() != null && node.GetTypedParent()!.IsDWGProjectNode());
    }

    /// <summary>
    /// 判断是否为文件夹类型节点（年份或项目）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否为文件夹类型</returns>
    public static bool IsDWGFolderNode(this TreeNodeData node)
    {
        return node.IsDWGYearNode() || node.IsDWGProjectNode() ||
               node.NodeType?.ToLower() == DWGNodeTypes.Year;
    }

    #endregion

    #region DWG节点层级判断

    /// <summary>
    /// 获取DWG节点层级（智能判断）
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>节点层级（1=年份，2=项目，3=栋号）</returns>
    public static int GetDWGNodeLevel(this TreeNodeData node)
    {
        // 优先使用Level属性
        if (node.Level >= 1 && node.Level <= 3)
            return node.Level;

        // 根据父子关系判断
        if (node.GetTypedParent() == null) return 1; // 顶层年份
        if (node.GetTypedParent()?.GetTypedParent() == null) return 2; // 二级项目
        return 3; // 三级栋号
    }

    /// <summary>
    /// 获取DWG节点层级的显示名称
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>层级显示名称</returns>
    public static string GetDWGNodeLevelName(this TreeNodeData node)
    {
        return node.NodeType?.ToLower() switch
        {
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Building => "建筑",
            _ => node.GetDWGNodeLevel() switch
            {
                1 => "年份",
                2 => "项目",
                3 => "建筑",
                _ => "未知"
            }
        };
    }

    /// <summary>
    /// 判断DWG节点是否可以添加子节点
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>是否可以添加子节点</returns>
    public static bool CanAddDWGChild(this TreeNodeData node)
    {
        return node.GetDWGNodeLevel() switch
        {
            1 => true, // 年份节点可以添加项目
            2 => true, // 项目节点可以添加栋号
            3 => false, // 栋号节点不能添加子节点
            _ => false
        };
    }

    /// <summary>
    /// 获取DWG节点可以添加的子节点类型名称
    /// </summary>
    /// <param name="node">树节点</param>
    /// <returns>子节点类型名称</returns>
    public static string GetDWGChildNodeTypeName(this TreeNodeData node)
    {
        return node.GetDWGNodeLevel() switch
        {
            1 => "项目",
            2 => "栋号",
            _ => "无"
        };
    }

    #endregion

    #region 业务逻辑扩展

    /// <summary>
    /// 计算项目节点的总预算（包括所有栋号）
    /// </summary>
    public static decimal CalculateProjectTotalBudget(this TreeNodeData projectNode)
    {
        if (!projectNode.IsProjectNode()) return projectNode.Budget;

        var buildingBudgets = projectNode.GetTypedChildren()
            .Where(b => b.IsBuildingNode())
            .Sum(b => b.Budget);

        return projectNode.Budget + buildingBudgets;
    }

    /// <summary>
    /// 获取年份节点下的所有项目
    /// </summary>
    public static IEnumerable<TreeNodeData> GetAllProjects(this TreeNodeData yearNode)
    {
        if (!yearNode.IsYearNode()) return Enumerable.Empty<TreeNodeData>();

        return yearNode.GetTypedChildren().Where(p => p.IsProjectNode());
    }

    /// <summary>
    /// 获取项目节点下的所有栋号
    /// </summary>
    public static IEnumerable<TreeNodeData> GetAllBuildings(this TreeNodeData projectNode)
    {
        if (!projectNode.IsProjectNode()) return Enumerable.Empty<TreeNodeData>();

        return projectNode.GetTypedChildren().Where(b => b.IsBuildingNode());
    }

    #endregion
}
