using System;
using System.Collections.Generic;
using System.Linq;
using AlphaPM.Models.DWG;
using Zylo.WPF.Controls.TreeView;
using Wpf.Ui.Controls;

namespace AlphaPM.Extensions;

/// <summary>
/// TreeNodeData扩展方法 - AlphaPM专用扩展
/// </summary>
public static class TreeNodeDataExtensions
{
    #region 节点类型判断

    /// <summary>
    /// 判断是否为年份节点
    /// </summary>
    public static bool IsYearNode(this TreeNodeData node)
    {
        return node.NodeType == DWGNodeTypes.Year;
    }

    /// <summary>
    /// 判断是否为项目节点
    /// </summary>
    public static bool IsProjectNode(this TreeNodeData node)
    {
        return node.NodeType == DWGNodeTypes.Project;
    }

    /// <summary>
    /// 判断是否为栋号节点
    /// </summary>
    public static bool IsBuildingNode(this TreeNodeData node)
    {
        return node.NodeType == DWGNodeTypes.Building;
    }

    #endregion

    #region 节点创建工厂方法

    /// <summary>
    /// 创建年份节点
    /// </summary>
    public static TreeNodeData CreateYear(string yearName, string? manager = null)
    {
        return new TreeNodeData(yearName)
        {
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            Manager = manager,
            Description = $"{yearName}年度项目管理",
            WpfUiSymbol = SymbolRegular.CalendarLtr24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };
    }

    /// <summary>
    /// 创建项目节点
    /// </summary>
    public static TreeNodeData CreateProject(string projectName, int parentId,
        string? manager = null, decimal budget = 0, DateTime? startDate = null, DateTime? endDate = null)
    {
        return new TreeNodeData(projectName)
        {
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            ParentId = parentId,
            Manager = manager,
            Budget = budget,
            StartDate = startDate,
            EndDate = endDate,
            ProjectStatus = "规划中",
            WpfUiSymbol = SymbolRegular.BuildingBank24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };
    }

    #endregion

    #region 节点类型显示

    /// <summary>
    /// 获取节点类型的显示名称
    /// </summary>
    public static string GetDisplayName(this TreeNodeData node)
    {
        return node.NodeType switch
        {
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Building => "建筑",
            _ => "节点"
        };
    }

    /// <summary>
    /// 获取节点类型的显示名称（静态方法）
    /// </summary>
    public static string GetDisplayName(string nodeType)
    {
        return nodeType?.ToLower() switch
        {
            DWGNodeTypes.Year => "年份",
            DWGNodeTypes.Project => "项目",
            DWGNodeTypes.Building => "建筑",
            _ => "节点"
        };
    }

    #endregion

    #region 业务计算

    /// <summary>
    /// 计算年份节点的总预算
    /// </summary>
    public static decimal CalculateYearTotalBudget(this TreeNodeData yearNode)
    {
        if (!yearNode.IsYearNode()) return 0;

        return yearNode.Children
            .Where(p => p.IsProjectNode())
            .Sum(p => p.Budget);
    }

    #endregion

    #region 状态描述

    /// <summary>
    /// 获取节点的状态描述
    /// </summary>
    public static string GetStatusDescription(this TreeNodeData node)
    {
        return node.NodeType switch
        {
            DWGNodeTypes.Year => $"年度项目 | 总预算: {node.CalculateYearTotalBudget():N0}万元 | 项目数: {node.Children.Count} | 负责人: {node.Manager}",
            DWGNodeTypes.Project => $"项目进度: {node.ProgressPercentage:F1}% | 预算: {node.Budget:N0}万元 | 经理: {node.Manager} | 状态: {node.ProjectStatus}",
            DWGNodeTypes.Building => $"{node.BuildingType} | {node.BuildingArea:N1}㎡ | {node.FloorCount}层 | {node.BuildingStatus} | 负责人: {node.Manager} | 设计师: {node.Designer}",
            _ => node.Description ?? ""
        };
    }

    #endregion

    #region 路径构建

    /// <summary>
    /// 构建节点的完整路径
    /// </summary>
    public static string BuildNodePath(this TreeNodeData node)
    {
        if (node == null) return string.Empty;

        var pathParts = new List<string>();
        var current = node;

        while (current != null)
        {
            pathParts.Insert(0, current.Name);
            current = current.Parent;
        }

        return "/" + string.Join("/", pathParts);
    }

    #endregion
}
