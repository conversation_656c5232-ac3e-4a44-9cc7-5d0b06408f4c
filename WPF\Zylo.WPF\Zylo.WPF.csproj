﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0-windows</TargetFramework>
        <LangVersion>preview</LangVersion>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <AssemblyTitle>Zylo.MVVM Universal Framework</AssemblyTitle>
        <AssemblyDescription>Universal MVVM framework with pluggable architecture - call the strongest features from best libraries</AssemblyDescription>
        <PackageId>Zylo.MVVM.Core</PackageId>
        <Version>1.0.0-alpha.1</Version>
        <Authors>Zylo Team</Authors>
        <PackageTags>MVVM;WPF;Universal;Framework;Pluggable;CommunityToolkit;Prism</PackageTags>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    </PropertyGroup>

    <ItemGroup>
        <!-- 🚀 终极双核心包整合：选择最强功能，避免重复 -->

        <!-- CommunityToolkit.Mvvm - 现代化 MVVM 基础设施 (最强) -->
        <!-- ✅ 源生成器：[ObservableProperty], [RelayCommand] 零运行时开销 -->
        <!-- ✅ 高性能 Messenger：RequestMessage, AsyncMessage (比 Prism EventAggregator 更强) -->
        <!-- ✅ ObservableObject：替代 Prism.BindableBase，性能更优 -->
        <!-- ✅ AsyncRelayCommand：原生异步命令支持 -->
        <!-- ✅ ObservableValidator：内置数据验证 -->
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />


        <!-- 🔧 容器支持 - 支持多种 DI 容器，用户可选择 -->

        <!-- 🔍 程序集扫描和反射支持 -->
        <PackageReference Include="System.Reflection.Extensions" Version="4.3.0" />
        <PackageReference Include="Prism.Wpf" Version="8.1.97" />
        <PackageReference Include="Prism.DryIoc" Version="8.1.97" />
        <PackageReference Include="WPF-UI" Version="4.0.3" />
        <PackageReference Include="AvalonEdit" Version="********" />

        <!-- 拖拽功能 -->
        <PackageReference Include="gong-wpf-dragdrop" Version="4.0.0" />

        <!-- 压缩解压功能 - 支持中文文件名和多种格式 -->
        <PackageReference Include="SharpZipLib" Version="1.4.2" />

    </ItemGroup>

    <!-- WPF support for ViewDiscovery -->
    <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
        <FrameworkReference Include="Microsoft.WindowsDesktop.App" />
    </ItemGroup>
    <ItemGroup>
      <Folder Include="Comman\" />
      <Folder Include="Controls\NotificationControls\" />
      <Folder Include="Handlers\" />
      <Folder Include="Themes\" />
      <Folder Include="Views\" />
    </ItemGroup>
    <ItemGroup>
      <Resource Include="Assets\Font\iconfont.ttf" />
    </ItemGroup>
    <ItemGroup>
      <Page Update="Themes\Themes\Generic.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Controls\Navigation\NavigationControl.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Resources\Navigation\NavigationControlStyles.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Resources\Navigation\NavigationControlTemplates.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Themes\Controls\ZyloIcon.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Resources\ListView\ListViewStyles.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Controls\YFile\FileExplorerControl.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
      <Page Update="Resources\CardExpander\CardExpanderStyles.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Windows\CustomDialogWindow.xaml">
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\Zylo.Core\Zylo.All\Zylo.All.csproj" />
    </ItemGroup>
 


</Project>
