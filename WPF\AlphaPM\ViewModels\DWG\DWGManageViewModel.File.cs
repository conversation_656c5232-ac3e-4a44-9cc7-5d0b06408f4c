﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using AlphaPM.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Prism.Services.Dialogs;

using Zylo.WPF.Controls;
using Zylo.YLog.Runtime;
using AlphaPM.Models;
using AlphaPM.Services.DWG;
using Wpf.Ui.Controls;
using MessageBox = System.Windows.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// DWG管理视图模型 - 文件管理相关功能
/// </summary>
public partial class DWGManageViewModel
{
    /// <summary>
    /// 项目路经
    /// </summary>
    [ObservableProperty]
    [Description("项目路经")]
    public partial string ProjectPasses { get; set; } = YDataModel.Current.YdwgData.ProjectPath;


    /// <summary>
    /// 模型路经
    /// </summary>
    [ObservableProperty]
    [Description("模型路经")]
    public partial string ModelPath { get; set; }= YDataModel.Current.YdwgData.ProjectPath;

    #region FileTypeItemModel 相关命令

    /// <summary>
    /// 管理文件类型命令 - 弹出 FileTypeItemManagerView 对话框
    /// 只有选中栋号级别（第三层）的节点时才可用
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedBuildingNode))]
    private void ManageFileTypes()
    {
        try
        {
            var parameters = new DialogParameters();

            // 如果有选中的节点，传递给对话框
            if (SelectedNode != null)
            {
                parameters.Add("SelectedTreeNode", SelectedNode);
                parameters.Add("TreeNodeId", SelectedNode.Id);
                parameters.Add("TreeNodeName", SelectedNode.Name);
                _logger.Info($"传递选中节点到文件类型管理: {SelectedNode.Name} (ID: {SelectedNode.Id})");
            }

            // 设置对话框标题和尺寸
            parameters.Add("Title", "文件类型管理");
            // parameters.Add("Width", 1200);
            // parameters.Add("Height", 1200);
            // parameters.Add("MinWidth", 800);
            // parameters.Add("MinHeight", 1200);

            _dialogService.ShowDialog("FileTypeItemManagerView", parameters, result =>
            {
                if (result.Result == ButtonResult.OK)
                {
                    StatusMessage = "文件类型管理完成";
                    _logger.Info("文件类型管理对话框关闭 - 确认");
                }
                else
                {
                    StatusMessage = "取消文件类型管理";
                    _logger.Info("文件类型管理对话框关闭 - 取消");
                }
            });
        }
        catch (Exception ex)
        {
            StatusMessage = $"打开文件类型管理失败: {ex.Message}";
            _logger.Error($"打开文件类型管理失败: {ex}");
        }
    }

 

    #endregion
}