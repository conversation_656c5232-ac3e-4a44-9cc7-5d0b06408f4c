﻿using System.ComponentModel;
using System.Text.Json;
using AlphaPM.Hellper;
using AlphaPM.Services.Window;
using Google.Protobuf;
using Zylo.YIO.Core;

namespace AlphaPM.Data;

public partial class YDWGData : ObservableObject
{
    #region 路径属性 - 使用 ObservableProperty

    YSelectWindow ySelectWindow = new YSelectWindow();


    private static string _DefauntappPath = Path.Combine(YCommonPaths.ApplicationDataDirectory, "Json");

    /// <summary>
    /// 项目路径
    /// </summary>
    [ObservableProperty]
    [Description("项目路径")]
    public partial string ProjectPath { get; set; } = Path.Combine(_DefauntappPath, "项目");

    /// <summary>
    /// 模型路径
    /// </summary>
    [ObservableProperty]
    [Description("模型路径")]
    public partial string ModelPath { get; set; } = Path.Combine(_DefauntappPath, "模型");

    /// <summary>
    /// 工作区路径
    /// </summary>
    [ObservableProperty]
    [Description("工作区路径")]
    public partial string WorkspacePath { get; set; } = string.Empty;

    /// <summary>
    /// 模板路径
    /// </summary>
    [ObservableProperty]
    [Description("模板路径")]
    public partial string TemplatePath { get; set; } = string.Empty;

    /// <summary>
    /// 导出路径
    /// </summary>
    [ObservableProperty]
    [Description("导出路径")]
    public partial string ExportPath { get; set; } = string.Empty;

    /// <summary>
    /// 备份路径
    /// </summary>
    [ObservableProperty]
    [Description("备份路径")]
    public partial string BackupPath { get; set; } = Path.Combine(_DefauntappPath, "备份");

    #endregion

    #region 路径验证

    /// <summary>
    /// 验证所有路径的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult ValidatePaths()
    {
        var result = new ValidationResult();

        // 验证项目路径
        ValidatePath(ProjectPath, "项目路径", result, isDirectory: true);

        // 验证模型路径
        ValidatePath(ModelPath, "模型路径", result, isDirectory: false);

        // 验证工作区路径
        ValidatePath(WorkspacePath, "工作区路径", result, isDirectory: true);

        // 验证模板路径
        ValidatePath(TemplatePath, "模板路径", result, isDirectory: true);

        // 验证导出路径
        ValidatePath(ExportPath, "导出路径", result, isDirectory: true);

        // 验证备份路径
        ValidatePath(BackupPath, "备份路径", result, isDirectory: true);

        return result;
    }

    /// <summary>
    /// 验证单个路径
    /// </summary>
    private void ValidatePath(string path, string pathName, ValidationResult result, bool isDirectory)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            result.Warnings.Add($"{pathName}为空");
            return;
        }

        try
        {
            if (isDirectory)
            {
                if (!Directory.Exists(path))
                {
                    result.Errors.Add($"{pathName}不存在: {path}");
                }
            }
            else
            {
                if (!File.Exists(path))
                {
                    result.Errors.Add($"{pathName}不存在: {path}");
                }
            }
        }
        catch (Exception ex)
        {
            result.Errors.Add($"{pathName}验证失败: {ex.Message}");
        }
    }

    #endregion

    /// <summary>
    /// 确保目录存在
    /// </summary>
    public void EnsureDirectoriesExist()
    {
        var paths = new[] { ProjectPath, WorkspacePath, TemplatePath, ExportPath, BackupPath };
        foreach (var path in paths)
        {
            if (!string.IsNullOrWhiteSpace(path) && !Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch
                {
                    // 忽略创建失败的情况
                }
            }
        }
    }

    #region 验证结果类

    /// <summary>
    /// 路径验证结果
    /// </summary>
    public record ValidationResult
    {
        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; init; } = new();

        /// <summary>
        /// 警告列表
        /// </summary>
        public List<string> Warnings { get; init; } = new();

        /// <summary>
        /// 是否验证通过（无错误）
        /// </summary>
        public bool IsValid => Errors.Count == 0;

        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => Warnings.Count > 0;
    }

    #endregion

    #region 文件夹

    // /// <summary>
    // /// 选择项目路径
    // /// </summary>
    // public bool SelectProjectPath(string title = "选择项目路径")
    //     => ySelectWindow.SelectFolder(nameof(ProjectPath), title, ProjectPath);
    //
    // /// <summary>
    // /// 选择工作空间路径
    // /// </summary>
    // public bool SelectWorkspacePath(string title = "选择工作空间路径")
    //     => ySelectWindow.SelectFolder(nameof(WorkspacePath), title, WorkspacePath);

    /// <summary>
    /// 选择模型文件
    /// </summary>
    public bool SelectModelPath(string title = "选择模型文件")
        => ySelectWindow.SelectFile(nameof(ModelPath), title, "DWG文件|*.dwg|DXF文件|*.dxf|所有文件|*.*",
            string.IsNullOrEmpty(ProjectPath) ? null : ProjectPath);

    // /// <summary>
    // /// 通用路径选择方法 - 最简单的调用方式
    // /// </summary>
    // /// <param name="pathType">路径类型：project, model, workspace</param>
    // /// <param name="title">对话框标题（可选）</param>
    // /// <returns>是否选择成功</returns>
    // public bool SelectPath(string pathType, string? title = null)
    // {
    //     return pathType.ToLower() switch
    //     {
    //         "project" => SelectProjectPath(title ?? "选择项目路径"),
    //         "model" => SelectModelPath(title ?? "选择模型文件"),
    //         "workspace" => SelectWorkspacePath(title ?? "选择工作空间路径"),
    //         _ => throw new ArgumentException($"不支持的路径类型: {pathType}")
    //     };
    // }


    /// <summary>
    /// 在资源管理器中打开项目路径
    /// </summary>
    /// <returns>是否成功打开</returns>
    public bool OpenProjectInExplorer()
    {
        if (string.IsNullOrEmpty(ProjectPath) || !Directory.Exists(ProjectPath))
            return false;

        var result = WindowsFileOperationService.OpenFolderInExplorer(ProjectPath);
        return result.Success;
    }

    /// <summary>
    /// 在资源管理器中打开工作空间路径
    /// </summary>
    /// <returns>是否成功打开</returns>
    public bool OpenWorkspaceInExplorer()
    {
        if (string.IsNullOrEmpty(WorkspacePath) || !Directory.Exists(WorkspacePath))
            return false;

        var result = WindowsFileOperationService.OpenFolderInExplorer(WorkspacePath);
        return result.Success;
    }

    #endregion
}