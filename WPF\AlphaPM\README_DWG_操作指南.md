# DWG项目管理 - 操作指南

## 🎯 功能概述

DWG项目管理模块提供了完整的三级层次结构管理功能，支持动态添加、编辑和删除项目数据。

## 📊 层次结构

### 三级层次设计
```
📅 年份 (2017-2025)
├── 🏢 项目名 (万科城市花园、恒大翡翠华庭等)
│   ├── 🏗️ 1#楼
│   ├── 🏗️ 2#楼
│   └── 🏗️ A栋
└── 🏢 其他项目...
```

## 🔧 操作功能

### 1. 添加年份
- **位置**：标题栏右侧的 ➕ 按钮
- **功能**：添加新的年份节点（默认为当前年份+1）
- **图标**：📅 日历图标
- **限制**：不能添加重复的年份

### 2. 右键菜单操作

#### 选中年份节点时：
- **添加项目**：为该年份添加新项目
- **编辑年份**：修改年份信息（待实现）
- **删除年份**：删除整个年份及其所有项目

#### 选中项目节点时：
- **添加栋号**：为该项目添加新栋号
- **编辑项目**：修改项目信息（待实现）
- **删除项目**：删除整个项目及其所有栋号

#### 选中栋号节点时：
- **编辑栋号**：修改栋号信息（待实现）
- **删除栋号**：删除该栋号
- **查看详情**：显示栋号相关的DWG文件和图纸

## 🎨 图标说明

| 层级 | 图标 | 说明 |
|------|------|------|
| 年份 | 📅 | 日历图标，表示时间维度 |
| 项目 | 🏢 | 建筑图标，表示项目/楼盘 |
| 栋号 | 🏗️ | 建设图标，表示具体建筑 |

## 📝 使用步骤

### 添加新年份
1. 点击标题栏右侧的 ➕ 按钮
2. 系统自动创建下一年的年份节点
3. 状态栏显示添加结果

### 添加项目
1. 右键点击年份节点
2. 选择"添加子节点"
3. 系统自动创建"新项目X"节点
4. 可以通过编辑功能修改项目名称

### 添加栋号
1. 右键点击项目节点
2. 选择"添加子节点"
3. 系统自动创建"X#楼"节点
4. 可以通过编辑功能修改栋号名称

### 删除节点
1. 右键点击要删除的节点
2. 选择"删除节点"
3. 系统会删除该节点及其所有子节点
4. 状态栏显示删除结果

## 🔍 搜索功能

- **实时搜索**：在搜索框中输入关键词
- **搜索范围**：年份、项目名、栋号名称
- **搜索提示**：🔍 搜索专业分类...

## 📊 状态反馈

### 选择反馈
- **年份**：显示"选择年份: 2024 (3个项目)"
- **项目**：显示"选择项目: 万科城市花园 (4栋建筑)"
- **栋号**：显示"选择栋号: 1#楼 - 高层住宅，32层"

### 操作反馈
- **添加**：显示添加成功信息
- **删除**：显示删除成功信息
- **错误**：显示错误原因（如重复添加）

## 🚀 扩展功能

### 已实现
- ✅ 三级层次结构
- ✅ 动态添加年份
- ✅ 右键菜单操作
- ✅ 节点删除功能
- ✅ 实时搜索过滤
- ✅ 状态反馈

### 待实现
- 🔄 节点编辑功能
- 🔄 DWG文件管理
- 🔄 图纸分类显示
- 🔄 文件版本控制
- 🔄 权限管理
- 🔄 数据持久化

## 💡 使用技巧

1. **快速导航**：使用搜索功能快速定位项目
2. **批量操作**：先添加年份，再批量添加项目
3. **层次管理**：合理规划项目层次，便于管理
4. **状态监控**：关注状态栏信息，了解操作结果

## 🔧 技术特性

- **MVVM架构**：使用CommunityToolkit.MVVM
- **依赖属性**：支持数据双向绑定
- **命令模式**：RelayCommand处理用户操作
- **虚拟化**：支持大数据集性能优化
- **类型安全**：强类型节点管理

## 📞 问题反馈

如果在使用过程中遇到问题，请检查：
1. 状态栏是否有错误信息
2. 节点类型是否正确
3. 操作权限是否足够
4. 数据是否已保存

---

*最后更新：2025年8月*
