
#if false


using Bin.Shared.Map;
using GongSolutions.Wpf.DragDrop;
using Prism.Events;
using Prism.Services.Dialogs;
using iNKORE.UI.WPF.Modern.Controls;
using System.Windows;
using System.Collections.ObjectModel;
using System.Drawing;
using YBJ.Core.DB;
using Zylo.Views;
using Zylo.Views.DWG;
using Zylo.WPF.Comman.IconKeys;
using Zylo.WPF.Services;
using Zylo.WPF.YPrism;
using TreeViewData = Zylo.WPF.Models.YTreeView.TreeViewData;


namespace Zylo.ViewModels.DWG
{
    public partial class DWGViewModel : ViewModelBase
    {
        private readonly IDialogService _dialogService;
        private readonly INotificationService _notificationService;

        public DWGViewModel(IEventAggregator ieventAggregator, IDBHelper idbHelper, IDialogService dialogService, 
            INotificationService notificationService)
        {
            DbTreeViewData = new(idbHelper);
            _ieventAggregator = ieventAggregator;
            _dialogService = dialogService;
            _notificationService = notificationService;

            TreeItems = new(); // region 树菜单选中
            TreeItemT = new(); // region 树菜单
            _expandedStates = AppSetData.Instance.TreeViewMenuExpandedStates;
            TreeViewLoadData(); // 加载树菜单
        }

        public void SetView(FrameworkElement view)
        {
            _notificationService.SetNotificationTarget(view);
        }

        #region 树菜单

        /// <summary>
        /// 树绑定的所有数据
        /// </summary>
        [ObservableProperty]
        public partial ObservableCollection<TreeViewData> TreeItems { get; set; }


        /// <summary>
        /// 树选中菜单
        /// </summary>
        [ObservableProperty]
        public partial TreeViewData TreeItemT { get; set; }

        // 保存的展开状态字典
        /// <summary>
        /// 用于保存当前所有树节点的展开状态的字典。
        /// 键为节点的名称，值为节点是否展开的布尔值。
        /// </summary>
        private Dictionary<string, bool> _expandedStates;

        /// <summary>
        /// 用于存储上一次保存的树节点展开状态的字典。
        /// 此成员目前未被读取使用，可考虑删除。
        /// </summary>
        private Dictionary<string, bool> _expandedStatesOld;

        /// <summary>
        /// 查找所有数据
        /// </summary>

        private List<TreeViewData> SharingTreeItemAll = new List<TreeViewData>();



        /// <summary>
        /// 加载树菜单 
        /// </summary>
        private void TreeViewLoadData()
        {
            TreeItems = new ObservableCollection<TreeViewData>();
            TreeItemT = new TreeViewData();


            ObservableCollection<TreeViewData> result = new(); // 结果集合

            var listFO = DbTreeViewData.TreeViewBuildTreeDefault();


            SharingTreeItemAll = listFO;


            var children = listFO.Where(a => a.SonID == 0).OrderBy(a => a.Name).ToList();
            foreach (var child in children)
            {
                result.Add(TreeViewBuildTree(child, listFO));
            }
            TreeItems = result;
        }

        /// <summary>
        /// 树菜单生成一个测试节点
        /// </summary>
        /// <returns></returns>
        private TreeViewData TreeItemCreateTest()
        {
            var newItem = new TreeViewData
            {
                Name = "测试点",
                IsExpanded = false,
                FointIcon = YIcons.Notes,
                SonID = 0,
                Id = 1
            };
            return newItem;
        }


        /// <summary>
        /// 构件树
        /// </summary>
        /// <param name="node">当前数据</param>
        /// <param name="Nodes">所有数据</param>
        /// <param name="loadLevel">加载级数</param>
        /// <returns></returns>
        private TreeViewData TreeViewBuildTree(TreeViewData node, List<TreeViewData> Nodes)
        {
            // 从所有节点数据中筛选出 SonID 等于当前节点 Id 的节点，并按名称排序后转换为列表
            var children = Nodes.Where(a => a.SonID == node.Id).OrderBy(a => a.Name).ToList();
            // 检查是否存在子节点，并且加载级数大于等于 1

            if (children.Count > 0)
            {
                // 初始化当前节点的子节点集合
                node.Children = new();

                var findboolResult = false; //节点下是否有需要展开的节点


                // 先判断是否有展开的节点
                foreach (var child in children)
                {
                    var Result = RestoreNodeState(child);
                    if (Result == true)
                    {
                        findboolResult = true;
                        break;
                    }
                }

                if (findboolResult) //证明子节点不需要展开
                {
                    foreach (var child in children)
                    {
                        RestoreNodeState(child);
                        node.Children.Add(TreeViewBuildTree(child, Nodes));
                    }
                }
                else //需要展开节点
                {
                    if (RestoreNodeState(node))
                    {
                        foreach (var child in children)
                        {
                            RestoreNodeState(child);
                            node.Children.Add(TreeViewBuildTree(child, Nodes));
                        }
                    }
                    else
                    {
                        node.Children.Add(TreeItemCreateTest());
                    }
                }

                RestoreNodeState(node);
                // 更新当前节点的子节点数量
                node.Count = node.Children.Count;
            }

            // 返回构建好的当前节点
            return node;
        }

        #endregion

        #region 事件

        [RelayCommand]
        private void TreeViewMouseLeave()
        {
            _expandedStatesOld = new Dictionary<string, bool>();
            _expandedStatesOld = _expandedStates;

            _expandedStates.Clear();
            foreach (var item in TreeItems)
            {
                SaveNodeState(item);
            }


            AppSetData.Instance.Save();
        }


        /// <summary>
        /// 树节点展开
        /// </summary>
        /// <param name="node"></param>
        [RelayCommand]
        private void TreeViewItemExpanded(TreeViewData node)
        {
            if (node == null)
            {
                return;
            }
            var children = SharingTreeItemAll.Where(a => a.SonID == node.Id).OrderBy(a => a.Name).ToList();
            node.Children.Clear();
            foreach (var child in children)
            {
                child.Level = node.Level + 1;
                try
                {
                    RestoreNodeState(child);
                }
                catch (Exception e)
                {
                }

                node.Children.Add(TreeViewBuildTree(child, SharingTreeItemAll));
            }
        }

        [RelayCommand]
        private void TreeViewSelt(TreeViewData node)
        {
            TreeItemT = node;

        }

        #endregion

        #region 树型保存状态

        /// <summary>
        /// 递归保存指定节点及其所有子节点的展开状态
        /// </summary>
        /// <param name="node">要保存状态的树节点</param>
        private void SaveNodeState(TreeViewData node)
        {
            // 使用节点名称作为字典键
            // 如果节点名称不为空，则保存当前节点的展开状态
            if (!string.IsNullOrEmpty(node.Name))
            {
                _expandedStates[node.Id.ToString()] = node.IsExpanded;
            }

            // 遍历当前节点的所有子节点，递归保存每个子节点的展开状态
            foreach (var child in node.Children)
            {
                SaveNodeState(child);
            }
        }

        // /// <summary>
        // /// 恢复之前保存的所有树节点的展开状态
        // /// </summary>
        // [RelayCommand]
        // private void RestoreExpandedStates()
        // {
        //     try
        //     {
        //         foreach (var item in TreeItems)
        //         {
        //             RestoreNodeState(item);
        //         }
        //     }
        //     catch (Exception e)
        //     {
        //     }
        //
        //
        //     // MessageBox.Show("展开状态已恢复");
        // }

        /// <summary>
        /// 递归恢复指定节点及其所有子节点的展开状态
        /// </summary>
        /// <param name="node">要恢复状态的树节点</param>
        private bool RestoreNodeState(TreeViewData node)
        {
            var resultbool = false;
            // 检查节点名称是否为空，并尝试从保存的状态字典中恢复展开状态
            if (!string.IsNullOrEmpty(node.Id.ToString()) && _expandedStates.TryGetValue(node.Id.ToString(), out var state))
            {
                node.IsExpanded = state;
                resultbool = state;
            }

            // 遍历当前节点的所有子节点，递归恢复每个子节点的展开状态
            foreach (var child in node.Children)
            {
                RestoreNodeState(child);
            }
            return resultbool;
        }

        #endregion




        #region 菜单

        [RelayCommand]
        private void TreeViewAdd()
        {
            var parameters = new DialogParameters
            {
                { "SonID", TreeItemT?.Id ?? 0 },
                { "Title", "添加节点" },
            };
            _dialogService.ShowDialog(nameof(TreeViewEditView), parameters, result =>
            {
                if (result.Result == ButtonResult.OK)
                {
                    var newItem = result.Parameters.GetValue<TreeViewData>("EditedItem");
                    
                    // 验证节点类型
                    if (string.IsNullOrWhiteSpace(newItem.ExeClass))
                    {
                        _notificationService.ShowError("请选择节点类型");
                        return;
                    }

                    // 检查名称是否已存在
                    if (DbTreeViewData.ISSonIDToId(newItem))
                    {
                        _notificationService.ShowError($"名称 '{newItem.Name}' 已存在");
                        return;
                    }

                    // 设置父节点ID
                    if (TreeItemT != null)
                    {
                        newItem.SonID = TreeItemT.Id;
                    }

                    // 添加到数据库
                    if (DbTreeViewData.Add(newItem))
                    {
                        // 重新加载树数据
                        TreeViewLoadData();
                        
                        // 展开父节点
                        if (TreeItemT != null)
                        {
                            TreeItemT.IsExpanded = true;
                        }
                        _notificationService.ShowSuccess("添加成功");
                    }
                    else
                    {
                        _notificationService.ShowError("添加失败");
                    }
                }
            }, "NoTitleWindow");
        }

        [RelayCommand]
        private void TreeViewDelete()
        {
            if (TreeItemT == null)
            {
                _notificationService.Show("请先选择一个节点");
                return;
            }

            // 确认删除
            var result = System.Windows.MessageBox.Show($"确定要删除节点 '{TreeItemT.Name}' 吗?", "确认删除",
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                // 处理删除操作
                if (DbTreeViewData.Delete(TreeItemT))
                {
                    _notificationService.ShowSuccess("删除成功");
                    // 重新加载树
                    TreeViewLoadData();
                }
                else
                {
                    _notificationService.ShowError("删除失败");
                }
            }
        }


        [RelayCommand]
        private void TreeViewEdit()
        {
            if (TreeItemT == null)
            {
                _notificationService.Show("请先选择一个节点");
                return;
            }

            var parameters = new DialogParameters
            {
                { "Item", TreeItemT },
                { "Title", "编辑节点" }
            };

            _dialogService.ShowDialog(nameof(TreeViewEditView), parameters, result =>
            {
                if (result.Result == ButtonResult.OK)
                {
                    var updatedItem = result.Parameters.GetValue<TreeViewData>("EditedItem");

                    if (updatedItem != null)
                    {
                        // 验证节点类型
                        if (string.IsNullOrWhiteSpace(updatedItem.ExeClass))
                        {
                            _notificationService.ShowError("请选择节点类型");
                            return;
                        }

                        // 检查名称是否已存在(排除自身)
                        var tempItem = new TreeViewData
                        {
                            Name = updatedItem.Name,
                            SonID = updatedItem.SonID
                        };
                        
                        if (DbTreeViewData.ISSonIDToId(tempItem) &&
                            !DbTreeViewData.FindSameName(updatedItem.Id, updatedItem.Name, updatedItem.SonID))
                        {
                            _notificationService.ShowError($"名称 '{updatedItem.Name}' 已存在");
                            return;
                        }

                        // 处理更新操作
                        if (DbTreeViewData.Up(updatedItem))
                        {
                            _notificationService.ShowSuccess("保存成功");
                            // 重新加载树
                            TreeViewLoadData();
                        }
                        else
                        {
                            _notificationService.ShowError("保存失败");
                        }
                    }
                }
            }, "NoTitleWindow");
        }

        #endregion

        #region Drag

      

        #endregion


        public void DragOver(IDropInfo dropInfo)
        {
            var sourceItem = dropInfo.TreeView;
            var targetItem = dropInfo.TargetItem;

            if (sourceItem is TreeViewData dataObject && targetItem is TreeViewData type)
            {
                dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                dropInfo.Effects = DragDropEffects.Copy; // 或者 
            }

            
        }

        void IDropTarget.Drop(IDropInfo dropInfo)
        {
            var sourceItem = dropInfo.TreeView;
            var targetItem = dropInfo.TargetItem;
            
            
            if (sourceItem is TreeViewData sour && targetItem is TreeViewData tar)
            {
                DWGListDorg(sour, tar);
            }
        }

        private void DWGListDorg(TreeViewData sour, TreeViewData tar)
        {
            var old = new TreeViewData();
            YMapperH.Copy(sour, old);
            sour.SonID = tar.Id;
            var iss = DbTreeViewData.IS(sour);
            if (!iss)
            {
                DbTreeViewData.Up(sour);
            }

            TreeViewLoadData();
        }
    }
}
#endif 