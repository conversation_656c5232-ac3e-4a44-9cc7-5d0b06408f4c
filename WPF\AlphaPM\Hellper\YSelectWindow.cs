﻿using AlphaPM.Services.Window;

namespace AlphaPM.Hellper;

public class YSelectWindow
{
    
    private static YSelectWindow? _current =new YSelectWindow();
    /// <summary>
    /// 单例锁
    /// </summary>
    private static readonly object _lock = new();
    /// <summary>
    /// 获取应用级单例实例
    /// </summary>
    public static YSelectWindow Current
    {
        get
        {
            if (_current == null)
            {
                lock (_lock)
                {
                    _current ??= new YSelectWindow();
                }
            }

            return _current;
        }
    }
    #region 文件夹对话框操作

    /// <summary>
    /// 通用文件夹选择方法
    /// </summary>
    /// <param name="propertyName">属性名称（ProjectPath, WorkspacePath等）</param>
    /// <param name="title">对话框标题</param>
    /// <param name="currentValue">当前值</param>
    /// <returns>是否选择成功</returns>
    public  FolderDialogResult SelectFolder(string title, string currentValue)
    {
        var result = WindowsFileOperationService.SelectFolderDialog(title, currentValue);
        // if (result.Success)
        // {
        //     // 使用反射设置属性值
        //     var property = GetType().GetProperty(propertyName);
        //     property?.SetValue(this, result.SelectedPath);
        //     currentValue=result.SelectedPath;
        //     return true;
        // }

        return result;
    }

    /// <summary>
    /// 通用文件选择方法
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="title">对话框标题</param>
    /// <param name="filter">文件过滤器</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <returns>是否选择成功</returns>
    public bool SelectFile(string propertyName, string title, string filter, string? initialDirectory = null)
    {
        var result = WindowsFileOperationService.OpenFileDialog(title, filter, initialDirectory);
        if (result.Success && result.SelectedFiles.Count > 0)
        {
            var property = GetType().GetProperty(propertyName);
            property?.SetValue(this, result.SelectedFiles[0]);
            return true;
        }

        return false;
    }

    #endregion
}