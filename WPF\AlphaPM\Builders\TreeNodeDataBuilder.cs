using System.Collections.ObjectModel;
using AlphaPM.Models.DWG;
using Wpf.Ui.Controls;

namespace AlphaPM.Builders;

/// <summary>
/// TreeNodeData 构建器 - 提供流畅的API来构建树形结构
/// </summary>
public class TreeNodeDataBuilder
{
    private readonly TreeNodeData _node;

    private TreeNodeDataBuilder(TreeNodeData node)
    {
        _node = node;
    }

    #region 静态工厂方法

    /// <summary>
    /// 创建年份节点构建器
    /// </summary>
    /// <param name="yearName">年份名称</param>
    /// <returns>构建器实例</returns>
    public static TreeNodeDataBuilder CreateYear(string yearName)
    {
        var node = new TreeNodeData(yearName)
        {
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            Description = $"{yearName}年度项目管理",
            WpfUiSymbol = SymbolRegular.CalendarLtr24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        return new TreeNodeDataBuilder(node);
    }

    /// <summary>
    /// 创建项目节点构建器
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <returns>构建器实例</returns>
    public static TreeNodeDataBuilder CreateProject(string projectName)
    {
        var node = new TreeNodeData(projectName)
        {
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            Description = $"项目：{projectName}",
            ProjectStatus = "规划中",
            WpfUiSymbol = SymbolRegular.BuildingBank24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        return new TreeNodeDataBuilder(node);
    }

    /// <summary>
    /// 创建建筑节点构建器
    /// </summary>
    /// <param name="buildingName">建筑名称</param>
    /// <returns>构建器实例</returns>
    public static TreeNodeDataBuilder CreateBuilding(string buildingName)
    {
        var node = new TreeNodeData(buildingName)
        {
            NodeType = DWGNodeTypes.Building,
            Level = 3,
            Description = $"建筑：{buildingName}",
            BuildingStatus = "设计中",
            WpfUiSymbol = SymbolRegular.Building24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        return new TreeNodeDataBuilder(node);
    }

    /// <summary>
    /// 从现有节点创建构建器
    /// </summary>
    /// <param name="node">现有节点</param>
    /// <returns>构建器实例</returns>
    public static TreeNodeDataBuilder From(TreeNodeData node)
    {
        return new TreeNodeDataBuilder(node);
    }

    #endregion

    #region 属性设置方法

    /// <summary>
    /// 设置管理者
    /// </summary>
    /// <param name="manager">管理者名称</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithManager(string? manager)
    {
        _node.Manager = manager;
        return this;
    }

    /// <summary>
    /// 设置预算
    /// </summary>
    /// <param name="budget">预算金额</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithBudget(decimal budget)
    {
        _node.Budget = budget;
        return this;
    }

    /// <summary>
    /// 设置时间范围
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithDateRange(DateTime? startDate, DateTime? endDate)
    {
        _node.StartDate = startDate;
        _node.EndDate = endDate;
        return this;
    }

    /// <summary>
    /// 设置项目状态
    /// </summary>
    /// <param name="status">项目状态</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithProjectStatus(string? status)
    {
        _node.ProjectStatus = status;
        return this;
    }

    /// <summary>
    /// 设置进度百分比
    /// </summary>
    /// <param name="percentage">进度百分比</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithProgress(decimal percentage)
    {
        _node.ProgressPercentage = Math.Max(0, Math.Min(100, percentage));
        return this;
    }

    /// <summary>
    /// 设置建筑信息
    /// </summary>
    /// <param name="buildingType">建筑类型</param>
    /// <param name="area">建筑面积</param>
    /// <param name="floorCount">楼层数</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithBuildingInfo(string? buildingType, decimal area, int floorCount)
    {
        _node.BuildingType = buildingType;
        _node.BuildingArea = area;
        _node.FloorCount = floorCount;
        return this;
    }

    /// <summary>
    /// 设置设计师
    /// </summary>
    /// <param name="designer">设计师名称</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithDesigner(string? designer)
    {
        _node.Designer = designer;
        return this;
    }

    /// <summary>
    /// 设置备注
    /// </summary>
    /// <param name="remarks">备注信息</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithRemarks(string? remarks)
    {
        _node.Remarks = remarks;
        return this;
    }

    /// <summary>
    /// 设置图标
    /// </summary>
    /// <param name="symbol">WPF-UI 图标</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithIcon(SymbolRegular symbol)
    {
        _node.WpfUiSymbol = symbol;
        return this;
    }

    /// <summary>
    /// 设置描述
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public TreeNodeDataBuilder WithDescription(string description)
    {
        _node.Description = description;
        return this;
    }

    #endregion

    #region 子节点操作方法

    /// <summary>
    /// 添加子节点
    /// </summary>
    /// <param name="childBuilder">子节点构建器</param>
    /// <returns>当前构建器实例</returns>
    public TreeNodeDataBuilder AddChild(TreeNodeDataBuilder childBuilder)
    {
        var child = childBuilder.Build();
        _node.AddChild(child);
        return this;
    }

    /// <summary>
    /// 添加子节点
    /// </summary>
    /// <param name="child">子节点</param>
    /// <returns>当前构建器实例</returns>
    public TreeNodeDataBuilder AddChild(TreeNodeData child)
    {
        _node.AddChild(child);
        return this;
    }

    /// <summary>
    /// 批量添加子节点
    /// </summary>
    /// <param name="children">子节点集合</param>
    /// <returns>当前构建器实例</returns>
    public TreeNodeDataBuilder AddChildren(params TreeNodeData[] children)
    {
        foreach (var child in children)
        {
            _node.AddChild(child);
        }
        return this;
    }

    /// <summary>
    /// 批量添加子节点构建器
    /// </summary>
    /// <param name="childBuilders">子节点构建器集合</param>
    /// <returns>当前构建器实例</returns>
    public TreeNodeDataBuilder AddChildren(params TreeNodeDataBuilder[] childBuilders)
    {
        foreach (var builder in childBuilders)
        {
            _node.AddChild(builder.Build());
        }
        return this;
    }

    #endregion

    #region 构建方法

    /// <summary>
    /// 构建节点
    /// </summary>
    /// <returns>构建完成的节点</returns>
    public TreeNodeData Build()
    {
        return _node;
    }

    /// <summary>
    /// 构建为集合（用于绑定到 TreeView）
    /// </summary>
    /// <returns>包含单个节点的集合</returns>
    public ObservableCollection<TreeNodeData> BuildAsCollection()
    {
        return new ObservableCollection<TreeNodeData> { _node };
    }

    #endregion

    #region 隐式转换

    /// <summary>
    /// 隐式转换为 TreeNodeData
    /// </summary>
    /// <param name="builder">构建器</param>
    public static implicit operator TreeNodeData(TreeNodeDataBuilder builder)
    {
        return builder.Build();
    }

    #endregion
}
