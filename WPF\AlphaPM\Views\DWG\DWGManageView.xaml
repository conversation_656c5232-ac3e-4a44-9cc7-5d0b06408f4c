﻿<UserControl
    x:Class="AlphaPM.Views.DWG.DWGManageView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:behaviors="clr-namespace:Zylo.WPF.Behaviors;assembly=Zylo.WPF"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dwg="clr-namespace:AlphaPM.ViewModels.DWG"
    xmlns:local="clr-namespace:AlphaPM.Views.DWG"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:treeView="clr-namespace:Zylo.WPF.Controls.TreeView;assembly=Zylo.WPF"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    x:Name="DWGManageViewRoot"
    d:DataContext="{d:DesignInstance dwg:DWGManageViewModel}"
    d:DesignHeight="800"
    d:DesignWidth="1400"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    mc:Ignorable="d">

    <UserControl.Resources>
        <!--  容器样式  -->
        <Style x:Key="PanelContainerStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource ControlFillColorDefaultBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource ControlStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="8" />
            <Setter Property="Margin" Value="4" />
            <Setter Property="Padding" Value="8" />
        </Style>
        <!--  ControlStrokeColorDefaultBrush  -->
        <!--  标题样式  -->
        <Style x:Key="PanelTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Margin" Value="0,0,0,8" />
        </Style>
    </UserControl.Resources>

    <!--  三列布局：TreeView | ListBox | DataView  -->


    <Grid Margin="2">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  项目路径显示  -->
        <Grid Grid.Row="0" Margin="0,4,0,0">
            <Grid.ColumnDefinitions>

                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Button
                Grid.Column="0"
                Width="24"
                Height="20"
                Margin="8,0,0,0"
                Command="{Binding SelectProjectPathCommand}"
                Content="📂"
                FontSize="10"
                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                Style="{StaticResource NavigationButtonStyle}"
                ToolTip="选择项目路径" />
            <TextBlock
                Grid.Column="1"
                VerticalAlignment="Center"
                FontSize="14"
                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                Text="项目路径: " />
            <TextBox
                Grid.Column="2"
                VerticalAlignment="Center"
                FontSize="14"
                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                Text="{Binding ProjectPath}"
                ToolTip="{Binding ProjectPath}" />

        </Grid>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <!--  左侧 TreeView 区域  -->
                <ColumnDefinition Width="350" />
                <ColumnDefinition Width="2" />

                <!--  中间 ListBox 区域  -->
                <ColumnDefinition Width="200" />
                <ColumnDefinition Width="2" />

                <!--  右侧 DataView 区域  -->
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  左侧：TreeView 专业文件夹树  -->
            <Border Grid.Column="0" Style="{StaticResource PanelContainerStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  标题和操作按钮  -->
                    <Grid Grid.Row="0" Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            FontSize="14"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Text="项目菜单: " />

                        <!--  操作按钮组  -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <!--  添加年份按钮  -->
                            <Button
                                Width="32"
                                Height="32"
                                Padding="4"
                                Command="{Binding AddYearCommand}"
                                Style="{StaticResource NavigationButtonStyle}"
                                ToolTip="添加新年份">
                                <ui:SymbolIcon
                                    FontSize="16"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    Symbol="CalendarAdd24" />
                            </Button>

                        </StackPanel>
                    </Grid>

                    <!--  � 使用新的可重用 ZyloTreeView 控件 - 启用拖拽功能  -->
                    <treeView:ZyloTreeView
                        x:Name="MainTreeView"
                        Grid.Row="1"
                        AddChildNodeCommand="{Binding AddChildNodeCommand}"
                        CanDeleteSelectedNode="{Binding CanDeleteSelectedNode}"
                        CanEditSelectedNode="{Binding CanEditSelectedNode}"
                        ContextMenuItems="{Binding ContextMenuItems}"
                        DeleteNodeCommand="{Binding DeleteNodeCommand}"
                        DragHandler="{Binding DragDropHandler}"
                        DropHandler="{Binding DragDropHandler}"
                        EnableDragDrop="True"
                        FilterText="{Binding SearchKeyword, Mode=TwoWay}"
                        FilteredTreeData="{Binding FilteredTreeData}"
                        HasSelectedNode="{Binding HasSelectedNode}"
                        NodeCollapsedCommand="{Binding NodeCollapsedCommand}"
                        NodeExpandedCommand="{Binding NodeExpandedCommand}"
                        NodeSelectionChangedCommand="{Binding NodeSelectionChangedCommand}"
                        SearchPlaceholderText="🔍 搜索专业分类..."
                        SelectedNode="{Binding SelectedNode, Mode=TwoWay}"
                        ShowContextMenu="True"
                        ShowSearchBox="True"
                        StartEditNodeCommand="{Binding StartEditNodeCommand}"
                        TreeData="{Binding TreeData}">

                        <!--  使用动态右键菜单构建，在 DWGManageView.xaml.cs 中实现  -->
                    </treeView:ZyloTreeView>
                </Grid>
            </Border>

            <!--  🎨 可拖动分隔线  -->
            <GridSplitter
                Grid.Column="1"
                Width="4"
                HorizontalAlignment="Center"
                Background="Transparent"
                Cursor="SizeWE">
                <GridSplitter.Template>
                    <ControlTemplate TargetType="GridSplitter">
                        <Border Background="Transparent">
                            <Rectangle
                                Width="1"
                                Margin="0,12"
                                Fill="{DynamicResource ControlStrokeColorDefaultBrush}"
                                Opacity="0.4" />
                        </Border>
                    </ControlTemplate>
                </GridSplitter.Template>
            </GridSplitter>

            <!--  中间：ListBox 文件类型列表  -->
            <Border Grid.Column="2" Style="{StaticResource PanelContainerStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  标题  -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Button
                            Grid.Column="0"
                            Width="24"
                            Height="20"
                            Margin="8,0,0,0"
                            Command="{Binding ManageFileTypesCommand}"
                            Content="📋"
                            FontSize="10"
                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                            Style="{StaticResource NavigationButtonStyle}"
                            ToolTip="管理文件类型" />
                        <TextBlock
                            Grid.Column="1"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="文件类型" />
                    </Grid>



                    <!--  搜索框  -->
                    <ui:TextBox
                        Grid.Row="1"
                        Margin="0,0,0,8"
                        PlaceholderText="搜索类型..."
                        Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="Search24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  ListBox 内容  -->
                    <ListBox
                        Grid.Row="2"
                        Background="Transparent"
                        BorderThickness="0"
                        ItemsSource="{Binding FileTypeItems}"
                        SelectedItem="{Binding SelectedFileType}">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border
                                    Padding="8,6"
                                    Background="Transparent"
                                    CornerRadius="4">
                                    <StackPanel Orientation="Horizontal">
                                        <ui:SymbolIcon
                                            Margin="0,0,8,0"
                                            FontSize="16"
                                            Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}"
                                            Symbol="Document24" />
                                        <TextBlock
                                            VerticalAlignment="Center"
                                            Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                            Text="{Binding Name}" />
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                <Setter Property="Padding" Value="0" />
                                <Setter Property="Margin" Value="0,1" />
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{DynamicResource AccentFillColorDefaultBrush}" />
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="{DynamicResource ControlFillColorSecondaryBrush}" />
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </Grid>
            </Border>

            <!--  🎨 可拖动分隔线  -->
            <GridSplitter
                Grid.Column="3"
                Width="4"
                HorizontalAlignment="Center"
                Background="Transparent"
                Cursor="SizeWE">
                <GridSplitter.Template>
                    <ControlTemplate TargetType="GridSplitter">
                        <Border Background="Transparent">
                            <Rectangle
                                Width="1"
                                Margin="0,12"
                                Fill="{DynamicResource ControlStrokeColorDefaultBrush}"
                                Opacity="0.4" />
                        </Border>
                    </ControlTemplate>
                </GridSplitter.Template>
            </GridSplitter>

            <!--  右侧：DataView 文件详细信息  -->
            <Border Grid.Column="4" Style="{StaticResource PanelContainerStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  标题和工具栏  -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            Style="{StaticResource PanelTitleStyle}"
                            Text="📊 文件详情" />

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <ui:Button
                                Margin="4,0"
                                Appearance="Secondary"
                                Command="{Binding RefreshCommand}"
                                Content="刷新"
                                Icon="{ui:SymbolIcon ArrowClockwise24}" />
                            <ui:Button
                                Margin="4,0"
                                Appearance="Primary"
                                Command="{Binding CreateNewCommand}"
                                Content="新建"
                                Icon="{ui:SymbolIcon Add24}" />
                        </StackPanel>
                    </Grid>

                    <!--  统计信息  -->
                    <Border
                        Grid.Row="1"
                        Margin="0,0,0,8"
                        Padding="12,8"
                        Background="{DynamicResource ControlFillColorDefaultBrush}"
                        CornerRadius="4">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock
                                Margin="0,0,8,0"
                                FontWeight="Medium"
                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                Text="📈 统计：" />
                            <TextBlock
                                Margin="0,0,16,0"
                                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                Text="{Binding TotalFiles, StringFormat='文件总数: {0}'}" />
                            <TextBlock Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}" Text="{Binding SelectedFiles, StringFormat='已选择: {0}'}" />
                        </StackPanel>
                    </Border>

                    <!--  DataGrid 文件列表  -->
                    <DataGrid
                        Grid.Row="2"
                        AutoGenerateColumns="False"
                        Background="Transparent"
                        BorderThickness="0"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        GridLinesVisibility="Horizontal"
                        HeadersVisibility="Column"
                        ItemsSource="{Binding FileItems}"
                        RowHeight="32"
                        SelectedItem="{Binding SelectedFile}">
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Width="*" Header="📄 文件名">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="8,4" Orientation="Horizontal">
                                            <ui:SymbolIcon
                                                Margin="0,0,8,0"
                                                FontSize="16"
                                                Foreground="{DynamicResource AccentTextFillColorSecondaryBrush}"
                                                Symbol="Document24" />
                                            <TextBlock
                                                VerticalAlignment="Center"
                                                Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                                Text="{Binding FileName}" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding FileSize}"
                                Header="📏 大小" />

                            <DataGridTextColumn
                                Width="140"
                                Binding="{Binding ModifiedTime, StringFormat='yyyy-MM-dd HH:mm'}"
                                Header="📅 修改时间" />

                            <DataGridTemplateColumn Width="120" Header="🔧 操作">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <ui:Button
                                                Margin="2"
                                                Padding="8,4"
                                                Appearance="Secondary"
                                                Command="{Binding DataContext.OpenFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Content="打开"
                                                FontSize="10" />
                                            <ui:Button
                                                Margin="2"
                                                Padding="8,4"
                                                Appearance="Danger"
                                                Command="{Binding DataContext.DeleteFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Content="删除"
                                                FontSize="10" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>


                </Grid>
            </Border>
        </Grid>

        <!--  状态栏  -->
        <Border
            Grid.Row="2"
            Margin="0,8,0,0"
            Padding="12,6"
            Background="{DynamicResource ControlFillColorDefaultBrush}"
            CornerRadius="4">
            <TextBlock
                FontSize="12"
                Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                Text="{Binding StatusMessage}" />
        </Border>
    </Grid>

</UserControl>