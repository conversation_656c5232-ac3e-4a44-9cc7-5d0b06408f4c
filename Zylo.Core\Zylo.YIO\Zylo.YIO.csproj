﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
        <LangVersion>13</LangVersion>
        <PackageId>Zylo.YIO</PackageId>
        <Product>Zylo File Operations</Product>

        <!-- NuGet 包信息 -->
        <Description>🚀 Zylo.YIO - 企业级文件操作工具库。提供全面的文件处理解决方案，支持文件操作、监控、安全、压缩、批量处理等现代化文件管理功能。</Description>
        <PackageTags>file;io;operations;monitoring;security;compression;batch;processing;zylo</PackageTags>
        <PackageProjectUrl>https://github.com/zylo/zylo-yio</PackageProjectUrl>
        <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

        <!-- 发布说明 -->
        <PackageReleaseNotes>
            🎉 v1.3.2 - 重构与功能增强：
            - 🔧 完全重构配置处理系统，分离为专用处理器
            - 📁 JSON、XML、INI 格式独立处理
            - 🚀 统一管理器提供一致接口
            - ✨ 扩展方法支持更灵活的路径配置
            - 📦 更好的模块化设计和可维护性
        </PackageReleaseNotes>

        <!-- 源代码生成器配置 -->
        <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
        <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>

        <!-- 警告抑制 -->
        <NoWarn>$(NoWarn);CS1587;CS1591</NoWarn>
        <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
    </PropertyGroup>
    <ItemGroup>
        <None Include="README.md" Pack="true" PackagePath="\" /><!-- 将README.md包含到NuGet包中 -->
    </ItemGroup>

    <!-- Microsoft.Extensions 依赖包 - 源代码生成器需要 -->
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions"
            Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions"
            Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
    </ItemGroup>

    <ItemGroup>
        <!-- 🚫 暂时移除 Zylo.Toolkit 引用，避免 YStatic 生成器 -->
        <ProjectReference Include="..\Zylo.Toolkit\Zylo.Toolkit.csproj"
            OutputItemType="Analyzer"
            ReferenceOutputAssembly="true" />


        <ProjectReference Include="..\Zylo.YLog.Runtime\Zylo.YLog.Runtime.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Tests\" />
    </ItemGroup>


</Project>