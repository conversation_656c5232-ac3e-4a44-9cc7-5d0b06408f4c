<UserControl
    Height="800"
    Width="800"
    d:DataContext="{d:DesignInstance Type=vm:FileTypeItemManagerViewModel}"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.DWG.FileTypeItemManagerView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:behaviors="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:AlphaPM.Views.DWG"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:vm="clr-namespace:AlphaPM.ViewModels.DWG"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  转换器  -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

        <!--  反向布尔到可见性转换器  -->
        <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />

        <!--  反向布尔转换器  -->
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter" />

        <!--  通用类型画刷转换器  -->
        <local:GeneralTypeBrushConverter x:Key="GeneralTypeBrushConverter" />

        <!--  通用类型文本转换器  -->
        <local:GeneralTypeTextConverter x:Key="GeneralTypeTextConverter" />

        <!--  编辑模式标题转换器  -->
        <local:EditModeHeaderConverter x:Key="EditModeHeaderConverter" />

        <!--  保存按钮文本转换器  -->
        <local:SaveButtonTextConverter x:Key="SaveButtonTextConverter" />

        <!--  状态颜色转换器  -->
        <local:StatusColorConverter x:Key="StatusColorConverter" />

        <!--  样式  -->
        <Style TargetType="TextBlock" x:Key="SectionHeaderStyle">
            <Setter Property="FontSize" Value="16" />
            <Setter Property="FontWeight" Value="SemiBold" />
            <Setter Property="Margin" Value="0,0,0,8" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
        </Style>

        <Style TargetType="Border" x:Key="StatCardStyle">
            <Setter Property="Background" Value="{DynamicResource CardBackgroundFillColorDefaultBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource CardStrokeColorDefaultBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="CornerRadius" Value="6" />
            <Setter Property="Padding" Value="12,8" />
            <Setter Property="Margin" Value="4" />
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <!--  标题栏  -->
            <RowDefinition Height="Auto" />
            <!--  统计信息  -->
            <RowDefinition Height="*" />
            <!--  主内容区域  -->
            <RowDefinition Height="Auto" />
            <!--  状态栏  -->
        </Grid.RowDefinitions>

        <!--#region 标题栏-->
        <Border
            Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="0">
            <Grid Margin="16,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  标题和描述  -->
                <StackPanel Grid.Column="0">
                    <TextBlock
                        FontSize="20"
                        Style="{StaticResource SectionHeaderStyle}"
                        Text="文件类型管理" />
                    <TextBlock
                        FontSize="12"
                        Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                        Text="管理系统中的文件类型，支持通用类型和TreeNode关联类型" />
                </StackPanel>

                <!--  操作按钮  -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ui:Button
                        Appearance="Primary"
                        Command="{Binding NewFileTypeCommand}"
                        Content="新建类型"
                        Icon="{ui:SymbolIcon Add24}"
                        Margin="0,0,8,0" />

                    <ui:Button
                        Appearance="Secondary"
                        Command="{Binding LoadDataCommand}"
                        Content="刷新"
                        Icon="{ui:SymbolIcon ArrowClockwise24}" />
                </StackPanel>
            </Grid>
        </Border>
        <!--#endregion-->

        <!--#region 统计信息-->
        <Border
            Background="{DynamicResource LayerFillColorDefaultBrush}"
            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
            BorderThickness="0,0,0,1"
            Grid.Row="1">
            <StackPanel Margin="16,8" Orientation="Horizontal">
                <!--  总数统计  -->
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <TextBlock
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="{DynamicResource AccentTextFillColorPrimaryBrush}"
                            Text="{Binding FileTypes.Count}" />
                        <TextBlock
                            FontSize="11"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Text="总数" />
                    </StackPanel>
                </Border>

                <!--  通用类型统计  -->
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <TextBlock
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="{DynamicResource SystemFillColorSuccessBrush}"
                            Text="{Binding GeneralTypesCount}" />
                        <TextBlock
                            FontSize="11"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Text="通用类型" />
                    </StackPanel>
                </Border>

                <!--  专用类型统计  -->
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <TextBlock
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="{DynamicResource SystemFillColorCautionBrush}"
                            Text="{Binding SpecificTypesCount}" />
                        <TextBlock
                            FontSize="11"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Text="专用类型" />
                    </StackPanel>
                </Border>

                <!--  启用统计  -->
                <Border Style="{StaticResource StatCardStyle}">
                    <StackPanel>
                        <TextBlock
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="{DynamicResource SystemFillColorAttentionBrush}"
                            Text="{Binding EnabledTypesCount}" />
                        <TextBlock
                            FontSize="11"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            Text="已启用" />
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>
        <!--#endregion-->

        <!--#region 主内容区域-->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350" />
                <!--  左侧文件类型列表  -->
                <ColumnDefinition Width="4" />
                <!--  分隔线  -->
                <ColumnDefinition Width="*" />
                <!--  右侧编辑表单  -->
            </Grid.ColumnDefinitions>

            <!--#region 左侧文件类型列表-->
            <Border
                Background="{DynamicResource LayerFillColorDefaultBrush}"
                BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
                BorderThickness="0,0,1,0"
                Grid.Column="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <!--  搜索框  -->
                        <RowDefinition Height="*" />
                        <!--  文件类型列表  -->
                        <RowDefinition Height="Auto" />
                        <!--  操作按钮  -->
                    </Grid.RowDefinitions>

                    <!--  搜索框  -->
                    <ui:TextBox
                        Grid.Row="0"
                        Margin="12,12,12,8"
                        PlaceholderText="搜索文件类型..."
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="Search24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  文件类型列表  -->
                    <ui:ListView
                        Background="Transparent"
                        BorderThickness="0"
                        Grid.Row="1"
                        ItemsSource="{Binding FilteredFileTypes}"
                        Margin="8,0,8,8"
                        SelectedItem="{Binding SelectedFileType}">

                        <!--  列表项模板  -->
                        <ui:ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="4,2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <!--  状态指示器  -->
                                        <ColumnDefinition Width="*" />
                                        <!--  名称和描述  -->
                                        <ColumnDefinition Width="Auto" />
                                        <!--  类型标签  -->
                                    </Grid.ColumnDefinitions>

                                    <!--  状态指示器  -->
                                    <Border
                                        CornerRadius="2"
                                        Grid.Column="0"
                                        Height="24"
                                        Margin="0,0,8,0"
                                        Width="4">
                                        <Border.Background>
                                            <MultiBinding Converter="{StaticResource StatusColorConverter}">
                                                <Binding Path="IsEnabled" />
                                                <Binding Path="IsGeneral" />
                                            </MultiBinding>
                                        </Border.Background>
                                    </Border>

                                    <!--  名称和描述  -->
                                    <StackPanel Grid.Column="1">
                                        <TextBlock FontWeight="Medium" Text="{Binding Name}" />
                                        <TextBlock
                                            FontSize="11"
                                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                                            Text="{Binding Description}"
                                            TextTrimming="CharacterEllipsis" />
                                    </StackPanel>

                                    <!--  类型标签  -->
                                    <StackPanel
                                        Grid.Column="2"
                                        Margin="8,0,0,0"
                                        Orientation="Horizontal">
                                        <!--  通用/专用标签  -->
                                        <Border
                                            Background="{Binding IsGeneral, Converter={StaticResource GeneralTypeBrushConverter}}"
                                            CornerRadius="3"
                                            Margin="0,0,2,0"
                                            Padding="4,2">
                                            <TextBlock
                                                FontSize="9"
                                                Foreground="White"
                                                Text="{Binding IsGeneral, Converter={StaticResource GeneralTypeTextConverter}}" />
                                        </Border>

                                        <!--  DWG标签  -->
                                        <Border
                                            Background="{DynamicResource SystemFillColorAttentionBrush}"
                                            CornerRadius="3"
                                            Padding="4,2"
                                            Visibility="{Binding IsDWG, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <TextBlock
                                                FontSize="9"
                                                Foreground="White"
                                                Text="DWG" />
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ui:ListView.ItemTemplate>
                    </ui:ListView>

                    <!--  操作按钮  -->
                    <StackPanel
                        Grid.Row="2"
                        HorizontalAlignment="Right"
                        Margin="12,0,12,12"
                        Orientation="Horizontal">
                        <ui:Button
                            Appearance="Secondary"
                            Command="{Binding EditFileTypeCommand}"
                            Content="编辑"
                            Icon="{ui:SymbolIcon Edit24}"
                            Margin="0,0,4,0"
                            Padding="8,4" />

                        <ui:Button
                            Appearance="Danger"
                            Command="{Binding DeleteCommand}"
                            Content="删除"
                            Icon="{ui:SymbolIcon Delete24}"
                            Padding="8,4" />
                    </StackPanel>
                </Grid>
            </Border>
            <!--#endregion-->

            <!--  分隔线  -->
            <GridSplitter Background="{DynamicResource CardStrokeColorDefaultBrush}" Grid.Column="1" />

            <!--#region 右侧编辑表单-->
            <Border Background="{DynamicResource LayerFillColorDefaultBrush}" Grid.Column="2">
                <Grid>
                    <!--  未选择状态  -->
                    <StackPanel
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Visibility="{Binding IsEditing, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <ui:SymbolIcon
                            FontSize="48"
                            Foreground="{DynamicResource TextFillColorDisabledBrush}"
                            Symbol="DocumentAdd24" />
                        <TextBlock
                            FontSize="14"
                            Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                            HorizontalAlignment="Center"
                            Margin="0,8,0,0"
                            Text="选择一个文件类型进行编辑" />
                        <TextBlock
                            FontSize="12"
                            Foreground="{DynamicResource TextFillColorTertiaryBrush}"
                            HorizontalAlignment="Center"
                            Margin="0,4,0,0"
                            Text="或点击 '新建类型' 创建新的文件类型" />
                    </StackPanel>

                    <!--  编辑表单  -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Visibility="{Binding IsEditing, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Margin="24">
                            <!--  表单标题  -->
                            <TextBlock Style="{StaticResource SectionHeaderStyle}" Text="{Binding IsNewMode, Converter={StaticResource EditModeHeaderConverter}}" />

                            <!--  基本信息  -->
                            <TextBlock
                                FontSize="14"
                                Margin="0,16,0,8"
                                Style="{StaticResource SectionHeaderStyle}"
                                Text="基本信息" />

                            <StackPanel Margin="0,0,0,12">
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,4"
                                    Text="名称 *" />
                                <ui:TextBox PlaceholderText="请输入文件类型名称" Text="{Binding EditingFileType.Name, UpdateSourceTrigger=PropertyChanged}" />
                            </StackPanel>

                            <StackPanel Margin="0,0,0,12">
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,4"
                                    Text="描述" />
                                <ui:TextBox PlaceholderText="请输入文件类型描述" Text="{Binding EditingFileType.Description, UpdateSourceTrigger=PropertyChanged}" />
                            </StackPanel>

                            <!--  类型设置  -->
                            <TextBlock
                                FontSize="14"
                                Margin="0,16,0,8"
                                Style="{StaticResource SectionHeaderStyle}"
                                Text="类型设置" />

                            <ui:ToggleSwitch
                                Content="启用此文件类型"
                                IsChecked="{Binding EditingFileType.IsEnabled}"
                                Margin="0,0,0,8" />

                            <ui:ToggleSwitch
                                Content="DWG文件类型"
                                IsChecked="{Binding EditingFileType.IsDWG}"
                                Margin="0,0,0,8" />

                            <ui:ToggleSwitch
                                Content="通用类型（适用于所有TreeNode）"
                                IsChecked="{Binding EditingFileType.IsGeneral}"
                                Margin="0,0,0,12" />

                            <!--  TreeNode关联  -->
                            <StackPanel Margin="0,0,0,24">
                                <TextBlock
                                    FontWeight="Medium"
                                    Margin="0,0,0,4"
                                    Text="关联的TreeNode" />
                                <ComboBox
                                    DisplayMemberPath="Name"
                                    IsEnabled="{Binding EditingFileType.IsGeneral, Converter={StaticResource InverseBooleanConverter}}"
                                    ItemsSource="{Binding TreeNodes}"
                                    SelectedValue="{Binding EditingFileType.TreeNodeId}"
                                    SelectedValuePath="Id" />
                            </StackPanel>

                            <!--  操作按钮  -->
                            <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                                <ui:Button
                                    Appearance="Secondary"
                                    Command="{Binding CancelCommand}"
                                    Content="取消"
                                    Margin="0,0,8,0" />

                                <ui:Button
                                    Appearance="Primary"
                                    Command="{Binding SaveCommand}"
                                    Content="{Binding IsNewMode, Converter={StaticResource SaveButtonTextConverter}}"
                                    Icon="{ui:SymbolIcon Save24}" />
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
            <!--#endregion-->
        </Grid>
        <!--#endregion-->

        <!--#region 状态栏-->
        <Border
            Background="{DynamicResource CardBackgroundFillColorDefaultBrush}"
            BorderBrush="{DynamicResource CardStrokeColorDefaultBrush}"
            BorderThickness="0,1,0,0"
            Grid.Row="3">
            <Grid Height="32" Margin="16,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  状态消息  -->
                <TextBlock
                    FontSize="12"
                    Grid.Column="0"
                    Text="{Binding StatusMessage}"
                    VerticalAlignment="Center" />

                <!--  加载指示器  -->
                <ui:ProgressRing
                    Grid.Column="1"
                    Height="16"
                    IsIndeterminate="True"
                    Margin="0,0,8,0"
                    Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                    Width="16" />

                <!--  对话框关闭按钮  -->
                <ui:Button
                    Appearance="Secondary"
                    Command="{Binding CloseDialogCommand}"
                    Content="关闭"
                    FontSize="12"
                    Grid.Column="2"
                    Height="30"
                    HorizontalAlignment="Center"
                    Icon="{ui:SymbolIcon Dismiss24}"
                    MinWidth="60"
                    VerticalAlignment="Center" />
            </Grid>
        </Border>
        <!--#endregion-->
    </Grid>
</UserControl>
