using System;
using System.IO;
using Zylo.YData;

namespace WPF
{
    /// <summary>
    /// 数据库连接测试类
    /// 用于验证 SQLite 连接字符串修复是否有效
    /// </summary>
    public static class DatabaseConnectionTest
    {
        /// <summary>
        /// 测试 SQLite 连接字符串
        /// </summary>
        public static void TestSQLiteConnection()
        {
            try
            {
                Console.WriteLine("🔍 开始测试 SQLite 连接...");
                
                // 创建测试数据库路径
                var testDbPath = Path.Combine(Path.GetTempPath(), "test_connection.db");
                Console.WriteLine($"📁 测试数据库路径: {testDbPath}");
                
                // 使用修复后的连接字符串格式
                var connectionString = $"Data Source={testDbPath};Cache=Shared;";
                Console.WriteLine($"🔗 连接字符串: {connectionString}");
                
                // 配置 YData
                YData.ConfigureAuto(connectionString, YDataType.Sqlite);
                Console.WriteLine("✅ YData 配置成功");
                
                // 清理测试文件
                if (File.Exists(testDbPath))
                {
                    File.Delete(testDbPath);
                    Console.WriteLine("🧹 清理测试文件");
                }
                
                Console.WriteLine("🎉 SQLite 连接测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ SQLite 连接测试失败: {ex.Message}");
                Console.WriteLine($"📋 详细错误: {ex}");
            }
        }
        
        /// <summary>
        /// 测试错误的连接字符串（用于对比）
        /// </summary>
        public static void TestWrongConnectionString()
        {
            try
            {
                Console.WriteLine("🔍 测试错误的连接字符串...");
                
                var testDbPath = Path.Combine(Path.GetTempPath(), "test_wrong.db");
                
                // 使用错误的连接字符串格式（修复前的格式）
                var wrongConnectionString = $"TreeView Source={testDbPath};Cache=Shared;";
                Console.WriteLine($"❌ 错误连接字符串: {wrongConnectionString}");
                
                YData.ConfigureAuto(wrongConnectionString, YDataType.Sqlite);
                Console.WriteLine("这行不应该被执行到");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✅ 预期的错误: {ex.Message}");
            }
        }
    }
}
