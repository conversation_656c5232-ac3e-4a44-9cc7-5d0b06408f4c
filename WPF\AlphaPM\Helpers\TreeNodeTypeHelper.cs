using System.Collections.ObjectModel;
using AlphaPM.Models.DWG;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Helpers;

/// <summary>
/// TreeNode 类型转换和处理工具类
/// 解决 YTreeNodeData 和 TreeNodeData 之间的类型不匹配问题
/// </summary>
public static class TreeNodeTypeHelper
{
    #region 类型检查方法

    /// <summary>
    /// 检查节点是否为 TreeNodeData 类型
    /// </summary>
    /// <param name="node">要检查的节点</param>
    /// <returns>是否为 TreeNodeData 类型</returns>
    public static bool IsTreeNodeData(YTreeNodeData? node)
    {
        return node is TreeNodeData;
    }

    /// <summary>
    /// 安全地将 YTreeNodeData 转换为 TreeNodeData
    /// </summary>
    /// <param name="node">基类节点</param>
    /// <returns>转换后的 TreeNodeData，失败返回 null</returns>
    public static TreeNodeData? SafeCast(YTreeNodeData? node)
    {
        return node as TreeNodeData;
    }

    #endregion

    #region 集合转换方法

    /// <summary>
    /// 将 YTreeNodeData 集合转换为 TreeNodeData 集合
    /// </summary>
    /// <param name="nodes">基类节点集合</param>
    /// <returns>TreeNodeData 节点集合</returns>
    public static IEnumerable<TreeNodeData> FilterTreeNodeData(IEnumerable<YTreeNodeData> nodes)
    {
        return nodes.OfType<TreeNodeData>();
    }

    /// <summary>
    /// 将 ObservableCollection<YTreeNodeData> 转换为 List<TreeNodeData>
    /// </summary>
    /// <param name="collection">基类节点集合</param>
    /// <returns>TreeNodeData 节点列表</returns>
    public static List<TreeNodeData> ToTreeNodeDataList(ObservableCollection<YTreeNodeData> collection)
    {
        return collection.OfType<TreeNodeData>().ToList();
    }

    /// <summary>
    /// 将 TreeNodeData 集合转换为 YTreeNodeData 集合（向上转型）
    /// </summary>
    /// <param name="nodes">TreeNodeData 节点集合</param>
    /// <returns>YTreeNodeData 节点集合</returns>
    public static IEnumerable<YTreeNodeData> ToBaseType(IEnumerable<TreeNodeData> nodes)
    {
        return nodes.Cast<YTreeNodeData>();
    }

    #endregion

    #region 递归查找方法

    /// <summary>
    /// 在混合类型的树结构中递归查找 TreeNodeData 节点
    /// </summary>
    /// <param name="nodes">节点集合（可能包含不同类型）</param>
    /// <param name="nodeId">要查找的节点ID</param>
    /// <returns>找到的 TreeNodeData 节点</returns>
    public static TreeNodeData? FindTreeNodeRecursive(IEnumerable<YTreeNodeData> nodes, int nodeId)
    {
        foreach (var node in nodes)
        {
            // 检查当前节点
            if (node.Id == nodeId && node is TreeNodeData treeNode)
                return treeNode;

            // 递归查找子节点
            var found = FindTreeNodeRecursive(node.YChildren, nodeId);
            if (found != null)
                return found;
        }

        return null;
    }

    /// <summary>
    /// 在 TreeNodeData 集合中递归查找节点
    /// </summary>
    /// <param name="nodes">TreeNodeData 节点集合</param>
    /// <param name="nodeId">要查找的节点ID</param>
    /// <returns>找到的节点</returns>
    public static TreeNodeData? FindTreeNodeRecursive(IEnumerable<TreeNodeData> nodes, int nodeId)
    {
        foreach (var node in nodes)
        {
            if (node.Id == nodeId)
                return node;

            var found = FindTreeNodeRecursive(FilterTreeNodeData(node.YChildren), nodeId);
            if (found != null)
                return found;
        }

        return null;
    }

    #endregion

    #region 树结构操作方法

    /// <summary>
    /// 获取节点的所有 TreeNodeData 类型的子节点
    /// </summary>
    /// <param name="node">父节点</param>
    /// <returns>TreeNodeData 类型的子节点集合</returns>
    public static IEnumerable<TreeNodeData> GetTreeNodeChildren(YTreeNodeData node)
    {
        return FilterTreeNodeData(node.YChildren);
    }

    /// <summary>
    /// 获取节点的 TreeNodeData 类型的父节点
    /// </summary>
    /// <param name="node">子节点</param>
    /// <returns>TreeNodeData 类型的父节点，如果不是则返回 null</returns>
    public static TreeNodeData? GetTreeNodeParent(YTreeNodeData node)
    {
        return SafeCast(node.YParent);
    }

    /// <summary>
    /// 递归获取所有 TreeNodeData 类型的后代节点
    /// </summary>
    /// <param name="node">根节点</param>
    /// <returns>所有 TreeNodeData 类型的后代节点</returns>
    public static IEnumerable<TreeNodeData> GetAllTreeNodeDescendants(YTreeNodeData node)
    {
        var descendants = new List<TreeNodeData>();
        
        foreach (var child in FilterTreeNodeData(node.YChildren))
        {
            descendants.Add(child);
            descendants.AddRange(GetAllTreeNodeDescendants(child));
        }
        
        return descendants;
    }

    #endregion

    #region 实用工具方法

    /// <summary>
    /// 统计树结构中 TreeNodeData 节点的数量
    /// </summary>
    /// <param name="nodes">节点集合</param>
    /// <returns>TreeNodeData 节点数量</returns>
    public static int CountTreeNodeData(IEnumerable<YTreeNodeData> nodes)
    {
        int count = 0;
        foreach (var node in nodes)
        {
            if (node is TreeNodeData)
                count++;
            
            count += CountTreeNodeData(node.YChildren);
        }
        return count;
    }

    /// <summary>
    /// 验证节点集合是否都是 TreeNodeData 类型
    /// </summary>
    /// <param name="nodes">要验证的节点集合</param>
    /// <returns>是否都是 TreeNodeData 类型</returns>
    public static bool AreAllTreeNodeData(IEnumerable<YTreeNodeData> nodes)
    {
        return nodes.All(node => node is TreeNodeData);
    }

    #endregion
}
