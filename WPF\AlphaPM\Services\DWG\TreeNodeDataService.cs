using AlphaPM.Data;
using AlphaPM.Extensions;
using AlphaPM.Models.DWG;
using AlphaPM.Services.Base;
using Wpf.Ui.Controls;
using Zylo.WPF.Controls.TreeView;
using Zylo.YData;
using Zylo.YIO.Core;

namespace AlphaPM.Services.DWG;

/// <summary>
/// TreeNodeData 数据服务
/// </summary>
/// <remarks>
/// 🎯 功能特点：
/// - 直接使用TreeNodeData作为数据模型
/// - 基于YData ORM框架
/// - 支持三层树形结构
/// - 自动构建父子关系
///
/// 🔧 技术特点：
/// - 使用YData.Select<T>()语法
/// - 支持异步操作
/// - 自动时间戳管理
/// - 支持事务操作
///
/// 💡 使用场景：
/// - DWGManageViewModel数据源
/// - ZyloTreeView数据绑定
/// - 树形数据CRUD操作
/// </remarks>
public class TreeNodeDataService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly YLoggerInstance _logger = YLogger.ForDebug<TreeNodeDataService>();

    /// <summary>
    /// 数据库目录
    /// </summary>
    private string _appDirectory = string.Empty;

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public TreeNodeDataService()
    {
        _logger.Debug("TreeNodeDataService 初始化");
        YServiceBase.ConnectionData();
        InitializeAsync();
    }

    #endregion


    #region 初始化

    /// <summary>
    /// 初始化数据库表结构
    /// </summary>
    /// <param name="dwgNodeDataService">DWG节点数据服务（可选，用于创建初始数据）</param>
    public async Task InitializeAsync(DWGNodeDataService? dwgNodeDataService = null)
    {
        try
        {
            _logger.Debug("开始初始化TreeNodeData表结构");

            // 使用YData的CodeFirst功能创建表
            YData.FreeSql.CodeFirst.SyncStructure<TreeNodeData>();

            _logger.Info("TreeNodeData表结构初始化完成");

            // 检查是否有初始数据
            var count = await YData.Select<TreeNodeData>().CountAsync();
            if (count == 0 && dwgNodeDataService != null)
            {
                _logger.Debug("数据库为空，使用DWGNodeDataService创建初始数据");
                await dwgNodeDataService.CreateInitialDataAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"初始化TreeNodeData表结构失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 创建初始数据
    /// </summary>
    // private async Task CreateInitialDataAsync()
    // {
    //     _logger.Debug("开始创建初始数据");
    //
    //     try
    //     {
    //         var year2024 = DWGNodeDataService.CreateNextYearTemplate("2024");
    //
    //
    //         var savedYear = await AddAsync(year2024);
    //         _logger.Info($"✅ 创建年份节点: {savedYear.Name}(ID:{savedYear.Id})");
    //
    //         var project = DWGNodeDataService.CreateProjectTemplate("示例项目",1, "项目经理",10, DateTime.Now, DateTime.Now);
    //
    //
    //         var savedProject = await AddAsync(project);
    //         _logger.Info($"✅ 创建项目节点: {savedProject.Name}(ID:{savedProject.Id})");
    //
    //         var PJ = DWGNodeDataService.CreateBuildingTemplate("1#", 2,"框架", 10, 10, "在建");
    //         await AddAsync(PJ);
    //
    //         _logger.Info("🎉 初始数据创建完成！");
    //         _logger.Info($"📊 数据结构: 📅 {year2024.Name}年 → 📁 {savedProject.Name} → 🏢 {PJ.Name}");
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.Error($"❌ 创建初始数据失败: {ex.Message}");
    //         throw;
    //     }
    // }

    #endregion

    #region 查询操作

    /// <summary>
    /// 获取所有节点（扁平列表）
    /// </summary>
    public async Task<List<TreeNodeData>> GetAllAsync()
    {
        _logger.Debug("获取所有节点");

        var nodes = await YData.Select<TreeNodeData>()
            .OrderBy(n => n.Level)
            .ToListAsync();

        _logger.Debug($"获取到 {nodes.Count} 个节点");
        return nodes;
    }

    /// <summary>
    /// 获取树形结构数据
    /// </summary>
    public async Task<List<TreeNodeData>> GetTreeDataAsync()
    {
        _logger.Debug("获取树形结构数据");

        var allNodes = await GetAllAsync();
        var treeData = BuildTreeStructure(allNodes);

        _logger.Debug($"构建树形结构完成，根节点数量: {treeData.Count}");
        return treeData;
    }

    /// <summary>
    /// 根据ID获取节点
    /// </summary>
    public async Task<TreeNodeData?> GetByIdAsync(int id)
    {
        _logger.Debug($"获取节点，ID: {id}");

        var node = await YData.GetAsync<TreeNodeData>(id);

        if (node == null)
        {
            _logger.Warning($"未找到节点，ID: {id}");
        }

        return node;
    }

    /// <summary>
    /// 根据名称获取节点
    /// </summary>
    public async Task<TreeNodeData?> GetByNameAsync(string name, int? parentId = null)
    {
        _logger.Debug($"根据名称获取节点: {name}, 父节点ID: {parentId}");

        var query = YData.Select<TreeNodeData>()
            .Where(n => n.Name == name);

        if (parentId.HasValue)
        {
            query = query.Where(n => n.ParentId == parentId);
        }
        else
        {
            query = query.Where(n => n.ParentId == null);
        }

        var node = await query.FirstAsync();

        if (node == null)
        {
            _logger.Warning($"未找到节点: {name}");
        }

        return node;
    }

    /// <summary>
    /// 获取子节点
    /// </summary>
    public async Task<List<TreeNodeData>> GetChildrenAsync(int parentId)
    {
        _logger.Debug($"获取子节点，父节点ID: {parentId}");

        var children = await YData.Select<TreeNodeData>()
            .Where(n => n.ParentId == parentId)
            .OrderBy(n => n.SortOrder)
            .ToListAsync();

        _logger.Debug($"获取到 {children.Count} 个子节点");
        return children;
    }

    /// <summary>
    /// 获取根节点
    /// </summary>
    public async Task<List<TreeNodeData>> GetRootNodesAsync()
    {
        _logger.Debug("获取根节点");

        var roots = await YData.Select<TreeNodeData>()
            .Where(n => n.ParentId == null)
            .ToListAsync();

        // 对年份节点进行智能排序：先尝试按数值排序，失败则按字母排序
        var sortedRoots = roots.OrderBy(n =>
            {
                if (int.TryParse(n.Name, out int year))
                {
                    return year; // 年份按数值排序（2017, 2018, 2019...）
                }

                return int.MaxValue; // 非数字年份排到最后
            }).ThenBy(n => n.Name) // 非数字按字母排序
            .ToList();

   
        _logger.Debug($"获取到 {sortedRoots.Count} 个根节点，已按年份排序");
        return sortedRoots;
    }

    #endregion

    #region 添加操作

    /// <summary>
    /// 添加节点
    /// </summary>
    public async Task<TreeNodeData> AddAsync(TreeNodeData node)
    {
        _logger.Debug($"添加节点: {node.Name}");

        // 设置创建时间
        node.CreatedAt = DateTime.Now;
        node.ModifiedAt = DateTime.Now;

        // 生成路径
        if (node.ParentId.HasValue)
        {
            var parent = await GetByIdAsync(node.ParentId.Value);
            if (parent != null)
            {
                node.DatabasePath = $"{parent.DatabasePath}/{node.Name}";
                node.Level = parent.Level + 1;
            }
        }
        else
        {
            node.DatabasePath = $"/{node.Name}";
            node.Level = 1;
        }

        _logger.Debug($"准备插入数据库: Name={node.Name}, NodeType={node.NodeType}, Level={node.Level}");

        // 临时清空 UserData 和 UserDataJson 避免序列化问题
        node.UserData = null;
        node.UserDataJson = null;

        try
        {
            var insertedId = await YData.InsertAsync(node);
            node.Id = insertedId;
            _logger.Info($"节点添加成功: {node.Name}, ID: {insertedId}");
        }
        catch (Exception ex)
        {
            _logger.Error($"数据库插入失败: {ex.Message}");
            throw;
        }

        return node;
    }

    #endregion

    #region 修改操作

    /// <summary>
    /// 更新节点
    /// </summary>
    public async Task<bool> UpdateAsync(TreeNodeData node)
    {
        _logger.Debug($"更新节点: {node.Name}, ID: {node.Id}");

        // 更新修改时间
        node.ModifiedAt = DateTime.Now;

        var affectedRows = await YData.UpdateAsync(node);
        var success = affectedRows > 0;

        if (success)
        {
            _logger.Info($"节点更新成功: {node.Name}");
        }
        else
        {
            _logger.Warning($"节点更新失败: {node.Name}");
        }

        return success;
    }

    #endregion

    #region 删除操作

    /// <summary>
    /// 删除节点
    /// </summary>
    public async Task<bool> DeleteAsync(int id)
    {
        _logger.Debug($"删除节点，ID: {id}");

        // 检查是否有子节点
        var children = await GetChildrenAsync(id);
        if (children.Count > 0)
        {
            _logger.Warning($"无法删除节点，存在 {children.Count} 个子节点");
            return false;
        }

        var affectedRows = await YData.DeleteAsync<TreeNodeData>(id);
        var success = affectedRows > 0;

        if (success)
        {
            _logger.Info($"节点删除成功，ID: {id}");
        }
        else
        {
            _logger.Warning($"节点删除失败，ID: {id}");
        }

        return success;
    }

    #endregion

    #region 辅助功能

    /// <summary>
    /// 构建树形结构
    /// </summary>
    private List<TreeNodeData> BuildTreeStructure(List<TreeNodeData> allNodes)
    {
        var nodeDict = allNodes.ToDictionary(n => n.Id, n => n);
        var rootNodes = new List<TreeNodeData>();

        foreach (var node in allNodes)
        {
            // 清空子节点集合
            node.YChildren.Clear();
            node.YParent = null;

            if (node.ParentId == null)
            {
                // 根节点
                rootNodes.Add(node);
            }
            else if (nodeDict.TryGetValue(node.ParentId.Value, out var parent))
            {
                // 设置父子关系
                node.YParent = parent;
                parent.YChildren.Add(node);
            }
        }

        // 对所有节点的子节点按SortOrder排序
        SortChildrenRecursively(rootNodes);

        // 对根节点（年份）进行智能排序
        return rootNodes.OrderBy(n =>
        {
            if (int.TryParse(n.Name, out int year))
            {
                return year; // 年份按数值排序（2017, 2018, 2019...）
            }

            return int.MaxValue; // 非数字年份排到最后
        }).ThenBy(n => n.Name).ToList();
    }

    /// <summary>
    /// 递归对所有子节点按SortOrder排序
    /// 确保项目和栋号都按照拖拽设置的顺序显示
    /// </summary>
    private void SortChildrenRecursively(List<TreeNodeData> nodes)
    {
        foreach (var node in nodes)
        {
            if (node.YChildren.Count > 0)
            {
                // 对子节点按SortOrder排序
                var sortedChildren = node.YChildren
                    .OrderBy(c => c.SortOrder)
                    .ThenBy(c => c.Name) // SortOrder相同时按名称排序
                    .ToList();

                // 清空原集合并重新添加排序后的子节点
                node.YChildren.Clear();
                foreach (var child in sortedChildren)
                {
                    node.YChildren.Add(child);
                }

                // 递归处理子节点的子节点 - 只处理 TreeNodeData 类型的子节点
                var typedChildren = node.YChildren.OfType<TreeNodeData>().ToList();
                if (typedChildren.Count > 0)
                {
                    SortChildrenRecursively(typedChildren);
                }
            }
        }
    }

    #endregion

    #region 拖拽操作方法

    /// <summary>
    /// 处理节点拖拽操作
    /// 根据源节点和目标节点的Level关系自动判断操作类型
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <returns>操作结果</returns>
    public async Task<bool> HandleDragDropAsync(TreeNodeData sourceNode, TreeNodeData targetNode)
    {
        try
        {
            _logger.Info($"开始处理拖拽操作: {sourceNode.Name}({sourceNode.Level}) → {targetNode.Name}({targetNode.Level})");

            // 根据Level关系判断操作类型
            if (sourceNode.Level > targetNode.Level)
            {
                // 下级拖拽到上级：改变依赖关系（父子关系）
                return await ChangeParentAsync(sourceNode, targetNode);
            }
            else if (sourceNode.Level == targetNode.Level)
            {
                // 同级拖拽：改变排列顺序
                return await ChangeSortOrderAsync(sourceNode, targetNode);
            }
            else
            {
                // 上级拖拽到下级：非法操作
                _logger.Warning($"非法拖拽操作：上级节点不能拖拽到下级节点");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"拖拽操作失败: {ex}");
            return false;
        }
    }

    /// <summary>
    /// 改变节点的父子关系
    /// 例如：项目拖拽到年份下，栋号拖拽到项目下
    /// </summary>
    /// <param name="sourceNode">源节点（子节点）</param>
    /// <param name="newParent">新父节点</param>
    /// <returns>操作结果</returns>
    public async Task<bool> ChangeParentAsync(TreeNodeData sourceNode, TreeNodeData newParent)
    {
        try
        {
            // 验证业务规则
            if (!ValidateParentChildRelation(sourceNode, newParent))
            {
                return false;
            }

            // 检查重复名称
            if (await HasDuplicateNameAsync(sourceNode.Name, newParent.Id))
            {
                _logger.Warning($"目标父节点下已存在同名节点: {sourceNode.Name}");
                return false;
            }

            // 更新节点信息
            sourceNode.ParentId = newParent.Id;
            sourceNode.Level = newParent.Level + 1;

            // 获取新父节点下的子节点数量，设置为最后一个
            var siblings = await GetChildrenAsync(newParent.Id);
            sourceNode.SortOrder = siblings.Count + 1;

            // 保存到数据库
            await UpdateAsync(sourceNode);

            _logger.Info($"成功改变父子关系: {sourceNode.Name} → {newParent.Name}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"改变父子关系失败: {ex}");
            return false;
        }
    }

    /// <summary>
    /// 改变同级节点的排序
    /// 例如：项目A和项目B交换位置，栋号1#和2#交换位置
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <returns>操作结果</returns>
    public async Task<bool> ChangeSortOrderAsync(TreeNodeData sourceNode, TreeNodeData targetNode)
    {
        try
        {
            // 验证是否为同级节点
            if (sourceNode.Level != targetNode.Level || sourceNode.ParentId != targetNode.ParentId)
            {
                _logger.Warning("只能对同级节点进行排序操作");
                return false;
            }

            // 交换SortOrder
            var sourceSortOrder = sourceNode.SortOrder;
            var targetSortOrder = targetNode.SortOrder;

            sourceNode.SortOrder = targetSortOrder;
            targetNode.SortOrder = sourceSortOrder;

            // 保存到数据库
            await UpdateAsync(sourceNode);
            await UpdateAsync(targetNode);

            _logger.Info(
                $"成功交换排序: {sourceNode.Name}({sourceSortOrder}→{targetSortOrder}) ↔ {targetNode.Name}({targetSortOrder}→{sourceSortOrder})");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"改变排序失败: {ex}");
            return false;
        }
    }

    /// <summary>
    /// 验证父子关系的业务规则
    /// </summary>
    /// <param name="child">子节点</param>
    /// <param name="parent">父节点</param>
    /// <returns>是否符合业务规则</returns>
    private bool ValidateParentChildRelation(TreeNodeData child, TreeNodeData parent)
    {
        // 年份(1) 可以包含 项目(2)
        // 项目(2) 可以包含 栋号(3)
        // 栋号(3) 不能包含任何子节点

        switch (parent.Level)
        {
            case 1: // 年份
                return child.Level == 2; // 只能包含项目
            case 2: // 项目
                return child.Level == 3; // 只能包含栋号
            case 3: // 栋号
                return false; // 不能包含子节点
            default:
                return false;
        }
    }

    /// <summary>
    /// 检查指定父节点下是否存在同名子节点
    /// </summary>
    /// <param name="name">节点名称</param>
    /// <param name="parentId">父节点ID</param>
    /// <returns>是否存在重复</returns>
    private async Task<bool> HasDuplicateNameAsync(string name, int parentId)
    {
        var existing = await YData.Select<TreeNodeData>()
            .Where(n => n.ParentId == parentId && n.Name == name)
            .FirstAsync();

        return existing != null;
    }

    #endregion

    #region 通用查找方法

    /// <summary>
    /// 在树结构中递归查找指定ID的节点
    /// </summary>
    /// <param name="rootNodes">根节点集合</param>
    /// <param name="nodeId">要查找的节点ID</param>
    /// <returns>找到的节点，如果没找到返回null</returns>
    public TreeNodeData? FindNodeInTree(List<TreeNodeData> rootNodes, int nodeId)
    {
        return rootNodes.FindTreeNodeRecursive(nodeId);
    }

    /// <summary>
    /// 在整个数据库中查找指定ID的节点（如果内存中没有）
    /// </summary>
    /// <param name="nodeId">要查找的节点ID</param>
    /// <returns>找到的节点，如果没找到返回null</returns>
    public async Task<TreeNodeData?> FindNodeByIdAsync(int nodeId)
    {
        try
        {
            var node = await YData.Select<TreeNodeData>()
                .Where(n => n.Id == nodeId)
                .FirstAsync();

            return node;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取节点的完整路径（从根到当前节点）
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点路径列表</returns>
    public async Task<List<TreeNodeData>> GetNodePathAsync(int nodeId)
    {
        var path = new List<TreeNodeData>();
        var currentNode = await FindNodeByIdAsync(nodeId);

        while (currentNode != null)
        {
            path.Insert(0, currentNode);
            if (currentNode.ParentId.HasValue)
            {
                currentNode = await FindNodeByIdAsync(currentNode.ParentId.Value);
            }
            else
            {
                break;
            }
        }

        return path;
    }

    #endregion
}