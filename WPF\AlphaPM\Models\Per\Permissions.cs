namespace AlphaPM.Models.Per;

/// <summary>
/// 软件级权限管理系统
/// 定义整个AlphaPM软件的权限常量和权限级别
/// </summary>
/// <remarks>
/// 🔐 软件级权限设计原则：
/// - 权限级别：数字越小权限越高（1=超级管理员, 5=普通用户）
/// - 模块权限：控制软件功能模块的访问权限
/// - 数据权限：控制数据访问范围（ALL/DEPARTMENT/SELF）
/// - 跨项目权限：管理多个项目的访问权限
///
/// 🏗️ 软件权限层级：
/// 1. 超级管理员：所有软件功能 + 系统管理
/// 2. 软件管理员：软件功能管理 + 用户管理
/// 3. 项目总监：项目管理 + 人员管理 
/// 4. 项目经理：项目管理 + 部门数据
/// 5. 设计师：设计功能 + 个人数据
/// 6. 普通用户：只读权限
///
/// 💡 与项目级权限的区别：
/// - 软件级：控制软件功能模块的访问（如DWG管理、CAD工具等）
/// - 项目级：控制具体项目内的数据和操作权限
/// </remarks>
public static class Permissions
{
    #region 软件级权限级别常量

    /// <summary>
    /// 超级管理员权限级别 - 系统最高权限
    /// </summary>
    public const int LEVEL_SUPER_ADMIN = 1;

    /// <summary>
    /// 软件管理员权限级别 - 软件功能管理
    /// </summary>
    public const int LEVEL_SOFTWARE_ADMIN = 2;

    /// <summary>
    /// 项目总监权限级别 - 项目管理
    /// </summary>
    public const int LEVEL_SUPERVISOR = 3;

    /// <summary>
    /// 项目经理权限级别 - 项目执行
    /// </summary>
    public const int LEVEL_MANAGER = 4;

    /// <summary>
    /// 设计师权限级别 - 设计功能
    /// </summary>
    public const int LEVEL_DESIGNER = 5;

    /// <summary>
    /// 普通用户权限级别 - 基础查看
    /// </summary>
    public const int LEVEL_USER = 6;

    #endregion

    #region 数据权限范围

    /// <summary>
    /// 全部数据访问权限
    /// </summary>
    public const string DATA_SCOPE_ALL = "ALL";

    /// <summary>
    /// 部门数据访问权限
    /// </summary>
    public const string DATA_SCOPE_DEPARTMENT = "DEPARTMENT";

    /// <summary>
    /// 个人数据访问权限
    /// </summary>
    public const string DATA_SCOPE_SELF = "SELF";

    #endregion

    #region 软件核心模块权限

    /// <summary>
    /// CAD工具模块 - 访问
    /// </summary>
    public const string CAD_TOOLS_ACCESS = "CAD_TOOLS_ACCESS";

    /// <summary>
    /// CAD工具模块 - 管理
    /// </summary>
    public const string CAD_TOOLS_MANAGE = "CAD_TOOLS_MANAGE";

    /// <summary>
    /// 文件管理模块 - 访问
    /// </summary>
    public const string FILE_MANAGER_ACCESS = "FILE_MANAGER_ACCESS";

    /// <summary>
    /// 文件管理模块 - 管理
    /// </summary>
    public const string FILE_MANAGER_MANAGE = "FILE_MANAGER_MANAGE";

    /// <summary>
    /// 数据库管理 - 访问
    /// </summary>
    public const string DATABASE_ACCESS = "DATABASE_ACCESS";

    /// <summary>
    /// 数据库管理 - 管理
    /// </summary>
    public const string DATABASE_MANAGE = "DATABASE_MANAGE";

    #endregion

    #region DWG管理模块权限

    /// <summary>
    /// DWG管理 - 查看
    /// </summary>
    public const string DWG_VIEW = "DWG_VIEW";

    /// <summary>
    /// DWG管理 - 管理（包含增删改）
    /// </summary>
    public const string DWG_MANAGE = "DWG_MANAGE";

    #endregion

    #region 项目管理模块权限

    /// <summary>
    /// 项目 - 查看
    /// </summary>
    public const string PROJECT_VIEW = "PROJECT_VIEW";

    /// <summary>
    /// 项目 - 创建
    /// </summary>
    public const string PROJECT_CREATE = "PROJECT_CREATE";

    /// <summary>
    /// 项目 - 编辑
    /// </summary>
    public const string PROJECT_EDIT = "PROJECT_EDIT";

    /// <summary>
    /// 项目 - 删除
    /// </summary>
    public const string PROJECT_DELETE = "PROJECT_DELETE";

    #endregion

    #region 建筑管理模块权限

    /// <summary>
    /// 建筑 - 查看
    /// </summary>
    public const string BUILDING_VIEW = "BUILDING_VIEW";

    /// <summary>
    /// 建筑 - 创建
    /// </summary>
    public const string BUILDING_CREATE = "BUILDING_CREATE";

    /// <summary>
    /// 建筑 - 编辑
    /// </summary>
    public const string BUILDING_EDIT = "BUILDING_EDIT";

    /// <summary>
    /// 建筑 - 删除
    /// </summary>
    public const string BUILDING_DELETE = "BUILDING_DELETE";

    #endregion

    #region 设计管理模块权限

    /// <summary>
    /// 设计 - 查看
    /// </summary>
    public const string DESIGN_VIEW = "DESIGN_VIEW";

    /// <summary>
    /// 设计 - 创建
    /// </summary>
    public const string DESIGN_CREATE = "DESIGN_CREATE";

    /// <summary>
    /// 设计 - 编辑
    /// </summary>
    public const string DESIGN_EDIT = "DESIGN_EDIT";

    /// <summary>
    /// 设计 - 删除
    /// </summary>
    public const string DESIGN_DELETE = "DESIGN_DELETE";

    #endregion

    #region 人员管理模块权限

    /// <summary>
    /// 人员管理 - 查看
    /// </summary>
    public const string PERSONNEL_VIEW = "PERSONNEL_VIEW";

    /// <summary>
    /// 人员管理 - 管理（包含增删改）
    /// </summary>
    public const string PERSONNEL_MANAGE = "PERSONNEL_MANAGE";

    #endregion

    #region 系统管理模块权限

    /// <summary>
    /// 系统设置
    /// </summary>
    public const string SYSTEM_SETTINGS = "SYSTEM_SETTINGS";

    /// <summary>
    /// 权限管理
    /// </summary>
    public const string PERMISSION_MANAGE = "PERMISSION_MANAGE";

    /// <summary>
    /// 日志查看
    /// </summary>
    public const string LOG_VIEW = "LOG_VIEW";

    #endregion

    #region 软件级权限模板 - 按层级组织

    /// <summary>
    /// 1级权限模板 - 超级管理员：所有软件功能 + 系统管理
    /// </summary>
    public static class SuperAdminTemplate
    {
        public const int Level = LEVEL_SUPER_ADMIN;
        public const string Role = "超级管理员";
        public const string DataScope = DATA_SCOPE_ALL;
        public const bool IsSystemAdmin = true;

        public static string[] GetModules() => new[]
        {
            // 所有权限
            "ALL"
        };

        public static string GetModulesString() => string.Join(",", GetModules());
    }

    /// <summary>
    /// 2级权限模板 - 软件管理员：软件功能管理 + 用户管理
    /// </summary>
    public static class SoftwareAdminTemplate
    {
        public const int Level = LEVEL_SOFTWARE_ADMIN;
        public const string Role = "软件管理员";
        public const string DataScope = DATA_SCOPE_ALL;
        public const bool IsSystemAdmin = false;

        public static string[] GetModules() => new[]
        {
            // 软件核心管理
            CAD_TOOLS_MANAGE, FILE_MANAGER_MANAGE, DATABASE_MANAGE,

            // DWG管理
            DWG_MANAGE,

            // 项目全权限
            PROJECT_VIEW, PROJECT_CREATE, PROJECT_EDIT, PROJECT_DELETE,

            // 建筑全权限
            BUILDING_VIEW, BUILDING_CREATE, BUILDING_EDIT, BUILDING_DELETE,

            // 设计全权限
            DESIGN_VIEW, DESIGN_CREATE, DESIGN_EDIT, DESIGN_DELETE,

            // 人员管理
            PERSONNEL_MANAGE,

            // 系统功能
            SYSTEM_SETTINGS, LOG_VIEW
        };

        public static string GetModulesString() => string.Join(",", GetModules());
    }

    /// <summary>
    /// 3级权限模板 - 项目总监：项目管理 + 人员管理
    /// </summary>
    public static class SupervisorTemplate
    {
        public const int Level = LEVEL_SUPERVISOR;
        public const string Role = "项目总监";
        public const string DataScope = DATA_SCOPE_ALL;
        public const bool IsSystemAdmin = false;

        public static string[] GetModules() => new[]
        {
            // 软件核心访问
            CAD_TOOLS_ACCESS, FILE_MANAGER_ACCESS, DATABASE_ACCESS,

            // DWG管理
            DWG_MANAGE,

            // 项目全权限
            PROJECT_VIEW, PROJECT_CREATE, PROJECT_EDIT, PROJECT_DELETE,

            // 建筑全权限
            BUILDING_VIEW, BUILDING_CREATE, BUILDING_EDIT, BUILDING_DELETE,

            // 设计全权限
            DESIGN_VIEW, DESIGN_CREATE, DESIGN_EDIT, DESIGN_DELETE,

            // 人员管理
            PERSONNEL_MANAGE,

            // 日志查看
            LOG_VIEW
        };

        public static string GetModulesString() => string.Join(",", GetModules());
    }

    /// <summary>
    /// 4级权限模板 - 项目经理：项目管理 + 部门数据
    /// </summary>
    public static class ManagerTemplate
    {
        public const int Level = LEVEL_MANAGER;
        public const string Role = "项目经理";
        public const string DataScope = DATA_SCOPE_DEPARTMENT;
        public const bool IsSystemAdmin = false;

        public static string[] GetModules() => new[]
        {
            // 软件核心访问
            CAD_TOOLS_ACCESS, FILE_MANAGER_ACCESS,

            // DWG管理
            DWG_MANAGE,

            // 项目管理权限（无删除）
            PROJECT_VIEW, PROJECT_CREATE, PROJECT_EDIT,

            // 建筑管理权限（无删除）
            BUILDING_VIEW, BUILDING_CREATE, BUILDING_EDIT,

            // 设计查看
            DESIGN_VIEW,

            // 人员查看
            PERSONNEL_VIEW
        };

        public static string GetModulesString() => string.Join(",", GetModules());
    }

    /// <summary>
    /// 5级权限模板 - 设计师：设计功能 + 个人数据
    /// </summary>
    public static class DesignerTemplate
    {
        public const int Level = LEVEL_DESIGNER;
        public const string Role = "设计师";
        public const string DataScope = DATA_SCOPE_SELF;
        public const bool IsSystemAdmin = false;

        public static string[] GetModules() => new[]
        {
            // CAD工具访问
            CAD_TOOLS_ACCESS,

            // DWG查看
            DWG_VIEW,

            // 项目查看
            PROJECT_VIEW,

            // 建筑查看
            BUILDING_VIEW,

            // 设计权限
            DESIGN_VIEW, DESIGN_CREATE, DESIGN_EDIT
        };

        public static string GetModulesString() => string.Join(",", GetModules());
    }

    /// <summary>
    /// 6级权限模板 - 普通用户：只读权限
    /// </summary>
    public static class UserTemplate
    {
        public const int Level = LEVEL_USER;
        public const string Role = "普通用户";
        public const string DataScope = DATA_SCOPE_SELF;
        public const bool IsSystemAdmin = false;

        public static string[] GetModules() => new[]
        {
            // 基础查看权限
            DWG_VIEW,
            PROJECT_VIEW,
            BUILDING_VIEW,
            DESIGN_VIEW
        };

        public static string GetModulesString() => string.Join(",", GetModules());
    }

    #endregion

    #region 兼容性方法 - 保持向后兼容

    /// <summary>
    /// 获取软件管理员权限模块（兼容性方法）
    /// </summary>
    public static string[] GetSoftwareAdminModules() => SoftwareAdminTemplate.GetModules();

    /// <summary>
    /// 获取项目总监权限模块（兼容性方法）
    /// </summary>
    public static string[] GetSupervisorModules() => SupervisorTemplate.GetModules();

    /// <summary>
    /// 获取项目经理权限模块（兼容性方法）
    /// </summary>
    public static string[] GetManagerModules() => ManagerTemplate.GetModules();

    /// <summary>
    /// 获取设计师权限模块（兼容性方法）
    /// </summary>
    public static string[] GetDesignerModules() => DesignerTemplate.GetModules();

    /// <summary>
    /// 获取普通用户权限模块（兼容性方法）
    /// </summary>
    public static string[] GetUserModules() => UserTemplate.GetModules();

    #endregion

    #region 权限模板获取方法

    /// <summary>
    /// 根据权限级别获取权限模板
    /// </summary>
    /// <param name="level">权限级别</param>
    /// <returns>权限模板信息</returns>
    public static PermissionTemplate GetPermissionTemplate(int level)
    {
        return level switch
        {
            LEVEL_SUPER_ADMIN => new PermissionTemplate
            {
                Level = SuperAdminTemplate.Level,
                Role = SuperAdminTemplate.Role,
                DataScope = SuperAdminTemplate.DataScope,
                IsSystemAdmin = SuperAdminTemplate.IsSystemAdmin,
                Modules = SuperAdminTemplate.GetModules(),
                ModulesString = SuperAdminTemplate.GetModulesString()
            },
            LEVEL_SOFTWARE_ADMIN => new PermissionTemplate
            {
                Level = SoftwareAdminTemplate.Level,
                Role = SoftwareAdminTemplate.Role,
                DataScope = SoftwareAdminTemplate.DataScope,
                IsSystemAdmin = SoftwareAdminTemplate.IsSystemAdmin,
                Modules = SoftwareAdminTemplate.GetModules(),
                ModulesString = SoftwareAdminTemplate.GetModulesString()
            },
            LEVEL_SUPERVISOR => new PermissionTemplate
            {
                Level = SupervisorTemplate.Level,
                Role = SupervisorTemplate.Role,
                DataScope = SupervisorTemplate.DataScope,
                IsSystemAdmin = SupervisorTemplate.IsSystemAdmin,
                Modules = SupervisorTemplate.GetModules(),
                ModulesString = SupervisorTemplate.GetModulesString()
            },
            LEVEL_MANAGER => new PermissionTemplate
            {
                Level = ManagerTemplate.Level,
                Role = ManagerTemplate.Role,
                DataScope = ManagerTemplate.DataScope,
                IsSystemAdmin = ManagerTemplate.IsSystemAdmin,
                Modules = ManagerTemplate.GetModules(),
                ModulesString = ManagerTemplate.GetModulesString()
            },
            LEVEL_DESIGNER => new PermissionTemplate
            {
                Level = DesignerTemplate.Level,
                Role = DesignerTemplate.Role,
                DataScope = DesignerTemplate.DataScope,
                IsSystemAdmin = DesignerTemplate.IsSystemAdmin,
                Modules = DesignerTemplate.GetModules(),
                ModulesString = DesignerTemplate.GetModulesString()
            },
            LEVEL_USER => new PermissionTemplate
            {
                Level = UserTemplate.Level,
                Role = UserTemplate.Role,
                DataScope = UserTemplate.DataScope,
                IsSystemAdmin = UserTemplate.IsSystemAdmin,
                Modules = UserTemplate.GetModules(),
                ModulesString = UserTemplate.GetModulesString()
            },
            _ => new PermissionTemplate
            {
                Level = LEVEL_USER,
                Role = "未知权限",
                DataScope = DATA_SCOPE_SELF,
                IsSystemAdmin = false,
                Modules = UserTemplate.GetModules(),
                ModulesString = UserTemplate.GetModulesString()
            }
        };
    }

    /// <summary>
    /// 根据角色名称获取权限模板
    /// </summary>
    /// <param name="role">角色名称</param>
    /// <returns>权限模板信息</returns>
    public static PermissionTemplate GetPermissionTemplateByRole(string role)
    {
        return role switch
        {
            "超级管理员" or "系统管理员" => GetPermissionTemplate(LEVEL_SUPER_ADMIN),
            "软件管理员" => GetPermissionTemplate(LEVEL_SOFTWARE_ADMIN),
            "项目总监" => GetPermissionTemplate(LEVEL_SUPERVISOR),
            "项目经理" => GetPermissionTemplate(LEVEL_MANAGER),
            "设计师" => GetPermissionTemplate(LEVEL_DESIGNER),
            "普通用户" => GetPermissionTemplate(LEVEL_USER),
            _ => GetPermissionTemplate(LEVEL_USER)
        };
    }

    #endregion

    #region 权限检查辅助方法

    /// <summary>
    /// 检查权限级别是否满足要求
    /// </summary>
    /// <param name="userLevel">用户权限级别</param>
    /// <param name="requiredLevel">要求的权限级别</param>
    /// <returns>是否满足权限要求</returns>
    public static bool CheckPermissionLevel(int userLevel, int requiredLevel)
    {
        return userLevel <= requiredLevel; // 数字越小权限越高
    }

    /// <summary>
    /// 获取权限级别描述
    /// </summary>
    /// <param name="level">权限级别</param>
    /// <returns>权限级别描述</returns>
    public static string GetPermissionLevelDescription(int level)
    {
        return level switch
        {
            LEVEL_SUPER_ADMIN => "超级管理员",
            LEVEL_SOFTWARE_ADMIN => "软件管理员",
            LEVEL_SUPERVISOR => "项目总监",
            LEVEL_MANAGER => "项目经理",
            LEVEL_DESIGNER => "设计师",
            LEVEL_USER => "普通用户",
            _ => "未知权限"
        };
    }

    /// <summary>
    /// 获取数据权限描述
    /// </summary>
    /// <param name="dataScope">数据权限范围</param>
    /// <returns>数据权限描述</returns>
    public static string GetDataScopeDescription(string dataScope)
    {
        return dataScope switch
        {
            DATA_SCOPE_ALL => "全部数据",
            DATA_SCOPE_DEPARTMENT => "部门数据",
            DATA_SCOPE_SELF => "个人数据",
            _ => "未知权限"
        };
    }

    #endregion
}

/// <summary>
/// 权限模板数据结构
/// </summary>
public class PermissionTemplate
{
    /// <summary>
    /// 权限级别
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// 数据权限范围
    /// </summary>
    public string DataScope { get; set; } = string.Empty;

    /// <summary>
    /// 是否为系统管理员
    /// </summary>
    public bool IsSystemAdmin { get; set; }

    /// <summary>
    /// 权限模块数组
    /// </summary>
    public string[] Modules { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 权限模块字符串（逗号分隔）
    /// </summary>
    public string ModulesString { get; set; } = string.Empty;

    /// <summary>
    /// 权限描述
    /// </summary>
    public string Description => $"{Role} (Level {Level}) - {Permissions.GetDataScopeDescription(DataScope)}";
}