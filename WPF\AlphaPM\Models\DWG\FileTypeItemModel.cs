﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CommunityToolkit.Mvvm.ComponentModel;

namespace AlphaPM.Models.DWG;

/// <summary>
/// 文件类型模型
/// </summary>
[Table("FileTypeItemModel")]
public partial class FileTypeItemModel : ObservableObject
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [ObservableProperty]
    [Description("节点ID")]
    public partial int Id { get; set; }


    [Description("名称")]
    [ObservableProperty]
    public partial string Name { get; set; } = string.Empty;

    [Description("说明")]
    [ObservableProperty]
    public partial string Description { get; set; } = string.Empty;

    [Description("使用")]
    [ObservableProperty]
    public partial bool IsEnabled { get; set; } = true;

    [Description("DWG类型")]
    [ObservableProperty]
    public partial bool IsDWG { get; set; } = true;
    
    [Description("通用")]
    [ObservableProperty]
    public partial bool IsGeneral { get; set; } = false;

    /// <summary>
    /// 关联的TreeNodeData ID（可为空，IsGeneral=true时为通用类型）
    /// </summary>
    [Description("关联的TreeNodeData ID")]
    [ObservableProperty]
    public partial int? TreeNodeId { get; set; }
}