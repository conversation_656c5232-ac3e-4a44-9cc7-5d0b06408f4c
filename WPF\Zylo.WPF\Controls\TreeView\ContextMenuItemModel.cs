﻿namespace Zylo.WPF.Controls.TreeView;

/// <summary>
/// 右键菜单项模型
/// </summary>
public class ContextMenuItemModel
{
    public string Header { get; set; }
    public System.Windows.Input.ICommand? Command { get; set; }
    public bool IsEnabled { get; set; }
    public bool IsSeparator => string.IsNullOrEmpty(Header) && Command == null;

    public ContextMenuItemModel(string header, System.Windows.Input.ICommand? command, bool isEnabled = true)
    {
        Header = header;
        Command = command;
        IsEnabled = isEnabled;
    }
}