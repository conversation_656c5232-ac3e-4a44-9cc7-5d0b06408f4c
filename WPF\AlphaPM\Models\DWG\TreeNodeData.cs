using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CommunityToolkit.Mvvm.ComponentModel;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Models.DWG;

/// <summary>
/// AlphaPM 树节点数据模型 - 继承 YTreeNodeData，包含DWG业务字段
/// </summary>
[Table("TreeNodeData")]
public partial class TreeNodeData : YTreeNodeData
{
    #region DWG业务字段

    /// <summary>
    /// 负责人/经理
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("负责人/经理")]
    public partial string? Manager { get; set; }

    /// <summary>
    /// 预算（万元）
    /// </summary>
    [Range(0, 999999)]
    [ObservableProperty]
    [Description("预算（万元）")]
    public partial decimal Budget { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [ObservableProperty]
    [Description("开始时间")]
    public partial DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [ObservableProperty]
    [Description("结束时间")]
    public partial DateTime? EndDate { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("项目状态")]
    public partial string? ProjectStatus { get; set; }

    /// <summary>
    /// 进度百分比
    /// </summary>
    [Range(0, 100)]
    [ObservableProperty]
    [Description("进度百分比")]
    public partial decimal ProgressPercentage { get; set; }

    /// <summary>
    /// 建筑类型（栋号节点使用）
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("建筑类型")]
    public partial string? BuildingType { get; set; }

    /// <summary>
    /// 建筑面积（平方米）
    /// </summary>
    [Range(0, 999999)]
    [ObservableProperty]
    [Description("建筑面积（平方米）")]
    public partial decimal BuildingArea { get; set; }

    /// <summary>
    /// 楼层数
    /// </summary>
    [Range(0, 200)]
    [ObservableProperty]
    [Description("楼层数")]
    public partial int FloorCount { get; set; }

    /// <summary>
    /// 建筑状态
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("建筑状态")]
    public partial string? BuildingStatus { get; set; }

    /// <summary>
    /// 设计师
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("设计师")]
    public partial string? Designer { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000)]
    [ObservableProperty]
    [Description("备注")]
    public partial string? Remarks { get; set; }

    // /// <summary>
    // /// 子节点集合（UI属性，不存储到数据库）
    // /// </summary>
    // [NotMapped]
    // [ObservableProperty]
    // [Description("子节点集合")]
    // public partial ObservableCollection<TreeNodeData> Children { get; set; } = new();
    //
    // /// <summary>
    // /// 父节点（UI属性，不存储到数据库）
    // /// </summary>
    // [NotMapped]
    // [ObservableProperty]
    // [Description("父节点")]
    // public partial TreeNodeData? Parent { get; set; }
    
    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public TreeNodeData()
    {
    }

    /// <summary>
    /// 带名称的构造函数
    /// </summary>
    /// <param name="name">节点名称</param>
    public TreeNodeData(string name) : base(name)
    {
    }

    #endregion

    #region 工厂方法

    /// <summary>
    /// 创建年份节点
    /// </summary>
    /// <param name="yearName">年份名称</param>
    /// <param name="manager">负责人</param>
    /// <returns>年份节点</returns>
    public static TreeNodeData CreateYear(string yearName, string? manager = null)
    {
        return new TreeNodeData(yearName)
        {
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            Manager = manager,
            Description = $"{yearName}年度项目管理",
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.CalendarLtr24
        };
    }

    /// <summary>
    /// 创建项目节点
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="parentId">父节点ID</param>
    /// <param name="manager">项目经理</param>
    /// <param name="budget">预算</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>项目节点</returns>
    public static TreeNodeData CreateProject(string projectName, int parentId, 
        string? manager = null, decimal budget = 0, DateTime? startDate = null, DateTime? endDate = null)
    {
        return new TreeNodeData(projectName)
        {
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            ParentId = parentId,
            Manager = manager,
            Budget = budget,
            StartDate = startDate,
            EndDate = endDate,
            ProjectStatus = "规划中",
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.BuildingBank24
        };
    }

    /// <summary>
    /// 创建栋号节点
    /// </summary>
    /// <param name="buildingName">栋号名称</param>
    /// <param name="parentId">父节点ID</param>
    /// <param name="buildingType">建筑类型</param>
    /// <param name="buildingArea">建筑面积</param>
    /// <param name="floorCount">楼层数</param>
    /// <param name="manager">负责人</param>
    /// <param name="designer">设计师</param>
    /// <returns>栋号节点</returns>
    public static TreeNodeData CreateBuilding(string buildingName, int parentId,
        string? buildingType = null, decimal buildingArea = 0, int floorCount = 0,
        string? manager = null, string? designer = null)
    {
        return new TreeNodeData(buildingName)
        {
            NodeType = DWGNodeTypes.Building,
            Level = 3,
            ParentId = parentId,
            BuildingType = buildingType,
            BuildingArea = buildingArea,
            FloorCount = floorCount,
            Manager = manager,
            Designer = designer,
            BuildingStatus = "设计中",
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.Building24
        };
    }

    #endregion

    #region 数据操作便捷方法

    /// <summary>
    /// 添加子节点（类型安全）
    /// </summary>
    /// <param name="child">要添加的子节点</param>
    /// <returns>当前节点，支持链式调用</returns>
    public TreeNodeData AddChild(TreeNodeData child)
    {
        if (child == null) return this;

        // 设置父子关系
        child.YParent = this;
        child.ParentId = this.Id;
        child.Level = this.Level + 1;

        // 添加到子节点集合
        if (!YChildren.Contains(child))
        {
            YChildren.Add(child);
        }

        return this;
    }

    /// <summary>
    /// 移除子节点
    /// </summary>
    /// <param name="child">要移除的子节点</param>
    /// <returns>是否移除成功</returns>
    public bool RemoveChild(TreeNodeData child)
    {
        if (child == null) return false;

        var removed = YChildren.Remove(child);
        if (removed)
        {
            child.YParent = null;
            child.ParentId = null;
        }

        return removed;
    }

    /// <summary>
    /// 获取强类型的子节点集合
    /// </summary>
    /// <returns>TreeNodeData 类型的子节点集合</returns>
    public IEnumerable<TreeNodeData> GetChildren()
    {
        return YChildren.OfType<TreeNodeData>();
    }

    /// <summary>
    /// 获取强类型的父节点
    /// </summary>
    /// <returns>TreeNodeData 类型的父节点</returns>
    public TreeNodeData? GetParent()
    {
        return YParent as TreeNodeData;
    }

    /// <summary>
    /// 设置父节点
    /// </summary>
    /// <param name="parent">新的父节点</param>
    /// <returns>当前节点，支持链式调用</returns>
    public TreeNodeData SetParent(TreeNodeData? parent)
    {
        // 从原父节点移除
        if (YParent is TreeNodeData oldParent)
        {
            oldParent.RemoveChild(this);
        }

        // 设置新父节点
        if (parent != null)
        {
            parent.AddChild(this);
        }
        else
        {
            YParent = null;
            ParentId = null;
            Level = 1;
        }

        return this;
    }

    /// <summary>
    /// 清空所有子节点
    /// </summary>
    /// <returns>当前节点，支持链式调用</returns>
    public TreeNodeData ClearChildren()
    {
        // 清除所有子节点的父引用
        foreach (var child in YChildren.OfType<TreeNodeData>().ToList())
        {
            child.YParent = null;
            child.ParentId = null;
        }

        YChildren.Clear();
        return this;
    }

    /// <summary>
    /// 查找子节点（按ID）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <returns>找到的子节点，如果没找到返回null</returns>
    public TreeNodeData? FindChild(int id)
    {
        return GetChildren().FirstOrDefault(c => c.Id == id);
    }

    /// <summary>
    /// 递归查找后代节点（按ID）
    /// </summary>
    /// <param name="id">节点ID</param>
    /// <returns>找到的后代节点，如果没找到返回null</returns>
    public TreeNodeData? FindDescendant(int id)
    {
        // 先查找直接子节点
        var directChild = FindChild(id);
        if (directChild != null) return directChild;

        // 递归查找子节点的后代
        foreach (var child in GetChildren())
        {
            var descendant = child.FindDescendant(id);
            if (descendant != null) return descendant;
        }

        return null;
    }

    /// <summary>
    /// 获取所有后代节点
    /// </summary>
    /// <returns>所有后代节点的集合</returns>
    public IEnumerable<TreeNodeData> GetAllDescendants()
    {
        var descendants = new List<TreeNodeData>();

        foreach (var child in GetChildren())
        {
            descendants.Add(child);
            descendants.AddRange(child.GetAllDescendants());
        }

        return descendants;
    }

    /// <summary>
    /// 获取节点路径（从根到当前节点）
    /// </summary>
    /// <returns>节点路径字符串</returns>
    public string GetPath()
    {
        var pathParts = new List<string>();
        var current = this;

        while (current != null)
        {
            pathParts.Insert(0, current.Name);
            current = current.GetParent();
        }

        return "/" + string.Join("/", pathParts);
    }

    #endregion
}
