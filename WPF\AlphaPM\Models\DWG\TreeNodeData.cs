using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CommunityToolkit.Mvvm.ComponentModel;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Models.DWG;

/// <summary>
/// AlphaPM 树节点数据模型 - 继承 YTreeNodeData，包含DWG业务字段
/// </summary>
[Table("TreeNodeData")]
public partial class TreeNodeData : YTreeNodeData
{
    #region DWG业务字段

    /// <summary>
    /// 负责人/经理
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("负责人/经理")]
    public partial string? Manager { get; set; }

    /// <summary>
    /// 预算（万元）
    /// </summary>
    [Range(0, 999999)]
    [ObservableProperty]
    [Description("预算（万元）")]
    public partial decimal Budget { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [ObservableProperty]
    [Description("开始时间")]
    public partial DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [ObservableProperty]
    [Description("结束时间")]
    public partial DateTime? EndDate { get; set; }

    /// <summary>
    /// 项目状态
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("项目状态")]
    public partial string? ProjectStatus { get; set; }

    /// <summary>
    /// 进度百分比
    /// </summary>
    [Range(0, 100)]
    [ObservableProperty]
    [Description("进度百分比")]
    public partial decimal ProgressPercentage { get; set; }

    /// <summary>
    /// 建筑类型（栋号节点使用）
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("建筑类型")]
    public partial string? BuildingType { get; set; }

    /// <summary>
    /// 建筑面积（平方米）
    /// </summary>
    [Range(0, 999999)]
    [ObservableProperty]
    [Description("建筑面积（平方米）")]
    public partial decimal BuildingArea { get; set; }

    /// <summary>
    /// 楼层数
    /// </summary>
    [Range(0, 200)]
    [ObservableProperty]
    [Description("楼层数")]
    public partial int FloorCount { get; set; }

    /// <summary>
    /// 建筑状态
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("建筑状态")]
    public partial string? BuildingStatus { get; set; }

    /// <summary>
    /// 设计师
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("设计师")]
    public partial string? Designer { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000)]
    [ObservableProperty]
    [Description("备注")]
    public partial string? Remarks { get; set; }

    // /// <summary>
    // /// 子节点集合（UI属性，不存储到数据库）
    // /// </summary>
    // [NotMapped]
    // [ObservableProperty]
    // [Description("子节点集合")]
    // public partial ObservableCollection<TreeNodeData> Children { get; set; } = new();
    //
    // /// <summary>
    // /// 父节点（UI属性，不存储到数据库）
    // /// </summary>
    // [NotMapped]
    // [ObservableProperty]
    // [Description("父节点")]
    // public partial TreeNodeData? Parent { get; set; }
    
    #endregion

    #region 构造函数

    /// <summary>
    /// 默认构造函数
    /// </summary>
    public TreeNodeData()
    {
    }

    /// <summary>
    /// 带名称的构造函数
    /// </summary>
    /// <param name="name">节点名称</param>
    public TreeNodeData(string name) : base(name)
    {
    }

    #endregion

    #region 工厂方法

    /// <summary>
    /// 创建年份节点
    /// </summary>
    /// <param name="yearName">年份名称</param>
    /// <param name="manager">负责人</param>
    /// <returns>年份节点</returns>
    public static TreeNodeData CreateYear(string yearName, string? manager = null)
    {
        return new TreeNodeData(yearName)
        {
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            Manager = manager,
            Description = $"{yearName}年度项目管理",
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.CalendarLtr24
        };
    }

    /// <summary>
    /// 创建项目节点
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="parentId">父节点ID</param>
    /// <param name="manager">项目经理</param>
    /// <param name="budget">预算</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>项目节点</returns>
    public static TreeNodeData CreateProject(string projectName, int parentId, 
        string? manager = null, decimal budget = 0, DateTime? startDate = null, DateTime? endDate = null)
    {
        return new TreeNodeData(projectName)
        {
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            ParentId = parentId,
            Manager = manager,
            Budget = budget,
            StartDate = startDate,
            EndDate = endDate,
            ProjectStatus = "规划中",
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.BuildingBank24
        };
    }

    /// <summary>
    /// 创建栋号节点
    /// </summary>
    /// <param name="buildingName">栋号名称</param>
    /// <param name="parentId">父节点ID</param>
    /// <param name="buildingType">建筑类型</param>
    /// <param name="buildingArea">建筑面积</param>
    /// <param name="floorCount">楼层数</param>
    /// <param name="manager">负责人</param>
    /// <param name="designer">设计师</param>
    /// <returns>栋号节点</returns>
    public static TreeNodeData CreateBuilding(string buildingName, int parentId,
        string? buildingType = null, decimal buildingArea = 0, int floorCount = 0,
        string? manager = null, string? designer = null)
    {
        return new TreeNodeData(buildingName)
        {
            NodeType = DWGNodeTypes.Building,
            Level = 3,
            ParentId = parentId,
            BuildingType = buildingType,
            BuildingArea = buildingArea,
            FloorCount = floorCount,
            Manager = manager,
            Designer = designer,
            BuildingStatus = "设计中",
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.Building24
        };
    }

    #endregion

    // 注意：直接使用基类的 YParent 和 YChildren 属性
    // 它们已经有 [ObservableProperty] 特性，会自动触发通知
    // 不需要重写 Parent 和 Children 属性
}
