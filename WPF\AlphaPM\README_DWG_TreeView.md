# DWG管理 TreeView 三级层次结构实现

## 概述

基于 ZyloTreeView 可重用控件，实现了 DWG 项目管理的三级层次结构，支持年份、项目名和栋号的分层管理。

## 层次结构设计

### 三级层次
1. **顶层（年份）**：2017~2025
   - 节点类型：`"folder"`
   - 图标：📁
   - 功能：按年份组织项目

2. **二级（项目名）**：具体项目
   - 节点类型：`"folder"`
   - 图标：📁
   - 示例：万科城市花园、恒大翡翠华庭、碧桂园凤凰城

3. **三级（栋号）**：具体建筑
   - 节点类型：`"project"`
   - 图标：🏗️
   - 示例：1#楼、2#楼、A栋、B栋、商业楼

## 技术实现

### 核心文件

1. **ZyloTreeView.xaml.cs** - 可重用树形控件
   - 提供丰富的依赖属性支持
   - 支持数据绑定、搜索过滤、命令绑定
   - 使用 CommunityToolkit.MVVM 的最佳实践

2. **DWGManageViewModel.cs** - 业务逻辑
   - 创建三级层次数据结构
   - 根据节点类型进行不同处理
   - 使用 `partial void On{PropertyName}Changed()` 处理属性变化

3. **DWGNodeTypes.cs** - 节点类型管理
   - 定义节点类型常量
   - 提供节点类型判断方法
   - 支持节点层级识别

### 关键代码片段

#### 数据创建
```csharp
// 创建年份节点（顶层 - 文件夹类型）
var yearNode = TreeNodeFactory.CreateFolder(nodeId++, year, $"{year}年度项目");

// 创建项目节点（二级 - 文件夹类型）
var projectNode = TreeNodeFactory.CreateFolder(nodeId++, project.Name, project.Description);

// 创建栋号节点（三级 - 项目类型）
var buildingNode = TreeNodeFactory.CreateProject(nodeId++, building.Name, building.Description);
```

#### 节点选择处理
```csharp
partial void OnSelectedNodeChanged(TreeNodeData? value)
{
    if (value != null)
    {
        var nodeLevel = DWGNodeTypes.GetNodeLevel(value);
        
        switch (nodeLevel)
        {
            case 1: // 年份
                StatusMessage = $"选择年份: {value.Name} ({value.Children.Count}个项目)";
                LoadYearData(value);
                break;
                
            case 2: // 项目
                StatusMessage = $"选择项目: {value.Name} ({value.Children.Count}栋建筑)";
                LoadProjectData(value);
                break;
                
            case 3: // 栋号
                StatusMessage = $"选择栋号: {value.Name} - {value.Description}";
                LoadBuildingData(value);
                break;
        }
    }
}
```

## 功能特性

### 1. 类型区分
- 通过 `NodeType` 属性区分文件夹类型和项目类型
- 支持不同类型节点的不同处理逻辑
- 提供节点层级识别功能

### 2. 数据绑定
- 使用 `TreeData` 和 `FilteredTreeData` 双向绑定
- 支持选中节点的双向绑定
- 实时搜索过滤功能

### 3. 交互体验
- 默认展开2024年节点
- 显示子节点数量统计
- 状态栏实时反馈选择信息

### 4. 扩展性
- 易于添加新的年份和项目
- 支持自定义节点类型
- 可扩展的业务逻辑处理

## 使用方式

### XAML 配置
```xml
<controls:ZyloTreeView
    TreeData="{Binding TreeData}"
    FilteredTreeData="{Binding FilteredTreeData}"
    SelectedNode="{Binding SelectedNode, Mode=TwoWay}"
    FilterText="{Binding SearchKeyword, Mode=TwoWay}"
    NodeSelectionChangedCommand="{Binding NodeSelectionChangedCommand}"
    ShowSearchBox="True"
    ShowContextMenu="True"
    SearchPlaceholderText="🔍 搜索专业分类..."
    x:Name="MainTreeView" />
```

### ViewModel 配置
```csharp
[ObservableProperty]
private ObservableCollection<TreeNodeData> treeData = new();

[ObservableProperty]
private ObservableCollection<TreeNodeData> filteredTreeData = new();

[ObservableProperty]
private TreeNodeData? selectedNode;

[RelayCommand]
private void NodeSelectionChanged(object? selectedNode)
{
    // 处理节点选择变化
}
```

## 优势

1. **可重用性**：ZyloTreeView 控件可用于其他项目
2. **类型安全**：使用强类型的节点类型管理
3. **性能优化**：支持虚拟化和大数据集
4. **用户体验**：直观的三级层次结构
5. **扩展性**：易于添加新功能和业务逻辑

## 后续扩展

1. **文件管理**：选中栋号时加载对应的 DWG 文件列表
2. **图纸分类**：支持平面图、立面图、剖面图等分类
3. **版本控制**：支持文件版本管理
4. **权限控制**：根据用户权限显示不同内容
5. **统计分析**：项目进度统计和分析功能
