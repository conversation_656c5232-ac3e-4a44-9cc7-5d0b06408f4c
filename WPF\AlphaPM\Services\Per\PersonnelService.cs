using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AlphaPM.Models.Per;
using Zylo.YData;
using FreeSql;

namespace AlphaPM.Services.Per;

/// <summary>
/// 人员管理服务
/// 提供人员的增删改查和业务逻辑
/// </summary>
public class PersonnelService
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<PersonnelService>();

    public PersonnelService()
    {
    }

    #region 基础CRUD操作

    /// <summary>
    /// 获取所有人员
    /// </summary>
    public async Task<List<Personnel>> GetAllAsync()
    {
        try
        {
            _logger.Debug("获取所有人员");
            
            var personnel = await YData.Select<Personnel>()
                .OrderBy(p => p.Department)
                .OrderBy(p => p.Position)
                .OrderBy(p => p.Name)
                .ToListAsync();

            _logger.Debug($"获取到 {personnel.Count} 个人员");
            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"获取所有人员失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取人员
    /// </summary>
    public async Task<Personnel?> GetByIdAsync(int id)
    {
        try
        {
            _logger.Debug($"获取人员: {id}");
            
            var personnel = await YData.Select<Personnel>()
                .Where(p => p.Id == id)
                .FirstAsync();

            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"获取人员失败: {id} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 根据项目ID获取人员
    /// </summary>
    public async Task<List<Personnel>> GetByProjectIdAsync(int projectId)
    {
        try
        {
            _logger.Debug($"获取项目人员: {projectId}");
            
            var personnel = await YData.Select<Personnel>()
                .Where(p => p.ProjectId == projectId)
                .Where(p => p.IsActive)
                .OrderBy(p => p.PermissionLevel)
                .OrderBy(p => p.Name)
                .ToListAsync();

            _logger.Debug($"获取到 {personnel.Count} 个项目人员");
            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"获取项目人员失败: {projectId} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 创建人员
    /// </summary>
    public async Task<Personnel> CreateAsync(Personnel personnel)
    {
        try
        {
            _logger.Debug($"创建人员: {personnel.Name}");
            
            // 设置创建时间
            personnel.CreatedAt = DateTime.Now;
            personnel.ModifiedAt = DateTime.Now;
            
            // 验证数据
            if (!ValidatePersonnel(personnel))
            {
                throw new ArgumentException("人员数据验证失败");
            }

            var result = await YData.InsertAsync(personnel);
            // personnel.Id 会自动设置

            _logger.Info($"创建人员成功: {personnel.Name} (ID: {personnel.Id})");
            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"创建人员失败: {personnel.Name} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 更新人员
    /// </summary>
    public async Task<bool> UpdateAsync(Personnel personnel)
    {
        try
        {
            _logger.Debug($"更新人员: {personnel.Name} (ID: {personnel.Id})");
            
            // 设置修改时间
            personnel.ModifiedAt = DateTime.Now;
            
            // 验证数据
            if (!ValidatePersonnel(personnel))
            {
                throw new ArgumentException("人员数据验证失败");
            }

            var affectedRows = await YData.Update<Personnel>()
                .SetSource(personnel)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            if (success)
            {
                _logger.Info($"更新人员成功: {personnel.Name}");
            }
            else
            {
                _logger.Warning($"更新人员失败，未找到记录: {personnel.Id}");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.Error($"更新人员失败: {personnel.Name} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 删除人员（软删除）
    /// </summary>
    public async Task<bool> DeleteAsync(int id)
    {
        try
        {
            _logger.Debug($"删除人员: {id}");
            
            var affectedRows = await YData.Update<Personnel>()
                .Where(p => p.Id == id)
                .Set(p => p.IsActive, false)
                .Set(p => p.LeaveDate, DateTime.Now)
                .Set(p => p.ModifiedAt, DateTime.Now)
                .ExecuteAffrowsAsync();

            var success = affectedRows > 0;
            if (success)
            {
                _logger.Info($"删除人员成功: {id}");
            }
            else
            {
                _logger.Warning($"删除人员失败，未找到记录: {id}");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.Error($"删除人员失败: {id} - {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 查询操作

    /// <summary>
    /// 搜索人员
    /// </summary>
    public async Task<List<Personnel>> SearchAsync(string keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return await GetAllAsync();
            }

            _logger.Debug($"搜索人员: {keyword}");
            
            var personnel = await YData.Select<Personnel>()
                .Where(p => p.IsActive)
                .Where(p => p.Name.Contains(keyword) || 
                           p.Position.Contains(keyword) || 
                           p.Department.Contains(keyword) ||
                           p.EmployeeNumber.Contains(keyword))
                .OrderBy(p => p.Name)
                .ToListAsync();

            _logger.Debug($"搜索到 {personnel.Count} 个匹配的人员");
            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"搜索人员失败: {keyword} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 根据角色获取人员
    /// </summary>
    public async Task<List<Personnel>> GetByRoleAsync(string role)
    {
        try
        {
            _logger.Debug($"获取角色人员: {role}");
            
            var personnel = await YData.Select<Personnel>()
                .Where(p => p.IsActive)
                .Where(p => p.UserRole == role)
                .OrderBy(p => p.Name)
                .ToListAsync();

            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"获取角色人员失败: {role} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 根据权限级别获取人员
    /// </summary>
    public async Task<List<Personnel>> GetByPermissionLevelAsync(int maxLevel)
    {
        try
        {
            _logger.Debug($"获取权限级别人员: <= {maxLevel}");
            
            var personnel = await YData.Select<Personnel>()
                .Where(p => p.IsActive)
                .Where(p => p.PermissionLevel <= maxLevel)
                .OrderBy(p => p.PermissionLevel)
                .OrderBy(p => p.Name)
                .ToListAsync();

            return personnel;
        }
        catch (Exception ex)
        {
            _logger.Error($"获取权限级别人员失败: {maxLevel} - {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 业务逻辑

    /// <summary>
    /// 验证人员数据
    /// </summary>
    private bool ValidatePersonnel(Personnel personnel)
    {
        if (string.IsNullOrWhiteSpace(personnel.Name))
        {
            _logger.Warning("人员姓名不能为空");
            return false;
        }

        if (personnel.PermissionLevel < 1 || personnel.PermissionLevel > 10)
        {
            _logger.Warning($"权限级别无效: {personnel.PermissionLevel}");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 检查工号是否重复
    /// </summary>
    public async Task<bool> IsEmployeeNumberUniqueAsync(string employeeNumber, int excludeId = 0)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(employeeNumber))
                return true;

            var count = await YData.Select<Personnel>()
                .Where(p => p.EmployeeNumber == employeeNumber)
                .Where(p => p.Id != excludeId)
                .CountAsync();

            return count == 0;
        }
        catch (Exception ex)
        {
            _logger.Error($"检查工号唯一性失败: {employeeNumber} - {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 获取部门列表
    /// </summary>
    public async Task<List<string>> GetDepartmentsAsync()
    {
        try
        {
            var departments = await YData.Select<Personnel>()
                .Where(p => p.IsActive)
                .Where(p => !string.IsNullOrEmpty(p.Department))
                .GroupBy(p => p.Department)
                .ToListAsync(g => g.Key);

            return departments.OrderBy(d => d).ToList();
        }
        catch (Exception ex)
        {
            _logger.Error($"获取部门列表失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 获取职位列表
    /// </summary>
    public async Task<List<string>> GetPositionsAsync()
    {
        try
        {
            var positions = await YData.Select<Personnel>()
                .Where(p => p.IsActive)
                .Where(p => !string.IsNullOrEmpty(p.Position))
                .GroupBy(p => p.Position)
                .ToListAsync(g => g.Key);

            return positions.OrderBy(p => p).ToList();
        }
        catch (Exception ex)
        {
            _logger.Error($"获取职位列表失败: {ex.Message}");
            throw;
        }
    }

    #endregion
}
