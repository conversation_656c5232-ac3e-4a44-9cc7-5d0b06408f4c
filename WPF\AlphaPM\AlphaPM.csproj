﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Zylo.WPF\Zylo.WPF.csproj" />
    </ItemGroup>
    <ItemGroup>
        <!-- UI 框架 -->
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.7" />
        <PackageReference Include="Prism.Wpf" Version="8.1.97" />
        <PackageReference Include="Prism.DryIoc" Version="8.1.97" />
        <PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.7" />
        <PackageReference Include="WPF-UI" Version="4.0.3" />

        <!-- MVVM 工具包 -->
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />

        <!-- 依赖注入和主机 -->
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.6" />

        <!-- 拖拽功能 -->
        <PackageReference Include="gong-wpf-dragdrop" Version="4.0.0" />

        <!-- VB.NET 交互功能 -->
        <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />

    </ItemGroup>
    <ItemGroup>
        <!-- 🚀 引用核心包 -->
        <ProjectReference Include="..\Zylo.WPF\Zylo.WPF.csproj" />


    </ItemGroup>
    <ItemGroup>
      <Folder Include="Examples\" />
      <Folder Include="Tests\" />
      <Folder Include="ViewModels\Per\" />
      <Folder Include="Views\Per\" />
    </ItemGroup>
    <ItemGroup>
      <Page Update="Views\MainView.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Views\Settings\ThemeSettingsView.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
      <Page Update="Views\Settings\SettingsView.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
    </ItemGroup>
</Project>
