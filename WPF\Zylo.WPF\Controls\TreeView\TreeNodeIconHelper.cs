namespace Zylo.WPF.Controls.TreeView;

/// <summary>
/// 树节点图标帮助类
/// </summary>
public static class TreeNodeIconHelper
{
    /// <summary>
    /// 根据节点类型获取默认图标 - 扩展版本，支持更多类型
    /// </summary>
    public static string GetDefaultIconByType(string nodeType) => nodeType.ToLower() switch
    {
        // === 文件系统 ===
        "folder" => "📁",
        "file" => "📄",
        "document" => "📄",
        "text" => "📝",
        "image" => "🖼️",
        "photo" => "📸",
        "video" => "🎥",
        "audio" => "🎵",
        "music" => "🎶",
        "archive" => "📦",
        "zip" => "🗜️",
        "pdf" => "📕",
        "excel" => "📊",
        "word" => "📘",
        "powerpoint" => "📙",
        "csv" => "📋",
        "json" => "📋",
        "xml" => "📋",
        "config" => "⚙️",
        "settings" => "⚙️",
        "log" => "📜",
        
        // === 编程语言 ===
        "code" => "💻",
        "javascript" => "🟨",
        "js" => "🟨",
        "typescript" => "🔷",
        "ts" => "🔷",
        "python" => "🐍",
        "py" => "🐍",
        "java" => "☕",
        "csharp" => "🔷",
        "cs" => "🔷",
        "cpp" => "⚡",
        "c" => "⚡",
        "php" => "🐘",
        "ruby" => "💎",
        "go" => "🐹",
        "rust" => "🦀",
        "swift" => "🦉",
        "kotlin" => "🟣",
        "dart" => "🎯",
        "scala" => "🔴",
        "r" => "📊",
        "matlab" => "🔢",
        "sql" => "🗄️",
        
        // === Web开发 ===
        "html" => "🌐",
        "css" => "🎨",
        "sass" => "💅",
        "scss" => "💅",
        "less" => "💅",
        "react" => "⚛️",
        "vue" => "💚",
        "angular" => "🅰️",
        "nodejs" => "🟢",
        "npm" => "📦",
        "webpack" => "📦",
        "babel" => "🔄",
        
        // === 数据库 ===
        "database" => "🗄️",
        "mysql" => "🐬",
        "postgresql" => "🐘",
        "mongodb" => "🍃",
        "redis" => "🔴",
        "sqlite" => "💾",
        "oracle" => "🔶",
        "table" => "📊",
        "view" => "👁️",
        "procedure" => "⚙️",
        "function" => "🔧",
        "trigger" => "⚡",
        "index" => "🔍",
        
        // === 系统和网络 ===
        "network" => "🌐",
        "server" => "🖥️",
        "service" => "⚙️",
        "api" => "🔌",
        "endpoint" => "🎯",
        "url" => "🔗",
        "domain" => "🌍",
        "ip" => "🔢",
        "port" => "🚪",
        "protocol" => "📡",
        "ssh" => "🔐",
        "ftp" => "📁",
        "http" => "🌐",
        "https" => "🔒",
        
        // === 组织和人员 ===
        "user" => "👤",
        "admin" => "👑",
        "guest" => "👥",
        "group" => "👥",
        "team" => "👥",
        "department" => "🏬",
        "organization" => "🏢",
        "company" => "🏢",
        "branch" => "🌿",
        "office" => "🏢",
        "role" => "🎭",
        "permission" => "🔐",
        
        // === 项目管理 ===
        "project" => "📋",
        "task" => "✅",
        "subtask" => "📝",
        "milestone" => "🏁",
        "sprint" => "🏃",
        "epic" => "🎯",
        "story" => "📖",
        "bug" => "🐛",
        "feature" => "⭐",
        "enhancement" => "✨",
        "issue" => "❗",
        "ticket" => "🎫",
        "todo" => "☑️",
        "done" => "✅",
        "inprogress" => "🔄",
        "blocked" => "🚫",
        "review" => "👀",
        "testing" => "🧪",
        
        // === 内容和媒体 ===
        "note" => "📝",
        "memo" => "📝",
        "comment" => "💬",
        "message" => "💌",
        "email" => "📧",
        "notification" => "🔔",
        "alert" => "⚠️",
        "warning" => "⚠️",
        "error" => "❌",
        "success" => "✅",
        "info" => "ℹ️",
        "bookmark" => "🔖",
        "favorite" => "⭐",
        "star" => "⭐",
        "like" => "👍",
        "tag" => "🏷️",
        "label" => "🏷️",
        "category" => "📂",
        
        // === 工具和应用 ===
        "tool" => "🔧",
        "plugin" => "🔌",
        "extension" => "🧩",
        "widget" => "🧩",
        "component" => "🧩",
        "module" => "📦",
        "library" => "📚",
        "framework" => "🏗️",
        "template" => "📄",
        "theme" => "🎨",
        "style" => "💅",
        "layout" => "📐",
        "design" => "🎨",
        
        // === 安全和权限 ===
        "security" => "🔒",
        "key" => "🔑",
        "certificate" => "📜",
        "token" => "🎫",
        "password" => "🔐",
        "encryption" => "🔐",
        "firewall" => "🛡️",
        "shield" => "🛡️",
        "lock" => "🔒",
        "unlock" => "🔓",
        
        // === 时间和日期 ===
        "calendar" => "📅",
        "date" => "📅",
        "time" => "🕐",
        "clock" => "🕐",
        "schedule" => "📅",
        "event" => "📅",
        "meeting" => "👥",
        "appointment" => "📅",
        
        // === 状态和指示器 ===
        "status" => "📊",
        "health" => "💚",
        "monitor" => "📊",
        "dashboard" => "📊",
        "chart" => "📈",
        "graph" => "📊",
        "report" => "📊",
        "analytics" => "📈",
        "metrics" => "📊",
        "kpi" => "🎯",
        
        // === 默认 ===
        _ => "📌"
    };
}
