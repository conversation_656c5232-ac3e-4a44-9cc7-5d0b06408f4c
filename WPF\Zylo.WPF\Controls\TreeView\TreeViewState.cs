using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Zylo.WPF.Controls.TreeView;

/// <summary>
/// TreeView状态数据 - 用于JSON序列化保存和读取
/// </summary>
public class TreeViewState
{
    /// <summary>
    /// 展开的节点ID列表
    /// </summary>
    [JsonPropertyName("expandedNodeIds")]
    public List<int> ExpandedNodeIds { get; set; } = new();

    /// <summary>
    /// 选中的节点ID
    /// </summary>
    [JsonPropertyName("selectedNodeId")]
    public int? SelectedNodeId { get; set; }

    /// <summary>
    /// 搜索文本
    /// </summary>
    [JsonPropertyName("searchText")]
    public string SearchText { get; set; } = string.Empty;

    /// <summary>
    /// 是否使用大字体
    /// </summary>
    [JsonPropertyName("isLargeFont")]
    public bool IsLargeFont { get; set; }

    /// <summary>
    /// 保存时间
    /// </summary>
    [JsonPropertyName("savedAt")]
    public DateTime SavedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 版本号
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 备注信息
    /// </summary>
    [JsonPropertyName("notes")]
    public string Notes { get; set; } = string.Empty;
}

/// <summary>
/// TreeView状态管理器 - 负责状态的保存和读取
/// </summary>
public static class TreeViewStateManager
{
    private static readonly string StateFilePath = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
        "ZyloWPF",
        "TreeViewState.json"
    );

    /// <summary>
    /// 保存TreeView状态到JSON文件
    /// </summary>
    /// <param name="state">要保存的状态</param>
    /// <returns>是否保存成功</returns>
    public static async Task<bool> SaveStateAsync(TreeViewState state)
    {
        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(StateFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 序列化为JSON
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(state, options);
            await File.WriteAllTextAsync(StateFilePath, json);

            return true;
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常
            Console.WriteLine($"保存TreeView状态失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 从JSON文件读取TreeView状态
    /// </summary>
    /// <returns>读取的状态，如果失败则返回默认状态</returns>
    public static async Task<TreeViewState> LoadStateAsync()
    {
        try
        {
            if (!File.Exists(StateFilePath))
            {
                return new TreeViewState();
            }

            var json = await File.ReadAllTextAsync(StateFilePath);
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var state = JsonSerializer.Deserialize<TreeViewState>(json, options);
            return state ?? new TreeViewState();
        }
        catch (Exception ex)
        {
            // 记录错误但返回默认状态
            Console.WriteLine($"读取TreeView状态失败: {ex.Message}");
            return new TreeViewState();
        }
    }

    /// <summary>
    /// 删除状态文件
    /// </summary>
    /// <returns>是否删除成功</returns>
    public static bool DeleteState()
    {
        try
        {
            if (File.Exists(StateFilePath))
            {
                File.Delete(StateFilePath);
            }
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"删除TreeView状态失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查状态文件是否存在
    /// </summary>
    /// <returns>状态文件是否存在</returns>
    public static bool StateFileExists()
    {
        return File.Exists(StateFilePath);
    }

    /// <summary>
    /// 获取状态文件路径
    /// </summary>
    /// <returns>状态文件的完整路径</returns>
    public static string GetStateFilePath()
    {
        return StateFilePath;
    }
}
