using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Services.Dialogs;
using AlphaPM.Extensions;
using AlphaPM.Models;
using AlphaPM.Models.DWG;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// 节点编辑视图模型 - 极简版，直接绑定 TreeNodeData
/// </summary>
public partial class NodeEditViewModel : ObservableObject, IDialogAware
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<NodeEditViewModel>();

    #region 属性

    /// <summary>
    /// 要编辑的节点数据（直接绑定到界面）
    /// </summary>
    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(ConfirmCommand))]
    public partial TreeNodeData? NodeData { get; set; }

    /// <summary>
    /// 是否为编辑模式
    /// </summary>
    [ObservableProperty]
    public partial bool IsEditMode { get; set; }

    /// <summary>
    /// 窗口标题
    /// </summary>
    public string WindowTitle => IsEditMode ? "编辑节点" : "添加节点";

    /// <summary>
    /// 确认按钮文本
    /// </summary>
    public string ConfirmButtonText => IsEditMode ? "保存" : "添加";

    /// <summary>
    /// 节点类型显示名称
    /// </summary>
    public string NodeTypeDisplayName => NodeData?.NodeType != null 
        ? TreeNodeDataExtensions.GetDisplayName(NodeData.NodeType) 
        : "节点";

    /// <summary>
    /// 是否为年份节点
    /// </summary>
    public bool IsYearNode => NodeData?.NodeType == DWGNodeTypes.Year;

    /// <summary>
    /// 是否为项目节点
    /// </summary>
    public bool IsProjectNode => NodeData?.NodeType == DWGNodeTypes.Project;

    /// <summary>
    /// 是否为栋号节点
    /// </summary>
    public bool IsBuildingNode => NodeData?.NodeType == DWGNodeTypes.Building;

    #endregion

    #region 命令

    /// <summary>
    /// 确认命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanConfirm))]
    private void Confirm()
    {
        if (string.IsNullOrWhiteSpace(NodeData?.Name))
        {
            _logger.Warning("节点名称不能为空");
            return;
        }

        _logger.Info($"确认{(IsEditMode ? "编辑" : "添加")}节点: {NodeData.Name}");

        var parameters = new DialogParameters
        {
            { "ModifiedNode", NodeData }
        };

        RequestClose?.Invoke(new DialogResult(ButtonResult.OK, parameters));
    }

    private bool CanConfirm()
    {
        return NodeData != null && !string.IsNullOrWhiteSpace(NodeData.Name);
    }

    /// <summary>
    /// 取消命令
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        _logger.Info("取消编辑操作");
        RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
    }

    #endregion

    #region IDialogAware 实现

    public string Title => WindowTitle;

    public event Action<IDialogResult>? RequestClose;

    public bool CanCloseDialog() => true;

    public void OnDialogClosed()
    {
        _logger.Debug("对话框已关闭");
    }

    public void OnDialogOpened(IDialogParameters parameters)
    {
        _logger.Debug("对话框打开，开始初始化");

        // 获取传入的节点数据
        if (parameters.ContainsKey("NodeData"))
        {
            NodeData = parameters.GetValue<TreeNodeData>("NodeData");
        }

        // 获取编辑模式（明确指定，不再猜测）
        if (parameters.ContainsKey("IsEditMode"))
        {
            IsEditMode = parameters.GetValue<bool>("IsEditMode");
        }

        _logger.Info($"模式: {(IsEditMode ? "编辑" : "添加")}, 节点: {NodeData?.Name}");
    }

    #endregion
}
