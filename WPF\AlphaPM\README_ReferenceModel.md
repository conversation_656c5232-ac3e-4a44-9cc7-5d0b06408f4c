# ReferenceModel 和 Windows 文件操作功能

## 概述

根据用户需求，我们创建了两个核心功能：

1. **ReferenceModel** - 软件级参考保存模型，包含项目路径和模型路径管理
2. **WindowsFileOperationService** - Windows 文件操作封装功能

## 文件结构

```
📁 AlphaPM/
├── 📁 Data/
│   └── ReferenceModel.cs              # 参考保存模型
├── 📁 Services/
│   ├── WindowsFileOperationService.cs # Windows 文件操作服务
│   ├── ReferenceService.cs           # 参考模型管理服务
│   └── FileOperationExample.cs       # 使用示例
├── 📁 ViewModels/
│   └── ReferenceConfigViewModel.cs   # MVVM 示例
└── 📁 Tests/
    └── ReferenceModelTests.cs        # 单元测试
```

## 核心功能

### 1. ReferenceModel (Data/ReferenceModel.cs)

**主要功能：**
- ✅ 项目路径管理 (`ProjectPath`)
- ✅ 模型路径管理 (`ModelPath`)
- ✅ 工作区路径管理 (`WorkspacePath`)
- ✅ 模板路径管理 (`TemplatePath`)
- ✅ 导出路径管理 (`ExportPath`)
- ✅ 备份路径管理 (`BackupPath`)
- ✅ 路径验证功能 (`ValidatePaths()`)
- ✅ 通用设置存储 (`SetSetting<T>`, `GetSetting<T>`)
- ✅ WPF 数据绑定支持 (`INotifyPropertyChanged`)
- ✅ JSON 序列化支持

**使用示例：**
```csharp
var model = new ReferenceModel();
model.ProjectPath = @"C:\Projects\MyProject";
model.ModelPath = @"C:\Models\MyModel.dwg";

// 验证路径
var validation = model.ValidatePaths();
if (!validation.IsValid)
{
    foreach (var error in validation.Errors)
        Console.WriteLine($"错误: {error}");
}

// 设置自定义配置
model.SetSetting("AutoSave", true);
model.SetSetting("SaveInterval", 300);

// 获取配置
var autoSave = model.GetSetting<bool>("AutoSave");
var interval = model.GetSetting<int>("SaveInterval");
```

### 2. WindowsFileOperationService (Services/WindowsFileOperationService.cs)

**主要功能：**
- ✅ 文件打开对话框 (`OpenFileDialog`)
- ✅ 文件保存对话框 (`SaveFileDialog`)
- ✅ 文件夹选择对话框 (`SelectFolderDialog`) - 使用 .NET 8 原生 `OpenFolderDialog`
- ✅ 文件复制操作 (`CopyFile`)
- ✅ 文件移动操作 (`MoveFile`)
- ✅ 在资源管理器中显示文件 (`ShowInExplorer`)
- ✅ 在资源管理器中打开文件夹 (`OpenFolderInExplorer`)
- ✅ 完整的错误处理和结果报告

**使用示例：**
```csharp
// 选择文件
var fileResult = WindowsFileOperationService.OpenFileDialog(
    title: "选择 DWG 文件",
    filter: "DWG 文件|*.dwg|所有文件|*.*",
    multiSelect: false
);

if (fileResult.Success)
{
    Console.WriteLine($"选择的文件: {fileResult.SelectedFiles.First()}");
}

// 选择文件夹
var folderResult = WindowsFileOperationService.SelectFolderDialog(
    title: "选择项目文件夹"
);

if (folderResult.Success)
{
    Console.WriteLine($"选择的文件夹: {folderResult.SelectedPath}");
}

// 复制文件
var copyResult = WindowsFileOperationService.CopyFile(
    sourceFile: @"C:\Source\file.dwg",
    destinationFile: @"C:\Backup\file.dwg",
    overwrite: true
);

// 在资源管理器中显示
WindowsFileOperationService.ShowInExplorer(@"C:\MyFile.dwg");
```

### 3. ReferenceService (Services/ReferenceService.cs)

**主要功能：**
- ✅ 配置文件的保存和加载
- ✅ 自动备份功能
- ✅ 与 WindowsFileOperationService 集成
- ✅ 异步操作支持

**使用示例：**
```csharp
var service = new ReferenceService("config.json");

// 加载配置
var loadResult = await service.LoadConfigurationAsync();
if (loadResult.Success)
{
    var model = loadResult.Data;
    // 使用模型...
}

// 保存配置
var model = new ReferenceModel { ProjectPath = @"C:\Project" };
var saveResult = await service.SaveConfigurationAsync(model);

// 选择项目路径
var selectResult = await service.SelectProjectPathAsync("选择项目文件夹");
if (selectResult.Success)
{
    model.ProjectPath = selectResult.Data;
}
```

## 技术特性

### 🎯 .NET 8 原生支持
- 使用 .NET 8 的 `OpenFolderDialog`，无需 Windows Forms 依赖
- 避免了命名空间冲突问题

### 🔧 WPF 集成
- 完整的 `INotifyPropertyChanged` 支持
- 适用于 MVVM 模式
- 支持数据绑定和验证

### 💾 数据持久化
- JSON 序列化支持
- 自动备份机制
- 类型安全的设置存储

### 🛡️ 错误处理
- 统一的结果类型 (`OperationResult<T>`)
- 详细的错误信息和警告
- 路径验证和存在性检查

### 🧪 测试支持
- 完整的单元测试覆盖
- 模拟和集成测试示例

## 编译状态

✅ **编译成功** - 所有命名空间冲突已解决，使用 .NET 8 原生 API

## 下一步建议

1. **运行测试** - 执行单元测试验证功能
2. **集成到主界面** - 在主应用中使用这些服务
3. **添加更多文件类型支持** - 扩展文件过滤器
4. **国际化支持** - 添加多语言支持

## 使用方法

1. 在你的 ViewModel 中注入 `ReferenceService`
2. 使用 `WindowsFileOperationService` 的静态方法进行文件操作
3. 通过 `ReferenceModel` 管理应用配置和路径
4. 利用数据绑定将模型连接到 UI

这个实现完全满足了用户的需求：创建了软件级参考保存模型和 Windows 文件操作封装功能，并且解决了所有编译问题。
