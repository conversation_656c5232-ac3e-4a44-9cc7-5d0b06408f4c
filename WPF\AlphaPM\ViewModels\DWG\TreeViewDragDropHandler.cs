﻿using System.Windows;
using System.Windows.Threading;
using AlphaPM.Models.DWG;
using AlphaPM.Services.DWG;
using GongSolutions.Wpf.DragDrop;
using Zylo.WPF.Controls;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// TreeView拖拽处理器 - MVVM模式实现
///
/// 🎯 功能说明：
/// - 实现gong-wpf-dragdrop库的IDragSource和IDropTarget接口
/// - 支持三层级树形结构的拖拽操作：年份(Level 1) → 项目(Level 2) → 栋号(Level 3)
/// - 自动处理数据库同步和UI更新
/// - 严格的业务规则验证和重复名称检查
///
/// 📋 拖拽规则：
/// - 年份节点(Level 1)：只能与其他年份进行同级排序，不能移动到其他层级
/// - 项目节点(Level 2)：可以移动到其他年份下，也可以与同级项目排序
/// - 栋号节点(Level 3)：可以移动到其他项目下，也可以与同级栋号排序
/// - 重复检查：同一父节点下不允许存在重名的子节点
/// - 循环引用检查：不允许将节点拖拽到自己的子节点上
///
/// 🔄 操作类型：
/// 1. 同级排序(SiblingReorder)：在同一父节点下重新排列子节点顺序
/// 2. 父子关系改变(ParentChange)：将节点移动到新的父节点下
///
/// 💾 数据持久化：
/// - 所有拖拽操作都会自动保存到数据库
/// - 更新节点的ParentId、Level、SortOrder等属性
/// - 失败时会显示错误提示并回滚UI操作
/// </summary>
public class TreeViewDragDropHandler : IDragSource, IDropTarget
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<TreeViewDragDropHandler>();

    /// <summary>
    /// 树节点数据服务，用于处理数据库操作
    /// </summary>
    private readonly TreeNodeDataService _treeNodeDataService;

    /// <summary>
    /// 关联的ViewModel实例，用于UI更新和状态管理
    /// </summary>
    private readonly DWGManageViewModel _viewModel;

    /// <summary>
    /// 构造函数 - 初始化拖拽处理器
    /// </summary>
    /// <param name="viewModel">关联的DWGManageViewModel实例</param>
    /// <param name="treeNodeDataService">树节点数据服务实例</param>
    public TreeViewDragDropHandler(DWGManageViewModel viewModel, TreeNodeDataService treeNodeDataService)
    {
        _viewModel = viewModel ?? throw new ArgumentNullException(nameof(viewModel));
        _treeNodeDataService = treeNodeDataService ?? throw new ArgumentNullException(nameof(treeNodeDataService));
    }

    #region IDragSource 实现 - 拖拽源接口

    /// <summary>
    /// 开始拖拽操作
    /// 当用户开始拖拽TreeView中的节点时调用此方法
    /// </summary>
    /// <param name="dragInfo">拖拽信息，包含源节点和拖拽状态</param>
    public void StartDrag(IDragInfo dragInfo)
    {
        // 检查拖拽的源项是否为TreeNodeData类型
        if (dragInfo.SourceItem is TreeNodeData sourceNode)
        {
            // 设置拖拽数据为源节点
            dragInfo.Data = sourceNode;

            // 设置拖拽效果为移动（不允许复制）
            dragInfo.Effects = DragDropEffects.Move;

            // 注意：DragDropCopyKeyState 是只读属性，不能手动设置
            // 它会根据用户按键状态（如Ctrl键）自动确定
        }
    }

    /// <summary>
    /// 判断是否可以开始拖拽
    /// 在用户尝试拖拽节点时调用，用于决定是否允许拖拽操作
    /// </summary>
    /// <param name="dragInfo">拖拽信息</param>
    /// <returns>true表示可以拖拽，false表示不允许拖拽</returns>
    public bool CanStartDrag(IDragInfo dragInfo)
    {
        // 只有TreeNodeData类型且启用状态的节点才能被拖拽
        return dragInfo.SourceItem is TreeNodeData sourceNode && sourceNode.IsEnabled;
    }

    /// <summary>
    /// 拖拽完成后的清理操作
    /// 当拖拽操作完成（无论成功还是失败）后调用
    /// </summary>
    /// <param name="dropInfo">放置信息</param>
    public void Dropped(IDropInfo dropInfo)
    {
        // 目前不需要特殊的清理操作
    }

    /// <summary>
    /// 拖拽操作结束时的回调
    /// 在整个拖拽操作完成后调用，可以用于记录日志或清理资源
    /// </summary>
    /// <param name="operationResult">操作结果（Move、Copy、None等）</param>
    /// <param name="dragInfo">拖拽信息</param>
    public void DragDropOperationFinished(DragDropEffects operationResult, IDragInfo dragInfo)
    {
        // 目前不需要特殊处理
    }

    /// <summary>
    /// 拖拽操作被取消时的回调
    /// 当用户按ESC键或其他方式取消拖拽时调用
    /// </summary>
    public void DragCancelled()
    {
        // 目前不需要特殊处理
    }

    /// <summary>
    /// 异常处理回调
    /// 当拖拽过程中发生异常时调用，返回true表示异常已处理
    /// </summary>
    /// <param name="exception">发生的异常</param>
    /// <returns>false表示不处理异常，让系统默认处理</returns>
    public bool TryCatchOccurredException(Exception exception) => false;

    #endregion

    #region IDropTarget 实现 - 放置目标接口

    /// <summary>
    /// 拖拽悬停时的处理
    /// 当拖拽的节点悬停在目标位置上时持续调用，用于提供视觉反馈
    /// </summary>
    /// <param name="dropInfo">放置信息，包含目标节点和放置位置</param>
    public void DragOver(IDropInfo dropInfo)
    {
        // 检查是否可以在此位置放置节点
        if (CanAcceptData(dropInfo))
        {
            // 设置允许移动效果
            dropInfo.Effects = DragDropEffects.Move;

            // 设置高亮显示目标区域，给用户视觉反馈
            dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
        }
        else
        {
            // 不允许在此位置放置，显示禁止图标
            dropInfo.Effects = DragDropEffects.None;
        }
    }

    /// <summary>
    /// 执行放置操作
    /// 当用户释放鼠标完成拖拽时调用，执行实际的数据移动操作
    /// </summary>
    /// <param name="dropInfo">放置信息</param>
    public async void Drop(IDropInfo dropInfo)
    {
        // 再次验证是否可以放置（双重检查）
        if (!CanAcceptData(dropInfo)) return;

        // 获取源节点和目标节点
        var sourceNode = (TreeNodeData)dropInfo.Data;
        var targetNode = dropInfo.TargetItem as TreeNodeData;

        if (targetNode == null) return;

        try
        {
            // 在Service层更新之前保存原始的ParentId
            var originalParentId = sourceNode.ParentId;

            // 使用Service层的统一拖拽处理方法
            // 根据源节点和目标节点的Level关系自动判断操作类型
            var success = await _treeNodeDataService.HandleDragDropAsync(sourceNode, targetNode);

            if (success)
            {
                // 操作成功，手动更新UI中的父子关系
                _viewModel.UpdateTreeStructureAfterDragDrop(sourceNode, targetNode, originalParentId);
                _viewModel.StatusMessage = $"成功移动 {sourceNode.Name} 到 {targetNode.Name}";
            }
            else
            {
                // 操作失败，显示错误提示
                await YMessageBox.ShowWarningAsync("拖拽操作失败，请检查操作是否符合业务规则", "操作失败");
            }
        }
        catch (Exception ex)
        {
            // 捕获并显示错误信息，确保用户知道操作失败的原因
            await YMessageBox.ShowErrorAsync($"拖拽操作异常: {ex.Message}", "系统错误");
            YLogger.ForDebug<TreeViewDragDropHandler>().Error($"拖拽操作异常: {ex}");
        }
    }

    #endregion

    #region 私有方法 - 拖拽验证和业务逻辑

    /// <summary>
    /// 检查是否可以接受拖拽数据
    /// 这是拖拽操作的核心验证方法，确保所有拖拽操作都符合业务规则
    /// </summary>
    /// <param name="dropInfo">放置信息</param>
    /// <returns>true表示可以接受拖拽，false表示拒绝</returns>
    private bool CanAcceptData(IDropInfo dropInfo)
    {
        // 验证1：检查拖拽数据类型是否正确
        if (dropInfo.Data is not TreeNodeData sourceNode) return false;

        // 验证2：检查目标节点类型是否正确
        if (dropInfo.TargetItem is not TreeNodeData targetNode) return false;

        // 验证3：不能拖拽到自己身上（无意义操作）
        if (sourceNode == targetNode) return false;

        // 验证4：不能拖拽到自己的子节点上（避免循环引用）
        // 例如：不能将"2024年"拖拽到"2024年/项目A"上
        if (IsDescendant(targetNode, sourceNode)) return false;

        // 验证5：根据拖拽位置判断具体的操作类型并验证
        if (dropInfo.InsertPosition.HasFlag(RelativeInsertPosition.TargetItemCenter))
        {
            // 拖拽到目标节点中心 - 检查是否可以成为子节点
            return CanBecomeChild(sourceNode, targetNode);
        }
        else
        {
            // 拖拽到目标节点前后 - 检查是否可以成为同级节点
            return CanBeSibling(sourceNode, targetNode);
        }
    }

    /// <summary>
    /// 检查节点A是否是节点B的后代
    /// 用于防止循环引用，确保不会将父节点拖拽到自己的子节点上
    /// </summary>
    /// <param name="nodeA">要检查的节点A</param>
    /// <param name="nodeB">要检查的节点B</param>
    /// <returns>true表示nodeA是nodeB的后代</returns>
    /// <example>
    /// 例如：nodeA="2024年/项目A/1#楼", nodeB="2024年"
    /// 返回true，因为"2024年/项目A/1#楼"是"2024年"的后代
    /// </example>
    private bool IsDescendant(TreeNodeData nodeA, TreeNodeData nodeB)
    {
        var current = nodeA.YParent;
        while (current != null)
        {
            if (current == nodeB) return true;
            current = current.YParent;
        }

        return false;
    }

    /// <summary>
    /// 检查源节点是否可以成为目标节点的子节点
    /// 根据三层级业务规则进行验证
    /// </summary>
    /// <param name="sourceNode">源节点（要移动的节点）</param>
    /// <param name="targetNode">目标节点（要成为父节点的节点）</param>
    /// <returns>true表示可以成为子节点</returns>
    /// <example>
    /// 业务规则：
    /// - Level 1 (年份) 可以包含 Level 2 (项目)
    /// - Level 2 (项目) 可以包含 Level 3 (栋号)
    /// - Level 3 (栋号) 不能包含任何子节点
    /// </example>
    private bool CanBecomeChild(TreeNodeData sourceNode, TreeNodeData targetNode)
    {
        switch (targetNode.Level)
        {
            case 1: // 年份节点
                return sourceNode.Level == 2; // 只有项目可以移动到年份下

            case 2: // 项目节点
                return sourceNode.Level == 3; // 只有栋号可以移动到项目下

            case 3: // 栋号节点
                return false; // 栋号不能有子节点

            default:
                return false; // 未知层级，拒绝操作
        }
    }

    /// <summary>
    /// 检查源节点是否可以与目标节点成为同级
    /// 同级节点必须具有相同的层级(Level)
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <returns>true表示可以成为同级节点</returns>
    /// <example>
    /// - 年份与年份可以同级排序
    /// - 项目与项目可以同级排序
    /// - 栋号与栋号可以同级排序
    /// - 但年份不能与项目同级
    /// </example>
    private bool CanBeSibling(TreeNodeData sourceNode, TreeNodeData targetNode)
    {
        return sourceNode.Level == targetNode.Level;
    }

    /// <summary>
    /// 处理父子关系改变
    /// 当用户将节点拖拽到另一个节点的中心时调用，表示要改变父子关系
    /// </summary>
    /// <param name="sourceNode">源节点（要移动的节点）</param>
    /// <param name="targetNode">目标节点（要成为新父节点的节点）</param>
    /// <example>
    /// 场景示例：
    /// - 将"项目A"拖拽到"2025年"上 → "项目A"成为"2025年"的子节点
    /// - 将"1#楼"拖拽到"项目B"上 → "1#楼"成为"项目B"的子节点
    /// </example>
    private async Task HandleParentChangeAsync(TreeNodeData sourceNode, TreeNodeData targetNode)
    {
        // 步骤1：检查目标父节点下是否已存在同名子节点
        if (HasDuplicateName(sourceNode, targetNode))
        {
            MessageBox.Show($"目标位置已存在名为 '{sourceNode.Name}' 的节点，无法移动。",
                "重复名称", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // 步骤2：更新UI界面（在UI线程中执行）
        Application.Current.Dispatcher.Invoke(() =>
        {
            // 从原父节点的子节点集合中移除
            sourceNode.Parent?.Children.Remove(sourceNode);

            // 更新节点的父子关系属性（在添加到新父节点前设置）
            sourceNode.YParent = targetNode; // UI绑定用的父节点引用
            sourceNode.ParentId = targetNode.Id; // 数据库用的父节点ID
            sourceNode.Level = targetNode.Level + 1; // 更新层级
            sourceNode.SortOrder = targetNode.Children.Count + 1; // 设置为最后一个（当前子节点数量+1）

            // 添加到新父节点的子节点集合中
            targetNode.Children.Add(sourceNode);
        });

        // 步骤3：将更改保存到数据库
        await _viewModel.UpdateNodeInDatabaseAsync(sourceNode);
    }

    /// <summary>
    /// 处理同级排序
    /// 当用户将节点拖拽到另一个节点的前面或后面时调用，表示要进行同级排序
    /// </summary>
    /// <param name="sourceNode">源节点（要移动的节点）</param>
    /// <param name="targetNode">目标节点（参考位置的节点）</param>
    /// <param name="dropInfo">放置信息，包含具体的插入位置</param>
    /// <example>
    /// 场景示例：
    /// - 将"项目A"拖拽到"项目B"前面 → 调整项目的排列顺序
    /// - 将"1#楼"拖拽到"2#楼"后面 → 调整栋号的排列顺序
    /// - 跨父节点：将"项目A"从"2024年"拖拽到"2025年"的项目列表中
    /// </example>
    private async Task HandleSiblingReorderAsync(TreeNodeData sourceNode, TreeNodeData targetNode, IDropInfo dropInfo)
    {
        var targetParent = targetNode.YParent;
        if (targetParent == null) return;

        // 步骤1：检查跨父节点移动时的重复名称
        // 如果源节点和目标节点不在同一父节点下，需要检查目标父节点下是否有重名
        if (sourceNode.YParent != targetParent && HasDuplicateName(sourceNode, targetParent))
        {
            YMessageBox.ShowErrorAsync($"目标位置已存在名为 '{sourceNode.Name}' 的节点，无法移动。",
                "重复名称");

            return;
        }

        // 步骤2：判断是同级排序还是跨父节点移动
        if (sourceNode.YParent == targetParent)
        {
            // 同级排序：简单交换SortOrder，更高效
            await HandleSameLevelReorderAsync(sourceNode, targetNode, dropInfo);
        }
        else
        {
            // 跨父节点移动：需要重新计算所有SortOrder
            await HandleCrossParentMoveAsync(sourceNode, targetParent, targetNode, dropInfo);
        }
    }

    /// <summary>
    /// 处理同级排序 - 简单交换SortOrder
    /// 当源节点和目标节点在同一父节点下时，只需要交换它们的SortOrder
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <param name="dropInfo">放置信息</param>
    private async Task HandleSameLevelReorderAsync(TreeNodeData sourceNode, TreeNodeData targetNode, IDropInfo dropInfo)
    {
        // 获取原始的SortOrder
        var sourceSortOrder = sourceNode.SortOrder;
        var targetSortOrder = targetNode.SortOrder;

        // 步骤1：更新UI界面
        Application.Current.Dispatcher.Invoke(() =>
        {
            var siblings = sourceNode.YParent!.Children;
            var sourceIndex = siblings.IndexOf(sourceNode);
            var targetIndex = siblings.IndexOf(targetNode);

            // 交换在集合中的位置
            siblings.RemoveAt(sourceIndex);

            // 重新计算目标索引（因为移除了源节点）
            targetIndex = siblings.IndexOf(targetNode);

            // 根据放置位置调整插入索引
            if (dropInfo.InsertPosition.HasFlag(RelativeInsertPosition.AfterTargetItem))
            {
                targetIndex++;
            }

            // 插入到新位置
            siblings.Insert(targetIndex, sourceNode);

            // 交换SortOrder
            sourceNode.SortOrder = targetSortOrder;
            targetNode.SortOrder = sourceSortOrder;
        });

        // 步骤2：保存两个节点到数据库
        await _viewModel.UpdateNodeInDatabaseAsync(sourceNode);
        await _viewModel.UpdateNodeInDatabaseAsync(targetNode);
    }

    /// <summary>
    /// 处理跨父节点移动 - 重新计算所有SortOrder
    /// 当源节点移动到不同父节点下时，需要重新计算目标父节点下所有子节点的SortOrder
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetParent">目标父节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <param name="dropInfo">放置信息</param>
    private async Task HandleCrossParentMoveAsync(TreeNodeData sourceNode, TreeNodeData targetParent,
        TreeNodeData targetNode, IDropInfo dropInfo)
    {
        var affectedNodes = new List<TreeNodeData>();

        // 步骤1：更新UI界面
        Application.Current.Dispatcher.Invoke(() =>
        {
            // 从原父节点移除
            sourceNode.Parent?.Children.Remove(sourceNode);

            // 更新父节点信息
            sourceNode.YParent = targetParent;
            sourceNode.ParentId = targetParent.Id;

            // 计算插入位置
            var siblings = targetParent.Children;
            var targetIndex = siblings.IndexOf(targetNode);

            if (dropInfo.InsertPosition.HasFlag(RelativeInsertPosition.AfterTargetItem))
            {
                targetIndex++;
            }

            // 插入到新位置
            siblings.Insert(targetIndex, sourceNode);

            // 重新计算所有同级节点的SortOrder
            for (int i = 0; i < siblings.Count; i++)
            {
                siblings[i].SortOrder = i + 1;
                affectedNodes.Add(siblings[i]);
            }
        });

        // 步骤2：批量保存所有受影响的节点
        foreach (var node in affectedNodes)
        {
            await _viewModel.UpdateNodeInDatabaseAsync(node);
        }
    }

    /// <summary>
    /// 检查目标父节点下是否存在重复名称
    /// 确保同一父节点下的子节点名称唯一性
    /// </summary>
    /// <param name="sourceNode">源节点（要移动的节点）</param>
    /// <param name="targetParent">目标父节点</param>
    /// <returns>true表示存在重复名称，false表示名称唯一</returns>
    /// <example>
    /// 场景：要将"项目A"移动到"2025年"下
    /// 如果"2025年"下已经有一个叫"项目A"的子节点，则返回true
    /// </example>
    private bool HasDuplicateName(TreeNodeData sourceNode, TreeNodeData targetParent)
    {
        return targetParent.Children.Any(child =>
            child != sourceNode && // 排除源节点本身（用于同级排序场景）
            string.Equals(child.Name, sourceNode.Name, StringComparison.OrdinalIgnoreCase)); // 忽略大小写比较
    }

    #endregion
}