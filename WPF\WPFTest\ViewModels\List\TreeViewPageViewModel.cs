using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Regions;
using WPFTest.Models;
using Zylo.YLog.Runtime;
using Wpf.Ui.Controls;
using Zylo.WPF.Controls.TreeView;
using Zylo.WPF.Enums;


namespace WPFTest.ViewModels.List;

/// <summary>
/// TreeView页面ViewModel - 完整功能实现
/// 基于CommunityToolkit.Mvvm最佳实践
/// 使用Zylo.YLog.Runtime进行详细调试
/// </summary>
public partial class TreeViewPageViewModel : ObservableValidator, INavigationAware
{
    // 🔥 使用最详细的日志级别 - 输出所有信息
    private readonly YLoggerInstance _logger = YLogger.ForDebug<TreeViewPageViewModel>();

    #region 核心数据属性

    /// <summary>
    /// 树形数据源
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(TotalNodeCount))]
    [NotifyPropertyChangedFor(nameof(TreeDataInfo))]
    [NotifyPropertyChangedFor(nameof(HasTreeData))]
    private ObservableCollection<YTreeNodeData> treeData = new();

    /// <summary>
    /// 过滤后的树形数据（用于搜索）
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<YTreeNodeData> filteredTreeData = new();

    /// <summary>
    /// 选中的节点
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(HasSelectedNode))]
    [NotifyPropertyChangedFor(nameof(SelectedNodeInfo))]
    [NotifyPropertyChangedFor(nameof(SelectedNodeDetails))]
    [NotifyPropertyChangedFor(nameof(CanEditSelectedNode))]
    [NotifyPropertyChangedFor(nameof(CanDeleteSelectedNode))]
    private YTreeNodeData? selectedNode;

    #endregion

    #region 界面状态属性

    /// <summary>
    /// 搜索过滤文本
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(IsFiltering))]
    private string filterText = string.Empty;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string statusMessage = "准备就绪";

    /// <summary>
    /// 是否正在加载
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CanPerformActions))]
    [NotifyPropertyChangedFor(nameof(CanReloadData))]
    private bool isLoading;

    /// <summary>
    /// 是否正在编辑节点
    /// </summary>
    [ObservableProperty]
    private bool isEditingNode;

    /// <summary>
    /// 编辑中的节点名称
    /// </summary>
    [ObservableProperty]
    [Required(ErrorMessage = "节点名称不能为空")]
    [MinLength(1, ErrorMessage = "节点名称至少1个字符")]
    [MaxLength(100, ErrorMessage = "节点名称不能超过100个字符")]
    private string editingNodeName = string.Empty;

    /// <summary>
    /// 编辑中的节点描述
    /// </summary>
    [ObservableProperty]
    [MaxLength(500, ErrorMessage = "节点描述不能超过500个字符")]
    private string editingNodeDescription = string.Empty;

    /// <summary>
    /// 复制的节点（用于复制粘贴功能）
    /// </summary>
    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(CanPasteNode))]
    private YTreeNodeData? copiedNode;

    #endregion

    #region 计算属性

    /// <summary>
    /// 是否有选中的节点
    /// </summary>
    public bool HasSelectedNode => SelectedNode != null;

    /// <summary>
    /// 是否有树形数据
    /// </summary>
    public bool HasTreeData => TreeData.Count > 0;

    /// <summary>
    /// 是否正在过滤
    /// </summary>
    public bool IsFiltering => !string.IsNullOrWhiteSpace(FilterText);

    /// <summary>
    /// 是否可以执行操作（非加载状态）
    /// </summary>
    public bool CanPerformActions => !IsLoading;

    /// <summary>
    /// 是否可以重新加载数据（按钮启用状态）
    /// </summary>
    public bool CanReloadData => !IsLoading;

    /// <summary>
    /// 是否可以编辑选中节点
    /// </summary>
    public bool CanEditSelectedNode => HasSelectedNode && !IsLoading && !IsEditingNode;

    /// <summary>
    /// 是否可以删除选中节点
    /// </summary>
    public bool CanDeleteSelectedNode => HasSelectedNode && !IsLoading && !IsEditingNode;

    /// <summary>
    /// 是否可以粘贴节点
    /// </summary>
    public bool CanPasteNode => CopiedNode != null && HasSelectedNode && !IsLoading && !IsEditingNode;

    /// <summary>
    /// 选中节点信息
    /// </summary>
    public string SelectedNodeInfo => SelectedNode != null
        ? $"📍 {SelectedNode.Name}"
        : "未选中任何节点";

    /// <summary>
    /// 选中节点详细信息
    /// </summary>
    public string SelectedNodeDetails => SelectedNode != null
        ? $"{SelectedNode.DefaultIcon} {SelectedNode.Name}\n类型: {SelectedNode.NodeType}\n描述: {SelectedNode.Description}"
        : "请选择一个节点查看详细信息";

    /// <summary>
    /// 总节点数量
    /// </summary>
    public int TotalNodeCount => CountAllNodes(TreeData);

    /// <summary>
    /// 树形数据状态信息
    /// </summary>
    public string TreeDataInfo => IsFiltering 
        ? $"显示 {CountAllNodes(FilteredTreeData)} 个节点（共 {TotalNodeCount} 个）"
        : $"共 {TotalNodeCount} 个节点";

    #endregion

    #region 构造函数

    public TreeViewPageViewModel()
    {
        _logger.Info("🔥🔥🔥 TreeViewPageViewModel构造函数开始！！！");

        using (_logger.Monitor("TreeViewPageViewModel初始化"))
        {
            _logger.Info("📋 初始化过滤数据集合");
            // 初始化过滤数据
            FilteredTreeData = TreeData;

            _logger.Info("🌱 开始创建测试数据");
            // 创建测试数据 - 使用专业组装方式
            CreateTestDataProfessional();

            StatusMessage = $"✅ 初始化完成，{TreeDataInfo}";
            _logger.Info($"🌳 TreeView页面ViewModel初始化完成 - {TreeDataInfo}");

            // 输出初始状态报告
            _logger.Info($"📊 ========== 初始状态报告 ==========");
            _logger.Info($"TreeData节点数: {TreeData.Count}");
            _logger.Info($"FilteredTreeData节点数: {FilteredTreeData.Count}");
            _logger.Info($"总节点数: {TotalNodeCount}");
            _logger.Info($"HasTreeData: {HasTreeData}");
            _logger.Info($"📊 ========== 初始状态报告结束 ==========");
        }

        _logger.Info("🔥🔥🔥 TreeViewPageViewModel构造函数完成！！！");
    }

    #endregion

    #region 基础命令

    /// <summary>
    /// 添加根节点命令
    /// </summary>
    [RelayCommand]
    private async Task AddRootNodeAsync()
    {
        _logger.Debug("🔍 开始执行添加根节点命令");

        using (_logger.Monitor("添加根节点操作"))
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在添加根节点...";
                _logger.InfoDetailed("📋 设置加载状态，准备添加根节点");

                await Task.Delay(100); // 模拟异步操作
                _logger.Debug("⏱️ 异步延迟完成");

                var nextId = GetNextId();
                _logger.Debug($"🆔 获取下一个ID: {nextId}");

                var node = new YTreeNodeData
                {
                    Id = nextId,
                    Name = $"🌳 新根节点 {DateTime.Now:HH:mm:ss}",
                    Description = "新添加的根节点",
                    NodeType = "organization",
                    IsExpanded = true,
                    YChildren = new ObservableCollection<YTreeNodeData>()
                };

                _logger.InfoDetailed($"📝 创建新根节点: ID={node.Id}, Name={node.Name}");

                TreeData.Add(node);
                _logger.Debug($"➕ 节点已添加到TreeData，当前总数: {TreeData.Count}");

                SelectedNode = node;
                _logger.Debug("📍 设置新节点为选中状态");

                // 更新过滤数据
                ApplyFilter();
                _logger.Debug("🔄 过滤数据已更新");

                StatusMessage = $"✅ 已添加根节点: {node.Name}";
                _logger.Info($"✅ 成功添加根节点: {node.Name} (ID: {node.Id})");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 添加根节点失败: {ex.Message}");
                _logger.Debug($"🔍 异常详情: {ex}");
                StatusMessage = $"❌ 添加根节点失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                _logger.Debug("🔄 重置加载状态");
            }
        }
    }

    /// <summary>
    /// 添加子节点命令
    /// </summary>
    [RelayCommand]
    private async Task AddChildNodeAsync()
    {
        if (SelectedNode == null)
        {
            _logger.Warning("⚠️ 尝试添加子节点但未选中任何节点");
            return;
        }

        _logger.Debug($"🔍 开始为节点 '{SelectedNode.Name}' 添加子节点");

        using (_logger.Monitor($"添加子节点到 {SelectedNode.Name}"))
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在添加子节点...";
                _logger.InfoDetailed($"📋 准备为父节点添加子节点: {SelectedNode.Name} (ID: {SelectedNode.Id})");

                await Task.Delay(100); // 模拟异步操作

                var nextId = GetNextId();
                var childNodeType = GetChildNodeType(SelectedNode.NodeType);
                _logger.Debug($"🆔 子节点ID: {nextId}, 类型: {childNodeType}");

                var childNode = new YTreeNodeData
                {
                    Id = nextId,
                    Name = $"➕ 新子节点 {DateTime.Now:HH:mm:ss}",
                    Description = "新添加的子节点",
                    NodeType = childNodeType,
                    YParent = SelectedNode,
                    YChildren = new ObservableCollection<YTreeNodeData>()
                };

                _logger.InfoDetailed($"📝 创建子节点: ID={childNode.Id}, Name={childNode.Name}, 父节点={SelectedNode.Name}");

                SelectedNode.YChildren ??= new ObservableCollection<YTreeNodeData>();
                SelectedNode.YChildren.Add(childNode);
                _logger.Debug($"➕ 子节点已添加，父节点现有子节点数: {SelectedNode.YChildren.Count}");

                SelectedNode.IsExpanded = true;
                _logger.Debug("📂 父节点已展开");

                SelectedNode = childNode;
                _logger.Debug("📍 新子节点已设为选中状态");

                // 更新过滤数据
                ApplyFilter();
                _logger.Debug("🔄 过滤数据已更新");

                StatusMessage = $"✅ 已添加子节点: {childNode.Name}";
                _logger.Info($"✅ 成功添加子节点: {childNode.Name} 到父节点: {childNode.YParent?.Name}");
            }
            catch (Exception ex)
            {
                _logger.Error($"❌ 添加子节点失败: {ex.Message}");
                _logger.Debug($"🔍 异常详情: {ex}");
                StatusMessage = $"❌ 添加子节点失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                _logger.Debug("🔄 重置加载状态");
            }
        }
    }

    /// <summary>
    /// 删除节点命令
    /// </summary>
    [RelayCommand]
    private async Task DeleteNodeAsync()
    {
        if (SelectedNode == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "正在删除节点...";

            await Task.Delay(100); // 模拟异步操作

            var nodeToDelete = SelectedNode;
            var nodeName = nodeToDelete.Name;

            // 从树中移除节点
            RemoveNodeFromTree(nodeToDelete);

            // 清除选择
            SelectedNode = null;

            // 更新过滤数据
            ApplyFilter();

            StatusMessage = $"✅ 已删除节点: {nodeName}";
            _logger.Info($"删除节点: {nodeName}");
        }
        catch (Exception ex)
        {
            _logger.Error($"删除节点失败: {ex.Message}");
            StatusMessage = $"❌ 删除节点失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 开始编辑节点命令
    /// </summary>
    [RelayCommand]
    private void StartEditNode()
    {
        _logger.Debug("🔍 开始编辑节点命令被调用");

        if (SelectedNode == null)
        {
            _logger.Warning("⚠️ 尝试编辑节点但未选中任何节点");
            return;
        }

        _logger.InfoDetailed($"📝 开始编辑节点: {SelectedNode.Name} (ID: {SelectedNode.Id})");

        EditingNodeName = SelectedNode.Name;
        EditingNodeDescription = SelectedNode.Description;
        IsEditingNode = true;
        StatusMessage = "正在编辑节点...";

        _logger.Info($"✅ 进入编辑模式: {SelectedNode.Name}");
    }

    /// <summary>
    /// 保存编辑命令
    /// </summary>
    [RelayCommand]
    private async Task SaveEditAsync()
    {
        if (SelectedNode == null) return;

        try
        {
            // 验证输入
            ValidateAllProperties();
            if (HasErrors) return;

            IsLoading = true;
            StatusMessage = "正在保存编辑...";

            await Task.Delay(100); // 模拟异步操作

            var oldName = SelectedNode.Name;
            SelectedNode.Name = EditingNodeName;
            SelectedNode.Description = EditingNodeDescription;

            IsEditingNode = false;

            // 更新过滤数据
            ApplyFilter();

            StatusMessage = $"✅ 已保存编辑: {SelectedNode.Name}";
            _logger.Info($"编辑节点: {oldName} -> {SelectedNode.Name}");
        }
        catch (Exception ex)
        {
            _logger.Error($"保存编辑失败: {ex.Message}");
            StatusMessage = $"❌ 保存编辑失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 取消编辑命令
    /// </summary>
    [RelayCommand]
    private void CancelEdit()
    {
        _logger.Debug("🔍 取消编辑命令被调用");
        _logger.InfoDetailed("❌ 用户取消了节点编辑操作");

        IsEditingNode = false;
        EditingNodeName = string.Empty;
        EditingNodeDescription = string.Empty;
        StatusMessage = "已取消编辑";

        _logger.Info("✅ 已退出编辑模式");
    }

    /// <summary>
    /// 清空所有数据命令
    /// </summary>
    [RelayCommand]
    private async Task ClearAllDataAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在清空数据...";

            await Task.Delay(100); // 模拟异步操作

            TreeData.Clear();
            FilteredTreeData.Clear();
            SelectedNode = null;

            StatusMessage = "✅ 已清空所有数据";
            _logger.Info("清空所有树形数据");
        }
        catch (Exception ex)
        {
            _logger.Error($"清空数据失败: {ex.Message}");
            StatusMessage = $"❌ 清空数据失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 重新加载测试数据命令
    /// </summary>
    [RelayCommand]
    private async Task ReloadTestDataAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在重新加载测试数据...";

            await Task.Delay(200); // 模拟异步操作

            TreeData.Clear();
            CreateTestDataProfessional();
            ApplyFilter();

            StatusMessage = $"✅ 已重新加载测试数据，{TreeDataInfo}";
            _logger.Info("重新加载测试数据");
        }
        catch (Exception ex)
        {
            _logger.Error($"重新加载测试数据失败: {ex.Message}");
            StatusMessage = $"❌ 重新加载测试数据失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 使用便利构造函数重新加载数据命令
    /// </summary>
    [RelayCommand]
    private async Task ReloadWithConstructors()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在使用便利构造函数重新加载数据...";

            await Task.Delay(200); // 模拟异步操作

            CreateTestDataWithConstructors();

            StatusMessage = $"✅ 已使用便利构造函数重新加载数据，{TreeDataInfo}";
            _logger.Info("使用便利构造函数重新加载数据");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 重新加载失败: {ex.Message}";
            _logger.Error($"使用便利构造函数重新加载数据失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 扩展图标演示命令
    /// </summary>
    [RelayCommand]
    private async Task ShowExtendedIconDemo()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在加载扩展图标演示...";

            await Task.Delay(200); // 模拟异步操作

            CreateExtendedIconDemo();

            StatusMessage = $"✅ 扩展图标演示加载完成，{TreeDataInfo}";
            _logger.Info("扩展图标演示加载完成");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 加载失败: {ex.Message}";
            _logger.Error($"扩展图标演示加载失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 展开所有节点命令
    /// </summary>
    [RelayCommand]
    private void ExpandAllNodes()
    {
        try
        {
            SetAllNodesExpanded(TreeData, true);

            // 强制刷新TreeView显示
            OnPropertyChanged(nameof(TreeData));

            StatusMessage = "✅ 已展开所有节点";
            _logger.Info("展开所有节点");
        }
        catch (Exception ex)
        {
            _logger.Error($"展开所有节点失败: {ex.Message}");
            StatusMessage = $"❌ 展开所有节点失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 折叠所有节点命令
    /// </summary>
    [RelayCommand]
    private void CollapseAllNodes()
    {
        try
        {
            SetAllNodesExpanded(TreeData, false);

            // 强制刷新TreeView显示
            OnPropertyChanged(nameof(TreeData));

            StatusMessage = "✅ 已折叠所有节点";
            _logger.Info("折叠所有节点");
        }
        catch (Exception ex)
        {
            _logger.Error($"折叠所有节点失败: {ex.Message}");
            StatusMessage = $"❌ 折叠所有节点失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 测试选中功能命令
    /// </summary>
    [RelayCommand]
    private void TestSelectedNode()
    {
        _logger.Info($"🔍 ========== 功能状态测试开始 ==========");
        _logger.Info($"当前选中节点: {SelectedNode?.Name ?? "无"}");
        _logger.Info($"选中节点ID: {SelectedNode?.Id ?? 0}");
        _logger.Info($"选中节点类型: {SelectedNode?.NodeType ?? "Default"}");
        _logger.Info($"HasSelectedNode: {HasSelectedNode}");
        _logger.Info($"CanEditSelectedNode: {CanEditSelectedNode}");
        _logger.Info($"CanDeleteSelectedNode: {CanDeleteSelectedNode}");
        _logger.Info($"CanPasteNode: {CanPasteNode}");
        _logger.Info($"IsEditingNode: {IsEditingNode}");
        _logger.Info($"IsLoading: {IsLoading}");
        _logger.Info($"TreeData节点数: {TreeData.Count}");
        _logger.Info($"FilteredTreeData节点数: {FilteredTreeData.Count}");
        _logger.Info($"总节点数: {TotalNodeCount}");
        _logger.Info($"复制的节点: {CopiedNode?.Name ?? "无"}");
        _logger.Info($"🔍 ========== 功能状态测试结束 ==========");

        StatusMessage = $"🔍 测试完成 - 选中: {SelectedNode?.Name ?? "无"}";
    }

    /// <summary>
    /// 展开选中节点命令
    /// </summary>
    [RelayCommand]
    private void ExpandNode()
    {
        if (SelectedNode == null) return;

        try
        {
            SelectedNode.IsExpanded = true;
            StatusMessage = $"✅ 已展开节点: {SelectedNode.Name}";
            _logger.Info($"展开节点: {SelectedNode.Name}");
        }
        catch (Exception ex)
        {
            _logger.Error($"展开节点失败: {ex.Message}");
            StatusMessage = $"❌ 展开节点失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 折叠选中节点命令
    /// </summary>
    [RelayCommand]
    private void CollapseNode()
    {
        if (SelectedNode == null) return;

        try
        {
            SelectedNode.IsExpanded = false;
            StatusMessage = $"✅ 已折叠节点: {SelectedNode.Name}";
            _logger.Info($"折叠节点: {SelectedNode.Name}");
        }
        catch (Exception ex)
        {
            _logger.Error($"折叠节点失败: {ex.Message}");
            StatusMessage = $"❌ 折叠节点失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 复制节点命令
    /// </summary>
    [RelayCommand]
    private void CopyNode()
    {
        _logger.Debug("🔍 复制节点命令被调用");

        if (SelectedNode == null)
        {
            _logger.Warning("⚠️ 尝试复制节点但未选中任何节点");
            return;
        }

        try
        {
            _logger.InfoDetailed($"📋 开始复制节点: {SelectedNode.Name} (ID: {SelectedNode.Id})");
            CopiedNode = SelectedNode.DeepClone();
            StatusMessage = $"✅ 已复制节点: {SelectedNode.Name}";
            _logger.Info($"✅ 复制节点成功: {SelectedNode.Name}");
        }
        catch (Exception ex)
        {
            _logger.Error($"复制节点失败: {ex.Message}");
            StatusMessage = $"❌ 复制节点失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 粘贴节点命令
    /// </summary>
    [RelayCommand]
    private async Task PasteNodeAsync()
    {
        if (SelectedNode == null || CopiedNode == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "正在粘贴节点...";

            await Task.Delay(100); // 模拟异步操作

            // 创建新的节点副本
            var pastedNode = CopiedNode.DeepClone();
            pastedNode.Id = GetNextId();
            pastedNode.Name = $"{pastedNode.Name} (副本)";
            pastedNode.YParent = SelectedNode;

            // 更新所有子节点的ID
            UpdateNodeIds(pastedNode);

            // 添加到选中节点的子节点
            SelectedNode.YChildren ??= new ObservableCollection<YTreeNodeData>();
            SelectedNode.YChildren.Add(pastedNode);
            SelectedNode.IsExpanded = true;

            // 选中新粘贴的节点
            SelectedNode = pastedNode;

            // 更新过滤数据
            ApplyFilter();

            StatusMessage = $"✅ 已粘贴节点: {pastedNode.Name}";
            _logger.Info($"粘贴节点: {pastedNode.Name}");
        }
        catch (Exception ex)
        {
            _logger.Error($"粘贴节点失败: {ex.Message}");
            StatusMessage = $"❌ 粘贴节点失败: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 保存展开状态命令
    /// </summary>
    [RelayCommand]
    private async Task SaveExpandedState()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在保存展开状态...";

            var state = CollectCurrentState();
            var success = await TreeViewStateManager.SaveStateAsync(state);

            if (success)
            {
                StatusMessage = $"✅ 展开状态已保存到: {System.IO.Path.GetFileName(TreeViewStateManager.GetStateFilePath())}";
                _logger.Info($"保存展开状态成功，展开节点数: {state.ExpandedNodeIds.Count}");
            }
            else
            {
                StatusMessage = "❌ 保存展开状态失败";
                _logger.Error("保存展开状态失败");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 保存展开状态失败: {ex.Message}";
            _logger.Error($"保存展开状态失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 读取展开状态命令
    /// </summary>
    [RelayCommand]
    private async Task LoadExpandedState()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在读取展开状态...";

            var state = await TreeViewStateManager.LoadStateAsync();
            await ApplyStateToTreeView(state);

            StatusMessage = $"✅ 展开状态已读取，恢复了 {state.ExpandedNodeIds.Count} 个展开节点";
            _logger.Info($"读取展开状态成功，展开节点数: {state.ExpandedNodeIds.Count}");
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 读取展开状态失败: {ex.Message}";
            _logger.Error($"读取展开状态失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 清除保存的状态命令
    /// </summary>
    [RelayCommand]
    private void ClearSavedState()
    {
        try
        {
            var success = TreeViewStateManager.DeleteState();
            if (success)
            {
                StatusMessage = "✅ 已清除保存的展开状态";
                _logger.Info("清除保存的展开状态成功");
            }
            else
            {
                StatusMessage = "❌ 清除保存的展开状态失败";
                _logger.Error("清除保存的展开状态失败");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"❌ 清除保存的展开状态失败: {ex.Message}";
            _logger.Error($"清除保存的展开状态失败: {ex}");
        }
    }

    #endregion

    #region TreeView事件命令

    /// <summary>
    /// 节点选择变化命令
    /// </summary>
    [RelayCommand]
    private void NodeSelectionChanged(object selectedNode)
    {
        _logger.Debug($"🎯 节点选择变化: {selectedNode?.GetType().Name}");

        if (selectedNode is YTreeNodeData node)
        {
            _logger.Info($"📍 选中节点: {node.Name} (ID: {node.Id})");
            StatusMessage = $"已选中: {node.Name}";
        }
        else
        {
            _logger.Info("📍 取消选择");
            StatusMessage = "准备就绪";
        }
    }

    /// <summary>
    /// 节点展开命令
    /// </summary>
    [RelayCommand]
    private void NodeExpanded(object expandedNode)
    {
        if (expandedNode is YTreeNodeData node)
        {
            _logger.Debug($"📂 节点展开: {node.Name}");
            node.IsExpanded = true;
        }
    }

    /// <summary>
    /// 节点折叠命令
    /// </summary>
    [RelayCommand]
    private void NodeCollapsed(object collapsedNode)
    {
        if (collapsedNode is YTreeNodeData node)
        {
            _logger.Debug($"📁 节点折叠: {node.Name}");
            node.IsExpanded = false;
        }
    }

    #endregion

    #region 搜索过滤功能

    /// <summary>
    /// 过滤文本变化时的处理
    /// </summary>
    partial void OnFilterTextChanged(string value)
    {
        _logger.Debug($"🔍 搜索文本变化: '{value}' (长度: {value?.Length ?? 0})");

        using (_logger.Monitor("搜索过滤"))
        {
            ApplyFilter();

            if (IsFiltering)
            {
                StatusMessage = $"🔍 搜索: {value}";
                _logger.InfoDetailed($"🔍 开始搜索: '{value}'");
            }
            else
            {
                StatusMessage = "准备就绪";
                _logger.InfoDetailed("🧹 清除搜索过滤，显示所有数据");
            }
        }
    }

    /// <summary>
    /// 应用过滤
    /// </summary>
    private void ApplyFilter()
    {
        try
        {
            var originalCount = CountAllNodes(TreeData);
            _logger.Debug($"📊 开始过滤，原始节点总数: {originalCount}");

            if (string.IsNullOrWhiteSpace(FilterText))
            {
                // 没有过滤条件，显示所有数据
                FilteredTreeData = TreeData;
                _logger.Debug("📋 无过滤条件，显示全部数据");
            }
            else
            {
                // 应用过滤条件
                var filtered = new ObservableCollection<YTreeNodeData>();
                var filterText = FilterText.ToLower();
                _logger.Debug($"🔍 应用过滤条件: '{filterText}'");

                FilterNodes(TreeData, filterText, filtered);
                FilteredTreeData = filtered;

                var filteredCount = CountAllNodes(filtered);
                _logger.InfoDetailed($"🎯 过滤完成: 找到 {filteredCount} 个匹配节点 (共 {originalCount} 个)");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 应用过滤失败: {ex.Message}");
            _logger.Debug($"🔍 过滤异常详情: {ex}");
            StatusMessage = $"❌ 搜索失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 递归过滤节点
    /// </summary>
    private void FilterNodes(ObservableCollection<YTreeNodeData> source, string filter, ObservableCollection<YTreeNodeData> result)
    {
        foreach (var node in source)
        {
            var nodeMatches = node.Name.ToLower().Contains(filter) ||
                             node.Description.ToLower().Contains(filter);

            var childMatches = false;
            var filteredYChildren = new ObservableCollection<YTreeNodeData>();

            if (node.YChildren != null)
            {
                FilterNodes(node.YChildren, filter, filteredYChildren);
                childMatches = filteredYChildren.Count > 0;
            }

            if (nodeMatches || childMatches)
            {
                var clonedNode = node.Clone();
                clonedNode.YChildren = filteredYChildren;
                clonedNode.IsExpanded = childMatches; // 如果有匹配的子节点，自动展开
                result.Add(clonedNode);
            }
        }
    }

    #endregion

    #region 导航接口实现

    public void OnNavigatedTo(NavigationContext navigationContext)
    {
        _logger.Info("导航到TreeView页面");
    }

    public bool IsNavigationTarget(NavigationContext navigationContext) => true;

    public void OnNavigatedFrom(NavigationContext navigationContext)
    {
        _logger.Info("离开TreeView页面");
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 创建测试数据 - 使用编号组装方式，测试四种图标类型
    /// </summary>
    private void CreateTestDataProfessional()
    {
        _logger.Info("🔄 开始创建TreeView测试数据 - 使用编号组装，测试四种图标");
        Console.WriteLine("🔄 CreateTestDataProfessional 被调用");

        // 🎯 定义树形数据结构 - 四种图标类型演示
        var YTreeNodeDataArray = new YTreeNodeDataInfo[]
        {
            // === 根节点 ===
            new("1", "图标类型演示", "展示四种图标类型的使用", "folder")
                { IsExpanded = true, ParentNumber = null },

            // === 1. WPF-UI图标演示 ===
            new("10", "WPF-UI图标演示", "使用WPF-UI的SymbolRegular图标", "wpf-test")
                { IsExpanded = true, ParentNumber = "1", WpfUiSymbol = SymbolRegular.Fluent24 },
            new("101", "用户管理", "用户管理模块", "wpf-test")
                { ParentNumber = "10", WpfUiSymbol = SymbolRegular.Person24 },
            new("102", "设置中心", "系统设置", "wpf-test")
                { ParentNumber = "10", WpfUiSymbol = SymbolRegular.Settings24 },
            new("103", "文档管理", "文档管理系统", "wpf-test")
                { ParentNumber = "10", WpfUiSymbol = SymbolRegular.Document24 },
            new("104", "数据分析", "数据分析工具", "wpf-test")
                { ParentNumber = "10", WpfUiSymbol = SymbolRegular.ChartMultiple24 },

            // === 2. Zylo图标演示 ===
            new("20", "Zylo图标演示", "使用Zylo自定义图标", "zylo-test")
                { IsExpanded = true, ParentNumber = "1", ZyloSymbol = ZyloSymbol.ICO },
            new("201", "ICO图标1", "Zylo ICO图标", "zylo-test")
                { ParentNumber = "20", ZyloSymbol = ZyloSymbol.ICO },
            new("202", "ICO图标2", "另一个ICO图标", "zylo-test")
                { ParentNumber = "20", ZyloSymbol = ZyloSymbol.ICO },
            new("203", "ICO图标3", "第三个ICO图标", "zylo-test")
                { ParentNumber = "20", ZyloSymbol = ZyloSymbol.ICO },

            // === 3. Emoji图标演示 ===
            new("30", "Emoji图标演示", "使用Emoji字符作为图标", "emoji-test")
                { IsExpanded = true, ParentNumber = "1", Emoji = "😀" },
            new("301", "开心表情", "开心的表情", "emoji-test")
                { ParentNumber = "30", Emoji = "😊" },
            new("302", "工作状态", "工作相关", "emoji-test")
                { ParentNumber = "30", Emoji = "💼" },
            new("303", "学习状态", "学习相关", "emoji-test")
                { ParentNumber = "30", Emoji = "📚" },
            new("304", "运动状态", "运动相关", "emoji-test")
                { ParentNumber = "30", Emoji = "🏃" },
            new("305", "美食分享", "美食相关", "emoji-test")
                { ParentNumber = "30", Emoji = "🍕" },

            // === 4. 默认图标演示（基于NodeType） ===
            new("40", "默认图标演示", "基于NodeType自动选择图标", "folder")
                { IsExpanded = true, ParentNumber = "1" },
            new("401", "JavaScript文件", "JS代码文件", "javascript")
                { ParentNumber = "40" },
            new("402", "Python文件", "Python代码文件", "python")
                { ParentNumber = "40" },
            new("403", "MySQL数据库", "MySQL数据库", "mysql")
                { ParentNumber = "40" },
            new("404", "用户账户", "用户账户", "user")
                { ParentNumber = "40" },
            new("405", "项目任务", "项目任务", "task")
                { ParentNumber = "40" },
            new("406", "Bug报告", "Bug报告", "bug")
                { ParentNumber = "40" },
            new("407", "新功能", "新功能开发", "feature")
                { ParentNumber = "40" },
            new("408", "安全证书", "安全证书", "certificate")
                { ParentNumber = "40" },
            new("409", "API接口", "API接口", "api")
                { ParentNumber = "40" },
        };

        // 📋 使用专业的组装方法构建树形结构
        Console.WriteLine($"🔄 准备组装 {YTreeNodeDataArray.Length} 个节点");
        TreeData = AssembleTreeByParentNumber(YTreeNodeDataArray);
        Console.WriteLine($"✅ 组装完成，TreeData.Count = {TreeData.Count}");

        // 🔍 关键步骤：应用过滤器（更新FilteredTreeData）
        ApplyFilter();
        Console.WriteLine($"✅ 过滤器应用完成，FilteredTreeData.Count = {FilteredTreeData.Count}");

        _logger.Info($"✅ TreeView测试数据创建完成，共 {TreeData.Count} 个根节点，使用编号组装方式");
    }



    /// <summary>
    /// 计算所有节点数量
    /// </summary>
    private int CountAllNodes(ObservableCollection<YTreeNodeData> nodes)
    {
        int count = nodes.Count;
        foreach (var node in nodes)
        {
            if (node.YChildren != null)
            {
                count += CountAllNodes(node.YChildren);
            }
        }
        return count;
    }

    /// <summary>
    /// 获取下一个ID
    /// </summary>
    private int GetNextId()
    {
        return GetMaxId(TreeData) + 1;
    }

    /// <summary>
    /// 获取最大ID
    /// </summary>
    private int GetMaxId(ObservableCollection<YTreeNodeData> nodes)
    {
        int maxId = 0;
        foreach (var node in nodes)
        {
            if (node.Id > maxId)
                maxId = node.Id;

            if (node.YChildren != null)
            {
                var childMaxId = GetMaxId(node.YChildren);
                if (childMaxId > maxId)
                    maxId = childMaxId;
            }
        }
        return maxId;
    }

    /// <summary>
    /// 根据父节点类型获取子节点类型（通用化）
    /// </summary>
    private string GetChildNodeType(string parentType)
    {
        return parentType.ToLower() switch
        {
            "organization" => "group",
            "group" => "user",
            "project" => "task",
            "folder" => "file",
            "database" => "table",
            "table" => "column",
            _ => "item"
        };
    }

    /// <summary>
    /// 从树中移除节点
    /// </summary>
    private void RemoveNodeFromTree(YTreeNodeData nodeToRemove)
    {
        // 如果是根节点
        if (TreeData.Contains(nodeToRemove))
        {
            TreeData.Remove(nodeToRemove);
            return;
        }

        // 递归查找并移除
        RemoveNodeRecursive(TreeData, nodeToRemove);
    }

    /// <summary>
    /// 递归移除节点
    /// </summary>
    private bool RemoveNodeRecursive(ObservableCollection<YTreeNodeData> nodes, YTreeNodeData nodeToRemove)
    {
        foreach (var node in nodes.ToList())
        {
            if (node.YChildren != null && node.YChildren.Contains(nodeToRemove))
            {
                node.YChildren.Remove(nodeToRemove);
                return true;
            }

            if (node.YChildren != null && RemoveNodeRecursive(node.YChildren, nodeToRemove))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 设置所有节点的展开状态
    /// </summary>
    private void SetAllNodesExpanded(ObservableCollection<YTreeNodeData> nodes, bool isExpanded)
    {
        _logger.Debug($"🔄 设置 {nodes.Count} 个节点的展开状态为: {isExpanded}");

        foreach (var node in nodes)
        {
            _logger.Debug($"📝 设置节点 '{node.Name}' 展开状态: {node.IsExpanded} → {isExpanded}");
            node.IsExpanded = isExpanded;

            if (node.YChildren != null && node.YChildren.Count > 0)
            {
                _logger.Debug($"🔄 递归设置节点 '{node.Name}' 的 {node.YChildren.Count} 个子节点");
                SetAllNodesExpanded(node.YChildren, isExpanded);
            }
        }

        _logger.Debug($"✅ 完成设置所有节点展开状态为: {isExpanded}");
    }

    /// <summary>
    /// 更新节点及其子节点的ID
    /// </summary>
    private void UpdateNodeIds(YTreeNodeData node)
    {
        if (node.YChildren != null)
        {
            foreach (var child in node.YChildren)
            {
                child.Id = GetNextId();
                child.YParent = node;
                UpdateNodeIds(child);
            }
        }
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// SelectedNode变化时更新状态
    /// </summary>
    partial void OnSelectedNodeChanged(YTreeNodeData? oldValue, YTreeNodeData? newValue)
    {
        _logger.Info($"🔥🔥🔥 选中节点变化被触发！！！");
        _logger.Info($"🔄 选中节点变化: '{oldValue?.Name}' -> '{newValue?.Name}'");
        _logger.Info($"🔥 这个方法被调用了，说明绑定工作正常！");

        // 清除旧节点的选中状态
        if (oldValue != null)
        {
            oldValue.IsSelected = false;
            _logger.Info($"📍 清除旧节点选中状态: {oldValue.Name}");
        }

        // 设置新节点的选中状态
        if (newValue != null)
        {
            newValue.IsSelected = true;
            _logger.Info($"📍 选中新节点: {newValue.Name} (ID: {newValue.Id}, 类型: {newValue.NodeType})");

            // 🔥 选中新节点时自动退出编辑模式
            if (IsEditingNode)
            {
                _logger.Info("🔄 选中新节点，自动退出编辑模式");
                IsEditingNode = false;
                EditingNodeName = string.Empty;
                EditingNodeDescription = string.Empty;
            }

            _logger.Info($"🎯 HasSelectedNode: {HasSelectedNode}");
            _logger.Info($"🎯 CanEditSelectedNode: {CanEditSelectedNode}");
            _logger.Info($"🎯 CanDeleteSelectedNode: {CanDeleteSelectedNode}");

            StatusMessage = $"📍 选中节点: {newValue.Name}";
        }
        else
        {
            _logger.Info("📍 清除节点选择");
            if (!IsEditingNode && !IsLoading)
            {
                StatusMessage = "准备就绪";
            }
        }

        _logger.Info($"🔥🔥🔥 选中节点变化处理完成！！！");
    }

    #endregion

    #region 专业组装方法 - 学习MainViewModel

    /// <summary>
    /// 树节点数据信息 - 学习NavigationData的设计，支持三图标
    /// </summary>
    public class YTreeNodeDataInfo
    {
        public string Number { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string NodeType { get; set; }
        public string? ParentNumber { get; set; }
        public bool IsExpanded { get; set; }

        // 🎯 三图标支持 - 学习NavigationData
        public SymbolRegular? WpfUiSymbol { get; set; }
        public ZyloSymbol? ZyloSymbol { get; set; }
        public string? Emoji { get; set; }

        public YTreeNodeDataInfo(string number, string name, string description, string nodeType)
        {
            Number = number;
            Name = name;
            Description = description;
            NodeType = nodeType;
        }
    }

    /// <summary>
    /// 按父级编号组装树形结构 - 学习NavigationItemHelper.AssembleByParentNumber
    /// </summary>
    private ObservableCollection<YTreeNodeData> AssembleTreeByParentNumber(YTreeNodeDataInfo[] YTreeNodeDataArray)
    {
        var result = new ObservableCollection<YTreeNodeData>();
        var allNodes = new Dictionary<string, YTreeNodeData>();

        _logger.Debug($"🔄 开始组装树形结构，共 {YTreeNodeDataArray.Length} 个节点");

        // 第一步：创建所有节点
        foreach (var data in YTreeNodeDataArray)
        {
            var node = CreateTreeNode(data);
            allNodes[data.Number] = node;
            _logger.Debug($"📝 创建节点: {data.Name} (编号: {data.Number})");
        }

        // 第二步：建立父子关系
        foreach (var data in YTreeNodeDataArray)
        {
            var node = allNodes[data.Number];

            if (!string.IsNullOrEmpty(data.ParentNumber) && allNodes.ContainsKey(data.ParentNumber))
            {
                // 有父节点，添加到父节点的子集合中
                var parentNode = allNodes[data.ParentNumber];

                if (parentNode.YChildren == null)
                {
                    parentNode.YChildren = new ObservableCollection<YTreeNodeData>();
                }

                parentNode.YChildren.Add(node);
                node.YParent = parentNode;

                _logger.Debug($"🔗 建立关系: {node.Name} → {parentNode.Name}");
            }
            else
            {
                // 没有父节点，添加到顶级集合中
                result.Add(node);
                _logger.Debug($"🌳 添加根节点: {node.Name}");
            }
        }

        _logger.Info($"✅ 树形结构组装完成，根节点数: {result.Count}，总节点数: {allNodes.Count}");
        return result;
    }

    /// <summary>
    /// 根据数据信息创建树节点 - 学习CreateNavigationItem，支持三图标
    /// </summary>
    private YTreeNodeData CreateTreeNode(YTreeNodeDataInfo data)
    {
        return new YTreeNodeData
        {
            Id = int.Parse(data.Number),
            Name = data.Name,
            Description = data.Description,
            NodeType = data.NodeType,
            IsExpanded = data.IsExpanded,
            YChildren = new ObservableCollection<YTreeNodeData>(),

            // 🎯 设置三图标 - 学习NavigationItemHelper.CreateNavigationItem
            WpfUiSymbol = data.WpfUiSymbol,
            ZyloSymbol = data.ZyloSymbol,
            Emoji = data.Emoji
        };
    }

    #endregion

    #region 便利构造函数演示

    /// <summary>
    /// 使用便利构造函数创建测试数据 - 演示新的构造函数
    /// </summary>
    private void CreateTestDataWithConstructors()
    {
        _logger.Info("🔄 使用便利构造函数创建测试数据");

        // 🏢 使用静态工厂方法创建组织
        var company = TreeNodeFactory.CreateOrganization(1, "科技创新公司", "一家专注于技术创新的现代化企业", SymbolRegular.Building24);
        company.IsExpanded = true;

        // 🏬 使用构造函数创建部门
        var techDept = new YTreeNodeData(10, "技术研发部", "负责产品技术研发和创新", "group", SymbolRegular.Code24);
        techDept.IsExpanded = true;

        var pmDept = TreeNodeFactory.Create(20, "项目管理部", "group", "负责项目规划和执行管理");
        pmDept.WpfUiSymbol = SymbolRegular.ClipboardTask24;

        // 👥 使用通用方法创建团队
        var frontendTeam = TreeNodeFactory.Create(11, "前端开发团队", "group", "负责用户界面和用户体验开发");
        frontendTeam.WpfUiSymbol = SymbolRegular.PaintBrush24;

        var backendTeam = TreeNodeFactory.Create(12, "后端开发团队", "group", "负责服务器端逻辑和API开发");
        backendTeam.WpfUiSymbol = SymbolRegular.Server24;

        // 👤 使用不同方式创建用户
        var dev1 = TreeNodeFactory.CreateUser(111, "张三", "高级前端开发工程师，专精React和Vue", SymbolRegular.Person24);
        var dev2 = new YTreeNodeData(112, "李四", "前端开发工程师，专注于移动端开发", "user", SymbolRegular.PersonCircle24);
        var dev3 = TreeNodeFactory.CreateWithEmoji(113, "王小明", "user", "UI/UX设计师，负责界面设计", "🎨");

        var backDev1 = TreeNodeFactory.CreateWithEmoji(121, "王五", "user", "后端架构师，精通微服务架构", "👨‍💼");
        var backDev2 = TreeNodeFactory.CreateWithEmoji(122, "赵六", "user", "后端开发工程师，专注于数据库优化", "👩‍💼");

        var pm1 = TreeNodeFactory.CreateWithEmoji(201, "陈七", "user", "资深项目经理，擅长敏捷开发管理", "👨‍💼");

        // 🔗 建立层级关系
        frontendTeam.YChildren.Add(dev1);
        frontendTeam.YChildren.Add(dev2);
        frontendTeam.YChildren.Add(dev3);

        backendTeam.YChildren.Add(backDev1);
        backendTeam.YChildren.Add(backDev2);

        techDept.YChildren.Add(frontendTeam);
        techDept.YChildren.Add(backendTeam);

        pmDept.YChildren.Add(pm1);

        company.YChildren.Add(techDept);
        company.YChildren.Add(pmDept);

        // 设置父子关系
        SetParentRelationships(company);

        TreeData.Clear();
        TreeData.Add(company);

        ApplyFilter();

        _logger.Info($"✅ 使用便利构造函数创建测试数据完成，共 {TreeData.Count} 个根节点");
    }

    /// <summary>
    /// 递归设置父子关系
    /// </summary>
    private void SetParentRelationships(YTreeNodeData parent)
    {
        if (parent.YChildren != null)
        {
            foreach (var child in parent.YChildren)
            {
                child.YParent = parent;
                SetParentRelationships(child);
            }
        }
    }

    #endregion

    #region 扩展图标演示

    /// <summary>
    /// 创建扩展图标演示数据
    /// </summary>
    private void CreateExtendedIconDemo()
    {
        _logger.Info("🎨 创建扩展图标演示数据");

        TreeData.Clear();

        // 🗂️ 文件系统演示
        var fileSystemRoot = TreeNodeFactory.Create(1, "文件系统演示", "folder", "展示各种文件类型图标");
        fileSystemRoot.IsExpanded = true;

        var documentsFolder = TreeNodeFactory.CreateFolder(10, "文档");
        documentsFolder.YChildren.Add(TreeNodeFactory.Create(101, "报告.pdf", "pdf"));
        documentsFolder.YChildren.Add(TreeNodeFactory.Create(102, "数据.xlsx", "excel"));
        documentsFolder.YChildren.Add(TreeNodeFactory.Create(103, "演示.pptx", "powerpoint"));
        documentsFolder.YChildren.Add(TreeNodeFactory.Create(104, "说明.docx", "word"));

        var mediaFolder = TreeNodeFactory.CreateFolder(20, "媒体");
        mediaFolder.YChildren.Add(TreeNodeFactory.Create(201, "照片.jpg", "image"));
        mediaFolder.YChildren.Add(TreeNodeFactory.Create(202, "视频.mp4", "video"));
        mediaFolder.YChildren.Add(TreeNodeFactory.Create(203, "音乐.mp3", "audio"));

        var codeFolder = TreeNodeFactory.CreateFolder(30, "代码");
        codeFolder.YChildren.Add(TreeNodeFactory.CreateCodeFile(301, "app.js", "javascript"));
        codeFolder.YChildren.Add(TreeNodeFactory.CreateCodeFile(302, "main.py", "python"));
        codeFolder.YChildren.Add(TreeNodeFactory.CreateCodeFile(303, "Program.cs", "csharp"));
        codeFolder.YChildren.Add(TreeNodeFactory.CreateCodeFile(304, "index.html", "html"));
        codeFolder.YChildren.Add(TreeNodeFactory.CreateCodeFile(305, "style.css", "css"));

        fileSystemRoot.YChildren.Add(documentsFolder);
        fileSystemRoot.YChildren.Add(mediaFolder);
        fileSystemRoot.YChildren.Add(codeFolder);

        // 💻 开发项目演示
        var devProjectRoot = TreeNodeFactory.CreateProject(2, "开发项目", "软件开发项目结构");
        devProjectRoot.IsExpanded = true;

        var frontendTeam = TreeNodeFactory.CreateTeam(40, "前端团队");
        frontendTeam.YChildren.Add(TreeNodeFactory.Create(401, "React组件", "react"));
        frontendTeam.YChildren.Add(TreeNodeFactory.Create(402, "Vue页面", "vue"));
        frontendTeam.YChildren.Add(TreeNodeFactory.Create(403, "TypeScript模块", "typescript"));

        var backendTeam = TreeNodeFactory.CreateTeam(50, "后端团队");
        backendTeam.YChildren.Add(TreeNodeFactory.CreateApi(501, "Node.js API"));
        backendTeam.YChildren.Add(TreeNodeFactory.CreateService(502, "Python服务"));
        backendTeam.YChildren.Add(TreeNodeFactory.CreateService(503, "Java微服务"));

        var databaseTeam = TreeNodeFactory.CreateDatabase(60, "数据库");
        databaseTeam.YChildren.Add(TreeNodeFactory.CreateDatabase(601, "MySQL数据库", "mysql"));
        databaseTeam.YChildren.Add(TreeNodeFactory.CreateDatabase(602, "MongoDB集合", "mongodb"));
        databaseTeam.YChildren.Add(TreeNodeFactory.CreateDatabase(603, "Redis缓存", "redis"));

        devProjectRoot.YChildren.Add(frontendTeam);
        devProjectRoot.YChildren.Add(backendTeam);
        devProjectRoot.YChildren.Add(databaseTeam);

        // 🏢 组织架构演示
        var orgRoot = TreeNodeFactory.CreateOrganization(3, "科技公司", "现代化科技企业");
        orgRoot.IsExpanded = true;

        var techDept = TreeNodeFactory.CreateDepartment(70, "技术部门");
        techDept.YChildren.Add(TreeNodeFactory.CreateAdmin(701, "架构师"));
        techDept.YChildren.Add(TreeNodeFactory.CreateUser(702, "开发工程师"));
        techDept.YChildren.Add(TreeNodeFactory.CreateUser(703, "测试工程师"));

        var securityDept = TreeNodeFactory.Create(80, "安全部门", "security");
        securityDept.YChildren.Add(TreeNodeFactory.CreateCertificate(801, "安全证书"));
        securityDept.YChildren.Add(TreeNodeFactory.CreateKey(802, "访问密钥"));
        securityDept.YChildren.Add(TreeNodeFactory.Create(803, "防火墙", "firewall"));

        orgRoot.YChildren.Add(techDept);
        orgRoot.YChildren.Add(securityDept);

        // 设置父子关系
        SetParentRelationships(fileSystemRoot);
        SetParentRelationships(devProjectRoot);
        SetParentRelationships(orgRoot);

        TreeData.Add(fileSystemRoot);
        TreeData.Add(devProjectRoot);
        TreeData.Add(orgRoot);

        ApplyFilter();

        _logger.Info($"✅ 扩展图标演示数据创建完成，共 {TreeData.Count} 个根节点");
    }

    #endregion

    #region 状态保存和读取

    /// <summary>
    /// 收集当前TreeView状态
    /// </summary>
    /// <returns>当前状态</returns>
    private TreeViewState CollectCurrentState()
    {
        var state = new TreeViewState
        {
            SearchText = FilterText,
            IsLargeFont = false, // 暂时硬编码，后续可以添加字体切换状态
            SelectedNodeId = SelectedNode?.Id,
            SavedAt = DateTime.Now,
            Notes = $"TreeView状态保存 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}"
        };

        // 收集所有展开的节点ID
        CollectExpandedNodeIds(TreeData, state.ExpandedNodeIds);

        return state;
    }

    /// <summary>
    /// 递归收集展开的节点ID
    /// </summary>
    /// <param name="nodes">节点集合</param>
    /// <param name="expandedIds">展开ID列表</param>
    private void CollectExpandedNodeIds(ObservableCollection<YTreeNodeData> nodes, List<int> expandedIds)
    {
        foreach (var node in nodes)
        {
            if (node.IsExpanded)
            {
                expandedIds.Add(node.Id);
            }

            if (node.YChildren?.Count > 0)
            {
                CollectExpandedNodeIds(node.YChildren, expandedIds);
            }
        }
    }

    /// <summary>
    /// 将状态应用到TreeView
    /// </summary>
    /// <param name="state">要应用的状态</param>
    private async Task ApplyStateToTreeView(TreeViewState state)
    {
        // 恢复搜索文本
        FilterText = state.SearchText;

        // 恢复字体设置（暂时跳过，后续可以添加字体切换状态）
        // IsLargeFont = state.IsLargeFont;

        // 恢复展开状态
        await Task.Run(() =>
        {
            ApplyExpandedState(TreeData, state.ExpandedNodeIds);
        });

        // 恢复选中节点
        if (state.SelectedNodeId.HasValue)
        {
            var selectedNode = FindNodeById(TreeData, state.SelectedNodeId.Value);
            if (selectedNode != null)
            {
                SelectedNode = selectedNode;
            }
        }

        // 应用过滤器
        ApplyFilter();
    }

    /// <summary>
    /// 递归应用展开状态
    /// </summary>
    /// <param name="nodes">节点集合</param>
    /// <param name="expandedIds">展开ID列表</param>
    private void ApplyExpandedState(ObservableCollection<YTreeNodeData> nodes, List<int> expandedIds)
    {
        foreach (var node in nodes)
        {
            node.IsExpanded = expandedIds.Contains(node.Id);

            if (node.YChildren?.Count > 0)
            {
                ApplyExpandedState(node.YChildren, expandedIds);
            }
        }
    }

    /// <summary>
    /// 根据ID查找节点
    /// </summary>
    /// <param name="nodes">节点集合</param>
    /// <param name="id">节点ID</param>
    /// <returns>找到的节点，如果没找到则返回null</returns>
    private YTreeNodeData? FindNodeById(ObservableCollection<YTreeNodeData> nodes, int id)
    {
        foreach (var node in nodes)
        {
            if (node.Id == id)
            {
                return node;
            }

            if (node.YChildren?.Count > 0)
            {
                var found = FindNodeById(node.YChildren, id);
                if (found != null)
                {
                    return found;
                }
            }
        }

        return null;
    }

    #endregion
}
