using System.Collections.ObjectModel;
using System.Windows.Input;
using AlphaPM.Builders;
using AlphaPM.Models.DWG;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Examples;

/// <summary>
/// TreeNodeData 使用示例 ViewModel
/// 展示如何正确绑定 ZyloTreeView 并添加数据
/// </summary>
public partial class TreeNodeDataExampleViewModel : ObservableValidator
{
    #region ZyloTreeView 绑定属性

    /// <summary>
    /// 树形数据源 - 绑定到 ZyloTreeView.TreeData
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<YTreeNodeData> treeData = new();

    /// <summary>
    /// 选中的节点 - 双向绑定到 ZyloTreeView.SelectedNode
    /// </summary>
    [ObservableProperty]
    private YTreeNodeData? selectedNode;

    /// <summary>
    /// 搜索文本 - 绑定到 ZyloTreeView.FilterText
    /// </summary>
    [ObservableProperty]
    private string filterText = string.Empty;

    /// <summary>
    /// 是否显示搜索框
    /// </summary>
    [ObservableProperty]
    private bool showSearchBox = true;

    /// <summary>
    /// 是否使用大字体
    /// </summary>
    [ObservableProperty]
    private bool isLargeFont = false;

    /// <summary>
    /// 是否启用拖拽
    /// </summary>
    [ObservableProperty]
    private bool enableDragDrop = true;

    #endregion

    #region 构造函数

    public TreeNodeDataExampleViewModel()
    {
        InitializeData();
    }

    #endregion

    #region 数据初始化

    /// <summary>
    /// 初始化示例数据
    /// </summary>
    private void InitializeData()
    {
        // 方法1：使用构建器创建复杂的树形结构
        var year2024 = TreeNodeDataBuilder
            .CreateYear("2024")
            .WithManager("张总监")
            .WithDescription("2024年度项目管理")
            .AddChildren(
                TreeNodeDataBuilder
                    .CreateProject("住宅项目A")
                    .WithManager("李经理")
                    .WithBudget(5000)
                    .WithDateRange(new DateTime(2024, 1, 1), new DateTime(2024, 12, 31))
                    .WithProjectStatus("进行中")
                    .WithProgress(65)
                    .AddChildren(
                        TreeNodeDataBuilder
                            .CreateBuilding("1#楼")
                            .WithBuildingInfo("住宅", 8500, 18)
                            .WithDesigner("王设计师")
                            .WithRemarks("主力户型"),
                        TreeNodeDataBuilder
                            .CreateBuilding("2#楼")
                            .WithBuildingInfo("住宅", 7200, 15)
                            .WithDesigner("赵设计师")
                    ),
                TreeNodeDataBuilder
                    .CreateProject("商业项目B")
                    .WithManager("陈经理")
                    .WithBudget(8000)
                    .WithProjectStatus("规划中")
                    .WithProgress(25)
            );

        var year2025 = TreeNodeDataBuilder
            .CreateYear("2025")
            .WithManager("刘总监")
            .AddChild(
                TreeNodeDataBuilder
                    .CreateProject("办公项目C")
                    .WithManager("孙经理")
                    .WithBudget(12000)
                    .WithProjectStatus("设计中")
            );

        // 添加到数据源
        TreeData.Add(year2024.Build());
        TreeData.Add(year2025.Build());

        // 方法2：直接使用 TreeNodeData 的便捷方法
        AddDirectExample();
    }

    /// <summary>
    /// 直接使用 TreeNodeData 方法的示例
    /// </summary>
    private void AddDirectExample()
    {
        var year2023 = new TreeNodeData("2023")
        {
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            Manager = "老张",
            Description = "2023年度项目"
        };

        var project = new TreeNodeData("历史项目")
        {
            NodeType = DWGNodeTypes.Project,
            Manager = "老李",
            Budget = 3000,
            ProjectStatus = "已完成"
        };

        var building = new TreeNodeData("历史建筑")
        {
            NodeType = DWGNodeTypes.Building,
            BuildingType = "办公",
            BuildingArea = 5000,
            FloorCount = 10
        };

        // 使用便捷方法建立关系
        year2023.AddChild(project);
        project.AddChild(building);

        TreeData.Add(year2023);
    }

    #endregion

    #region 命令

    /// <summary>
    /// 节点选择变化命令
    /// </summary>
    [RelayCommand]
    private void OnNodeSelectionChanged(YTreeNodeData? node)
    {
        SelectedNode = node;
        
        if (node is TreeNodeData treeNode)
        {
            // 处理 TreeNodeData 特有的逻辑
            var level = treeNode.GetDWGNodeLevel();
            var levelName = treeNode.GetDWGNodeLevelName();
            // 可以在这里添加更多业务逻辑
        }
    }

    /// <summary>
    /// 节点展开命令
    /// </summary>
    [RelayCommand]
    private void OnNodeExpanded(YTreeNodeData node)
    {
        node.IsExpanded = true;
    }

    /// <summary>
    /// 节点折叠命令
    /// </summary>
    [RelayCommand]
    private void OnNodeCollapsed(YTreeNodeData node)
    {
        node.IsExpanded = false;
    }

    /// <summary>
    /// 添加年份节点命令
    /// </summary>
    [RelayCommand]
    private void AddYear()
    {
        var currentYear = DateTime.Now.Year.ToString();
        var yearNode = TreeNodeDataBuilder
            .CreateYear(currentYear)
            .WithManager("新管理员")
            .Build();

        TreeData.Add(yearNode);
    }

    /// <summary>
    /// 添加项目节点命令
    /// </summary>
    [RelayCommand]
    private void AddProject()
    {
        if (SelectedNode is TreeNodeData selectedTreeNode && selectedTreeNode.IsDWGYearNode())
        {
            var projectNode = TreeNodeDataBuilder
                .CreateProject($"新项目 {DateTime.Now:HHmmss}")
                .WithManager("项目经理")
                .WithBudget(1000)
                .WithProjectStatus("规划中")
                .Build();

            selectedTreeNode.AddChild(projectNode);
            selectedTreeNode.IsExpanded = true;
        }
    }

    /// <summary>
    /// 添加建筑节点命令
    /// </summary>
    [RelayCommand]
    private void AddBuilding()
    {
        if (SelectedNode is TreeNodeData selectedTreeNode && selectedTreeNode.IsDWGProjectNode())
        {
            var buildingNode = TreeNodeDataBuilder
                .CreateBuilding($"新建筑 {DateTime.Now:HHmmss}")
                .WithBuildingInfo("住宅", 5000, 12)
                .WithDesigner("设计师")
                .Build();

            selectedTreeNode.AddChild(buildingNode);
            selectedTreeNode.IsExpanded = true;
        }
    }

    /// <summary>
    /// 删除选中节点命令
    /// </summary>
    [RelayCommand]
    private void DeleteSelected()
    {
        if (SelectedNode is TreeNodeData selectedTreeNode)
        {
            var parent = selectedTreeNode.GetParent();
            if (parent != null)
            {
                parent.RemoveChild(selectedTreeNode);
            }
            else
            {
                // 根节点，从 TreeData 中移除
                TreeData.Remove(selectedTreeNode);
            }
        }
    }

    /// <summary>
    /// 切换字体大小命令
    /// </summary>
    [RelayCommand]
    private void ToggleFontSize()
    {
        IsLargeFont = !IsLargeFont;
    }

    #endregion

    #region 拖拽支持（可选）

    // 如果需要拖拽功能，可以实现 IDragSource 和 IDropTarget
    // 这里提供一个简单的示例

    public void StartDrag(IDragInfo dragInfo)
    {
        if (dragInfo.SourceItem is TreeNodeData node)
        {
            dragInfo.Data = node;
            dragInfo.Effects = System.Windows.DragDropEffects.Move;
        }
    }

    public bool CanStartDrag(IDragInfo dragInfo)
    {
        return dragInfo.SourceItem is TreeNodeData;
    }

    public void DragOver(IDropInfo dropInfo)
    {
        if (dropInfo.Data is TreeNodeData sourceNode && 
            dropInfo.TargetItem is TreeNodeData targetNode)
        {
            // 简单的拖拽验证逻辑
            dropInfo.DropTargetAdorner = DropTargetAdorners.Insert;
            dropInfo.Effects = System.Windows.DragDropEffects.Move;
        }
    }

    public void Drop(IDropInfo dropInfo)
    {
        if (dropInfo.Data is TreeNodeData sourceNode && 
            dropInfo.TargetItem is TreeNodeData targetNode)
        {
            // 执行拖拽操作
            var oldParent = sourceNode.GetParent();
            oldParent?.RemoveChild(sourceNode);
            
            targetNode.AddChild(sourceNode);
            targetNode.IsExpanded = true;
        }
    }

    #endregion
}
