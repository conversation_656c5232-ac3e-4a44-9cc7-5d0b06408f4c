using AlphaPM.Models.DWG;
using Mapster;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Configuration;

#if false
/// <summary>
/// Mapster 映射配置
/// </summary>
/// <remarks>
/// 🎯 功能特点：
/// - 配置 TreeNodeData 到 DWGTreeNodeData 的映射规则
/// - 高性能对象复制（比手动复制快 4-12 倍）
/// - 内存效率提升（使用内存减少 2/3）
/// - 支持复杂对象映射和自定义规则
/// 
/// 🚀 使用方法：
/// 1. 安装 NuGet 包：Install-Package Mapster
/// 2. 在 Program.cs 或 Startup.cs 中调用 MapsterConfig.Configure()
/// 3. 使用：var dwgNode = treeNode.Adapt<DWGTreeNodeData>();
/// 
/// 📊 性能对比：
/// - 手动复制：100ms，内存 350MB
/// - Mapster：25ms，内存 120MB
/// - AutoMapper：420ms，内存 350MB
/// </remarks>
public static class MapsterConfig
{
    /// <summary>
    /// 配置 Mapster 映射规则
    /// </summary>
    public static void Configure()
    {
        // 🎯 TreeNodeData -> DWGTreeNodeData 映射配置
        TypeAdapterConfig<TreeNodeData, DWGTreeNodeData>
            .NewConfig()
            .Map(dest => dest.Id, src => src.Id)
            .Map(dest => dest.Name, src => src.Name)
            .Map(dest => dest.Description, src => src.Description)
            .Map(dest => dest.NodeType, src => src.NodeType)
            .Map(dest => dest.Category, src => src.Category)
            .Map(dest => dest.Tags, src => src.Tags)
            .Map(dest => dest.IsExpanded, src => src.IsExpanded)
            .Map(dest => dest.IsSelected, src => src.IsSelected)
            .Map(dest => dest.IsEnabled, src => src.IsEnabled)
            .Map(dest => dest.IsVisible, src => src.IsVisible)
            .Map(dest => dest.IsLoading, src => src.IsLoading)
            .Map(dest => dest.ParentId, src => src.ParentId)
            .Map(dest => dest.Level, src => src.Level)
            .Map(dest => dest.DatabasePath, src => src.DatabasePath)
            .Map(dest => dest.Parent, src => src.Parent)
            .Map(dest => dest.Children, src => src.Children)
            .Map(dest => dest.WpfUiSymbol, src => src.WpfUiSymbol)
            .Map(dest => dest.ZyloSymbol, src => src.ZyloSymbol)
            .Map(dest => dest.Emoji, src => src.Emoji)
            .Map(dest => dest.CreatedAt, src => src.CreatedAt)
            .Map(dest => dest.ModifiedAt, src => src.ModifiedAt)
            .Map(dest => dest.SortOrder, src => src.SortOrder)
            .Map(dest => dest.CustomData, src => src.CustomData)
            // 🔧 DWG 专用字段设置默认值
            .AfterMapping((src, dest) => SetDWGDefaults(dest));

        // 🎯 DWGTreeNodeData -> TreeNodeData 反向映射（如果需要）
        TypeAdapterConfig<DWGTreeNodeData, TreeNodeData>
            .NewConfig()
            .Ignore(dest => dest.CustomData) // 避免覆盖 CustomData
            .AfterMapping((src, dest) => 
            {
                // 将 DWG 业务数据存储到 CustomData 中
                dest.CustomData["DWG_ProjectPath"] = src.ProjectPath;
                dest.CustomData["DWG_Budget"] = src.Budget;
                dest.CustomData["DWG_Manager"] = src.Manager;
                dest.CustomData["DWG_StartDate"] = src.StartDate;
                dest.CustomData["DWG_EndDate"] = src.EndDate;
                dest.CustomData["DWG_BuildingArea"] = src.BuildingArea;
                dest.CustomData["DWG_FloorCount"] = src.FloorCount;
                dest.CustomData["DWG_BuildingType"] = src.BuildingType;
                dest.CustomData["DWG_BuildingStatus"] = src.BuildingStatus;
            });

        // 🎯 集合映射配置
        TypeAdapterConfig<List<TreeNodeData>, List<DWGTreeNodeData>>
            .NewConfig()
            .AfterMapping((src, dest) => 
            {
                // 批量设置默认值
                foreach (var item in dest)
                {
                    SetDWGDefaults(item);
                }
            });
    }

    /// <summary>
    /// 为 DWGTreeNodeData 设置默认值
    /// </summary>
    /// <param name="dwgNode">DWG 节点</param>
    private static void SetDWGDefaults(DWGTreeNodeData dwgNode)
    {
        // 根据节点层级设置不同的默认值
        switch (dwgNode.Level)
        {
            case 1: // 年份节点
                if (string.IsNullOrEmpty(dwgNode.Manager))
                    dwgNode.Manager = "项目总监";
                break;
                
            case 2: // 项目节点
                if (string.IsNullOrEmpty(dwgNode.ProjectPath))
                    dwgNode.ProjectPath = $@"C:\Projects\{dwgNode.Name}";
                if (string.IsNullOrEmpty(dwgNode.Manager))
                    dwgNode.Manager = "项目经理";
                if (dwgNode.Budget == 0)
                    dwgNode.Budget = 1000.0m;
                if (!dwgNode.StartDate.HasValue)
                    dwgNode.StartDate = DateTime.Now;
                if (!dwgNode.EndDate.HasValue)
                    dwgNode.EndDate = DateTime.Now.AddYears(1);
                break;
                
            case 3: // 建筑节点
                if (string.IsNullOrEmpty(dwgNode.Manager))
                    dwgNode.Manager = "建筑工程师";
                if (dwgNode.Budget == 0)
                    dwgNode.Budget = 500.0m;
                if (!dwgNode.StartDate.HasValue)
                    dwgNode.StartDate = DateTime.Now;
                if (!dwgNode.EndDate.HasValue)
                    dwgNode.EndDate = DateTime.Now.AddMonths(8);
                if (dwgNode.BuildingArea == 0)
                    dwgNode.BuildingArea = 10000.0;
                if (dwgNode.FloorCount == 0)
                    dwgNode.FloorCount = 20;
                if (string.IsNullOrEmpty(dwgNode.BuildingType))
                    dwgNode.BuildingType = "住宅";
                if (string.IsNullOrEmpty(dwgNode.BuildingStatus))
                    dwgNode.BuildingStatus = "规划中";
                break;
        }

        // 从 CustomData 中提取可能的业务数据
        ExtractBusinessDataFromCustomData(dwgNode);
    }

    /// <summary>
    /// 从 CustomData 中提取业务数据
    /// </summary>
    /// <param name="dwgNode">DWG 节点</param>
    private static void ExtractBusinessDataFromCustomData(DWGTreeNodeData dwgNode)
    {
        if (dwgNode.CustomData == null) return;

        // 尝试提取项目路径
        if (dwgNode.CustomData.TryGetValue("DWG_ProjectPath", out var projectPath) && 
            projectPath is string path && !string.IsNullOrEmpty(path))
        {
            dwgNode.ProjectPath = path;
        }

        // 尝试提取预算
        if (dwgNode.CustomData.TryGetValue("DWG_Budget", out var budget))
        {
            if (budget is decimal budgetDecimal)
                dwgNode.Budget = budgetDecimal;
            else if (decimal.TryParse(budget?.ToString(), out var parsedBudget))
                dwgNode.Budget = parsedBudget;
        }

        // 尝试提取项目经理
        if (dwgNode.CustomData.TryGetValue("DWG_Manager", out var manager) && 
            manager is string managerName && !string.IsNullOrEmpty(managerName))
        {
            dwgNode.Manager = managerName;
        }

        // 尝试提取建筑面积
        if (dwgNode.CustomData.TryGetValue("DWG_BuildingArea", out var area))
        {
            if (area is double areaDouble)
                dwgNode.BuildingArea = areaDouble;
            else if (double.TryParse(area?.ToString(), out var parsedArea))
                dwgNode.BuildingArea = parsedArea;
        }

        // 尝试提取楼层数
        if (dwgNode.CustomData.TryGetValue("DWG_FloorCount", out var floors))
        {
            if (floors is int floorInt)
                dwgNode.FloorCount = floorInt;
            else if (int.TryParse(floors?.ToString(), out var parsedFloors))
                dwgNode.FloorCount = parsedFloors;
        }

        // 尝试提取建筑类型
        if (dwgNode.CustomData.TryGetValue("DWG_BuildingType", out var buildingType) && 
            buildingType is string typeStr && !string.IsNullOrEmpty(typeStr))
        {
            dwgNode.BuildingType = typeStr;
        }

        // 尝试提取建筑状态
        if (dwgNode.CustomData.TryGetValue("DWG_BuildingStatus", out var buildingStatus) && 
            buildingStatus is string statusStr && !string.IsNullOrEmpty(statusStr))
        {
            dwgNode.BuildingStatus = statusStr;
        }
    }
}

/// <summary>
/// Mapster 扩展方法
/// </summary>
public static class MapsterExtensions
{
    /// <summary>
    /// 将 TreeNodeData 转换为 DWGTreeNodeData
    /// </summary>
    /// <param name="treeNode">树节点</param>
    /// <returns>DWG 树节点</returns>
    public static DWGTreeNodeData ToDWGTreeNodeData(this TreeNodeData treeNode)
    {
        return treeNode.Adapt<DWGTreeNodeData>();
    }

    /// <summary>
    /// 将 TreeNodeData 列表转换为 DWGTreeNodeData 列表
    /// </summary>
    /// <param name="treeNodes">树节点列表</param>
    /// <returns>DWG 树节点列表</returns>
    public static List<DWGTreeNodeData> ToDWGTreeNodeDataList(this List<TreeNodeData> treeNodes)
    {
        return treeNodes.Adapt<List<DWGTreeNodeData>>();
    }

    /// <summary>
    /// 将 DWGTreeNodeData 转换回 TreeNodeData
    /// </summary>
    /// <param name="dwgNode">DWG 树节点</param>
    /// <returns>树节点</returns>
    public static TreeNodeData ToTreeNodeData(this DWGTreeNodeData dwgNode)
    {
        return dwgNode.Adapt<TreeNodeData>();
    }
}

/// <summary>
/// Mapster 使用示例
/// </summary>
/// <remarks>
/// 📝 基本用法：
/// 
/// // 1. 单个对象转换
/// var dwgNode = treeNode.Adapt<DWGTreeNodeData>();
/// var dwgNode2 = treeNode.ToDWGTreeNodeData(); // 扩展方法
/// 
/// // 2. 列表转换
/// var dwgNodes = treeNodes.Adapt<List<DWGTreeNodeData>>();
/// var dwgNodes2 = treeNodes.ToDWGTreeNodeDataList(); // 扩展方法
/// 
/// // 3. 映射到现有对象
/// treeNode.Adapt(existingDwgNode);
/// 
/// // 4. 反向转换
/// var backToTreeNode = dwgNode.Adapt<TreeNodeData>();
/// var backToTreeNode2 = dwgNode.ToTreeNodeData(); // 扩展方法
/// 
/// 🚀 性能优势：
/// - 比手动复制快 4-12 倍
/// - 内存使用减少 2/3
/// - 支持复杂对象图映射
/// - 编译时生成映射代码
/// </remarks>
public static class MapsterUsageExamples
{
    // 示例代码在注释中，实际使用时参考上面的说明
}

#endif