using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace AlphaPM.Views.DWG;

/// <summary>
/// 反向布尔到可见性转换器
/// </summary>
public class InverseBooleanToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return boolValue ? Visibility.Collapsed : Visibility.Visible;
        return Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
            return visibility != Visibility.Visible;
        return false;
    }
}

/// <summary>
/// 反向布尔转换器
/// </summary>
public class InverseBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return !boolValue;
        return true;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
            return !boolValue;
        return false;
    }
}

/// <summary>
/// 通用类型画刷转换器
/// </summary>
public class GeneralTypeBrushConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isGeneral)
        {
            return isGeneral 
                ? new SolidColorBrush(Color.FromRgb(0, 120, 215)) // 蓝色 - 通用
                : new SolidColorBrush(Color.FromRgb(255, 140, 0)); // 橙色 - 专用
        }
        return new SolidColorBrush(Colors.Gray);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 通用类型文本转换器
/// </summary>
public class GeneralTypeTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isGeneral)
            return isGeneral ? "通用" : "专用";
        return "未知";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 编辑模式标题转换器
/// </summary>
public class EditModeHeaderConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isNewMode)
            return isNewMode ? "新建文件类型" : "编辑文件类型";
        return "编辑文件类型";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 保存按钮文本转换器
/// </summary>
public class SaveButtonTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isNewMode)
            return isNewMode ? "创建" : "保存";
        return "保存";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 状态颜色转换器 - 多值转换器
/// </summary>
public class StatusColorConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length >= 2 && values[0] is bool isEnabled && values[1] is bool isGeneral)
        {
            if (!isEnabled)
                return new SolidColorBrush(Colors.Gray); // 禁用 - 灰色
            
            return isGeneral 
                ? new SolidColorBrush(Color.FromRgb(0, 120, 215)) // 通用 - 蓝色
                : new SolidColorBrush(Color.FromRgb(255, 140, 0)); // 专用 - 橙色
        }
        return new SolidColorBrush(Colors.Gray);
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
