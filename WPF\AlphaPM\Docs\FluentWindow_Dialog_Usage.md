# 🎨 FluentWindow 对话框使用指南

## 📋 概述

我们已经将 `NodeEditView` 从 `UserControl` 升级为 `ui:FluentWindow`，提供了更现代化的对话框体验。

## 🎯 FluentWindow 的优势

### 1. **现代化外观**
- 符合 Fluent Design 设计语言
- 自动适配系统主题（深色/浅色模式）
- 支持亚克力效果和现代化圆角

### 2. **更好的用户体验**
- 原生窗口行为（最小化、最大化、关闭）
- 自动居中显示（`WindowStartupLocation="CenterOwner"`）
- 响应式设计，支持窗口缩放

### 3. **简化的布局**
- 移除了额外的 Border 容器
- 直接使用 Grid 布局，减少嵌套层级
- 更清晰的视觉层次

## 🔧 技术实现

### XAML 结构
```xml
<ui:FluentWindow
    Title="{Binding WindowTitle}"
    Height="500" Width="600"
    MinHeight="400" MinWidth="500"
    WindowStartupLocation="CenterOwner"
    ResizeMode="CanResize"
    ShowInTaskbar="False">
    
    <Grid Margin="24">
        <!-- 内容区域 -->
    </Grid>
</ui:FluentWindow>
```

### Code-Behind
```csharp
public partial class NodeEditView : FluentWindow
{
    public NodeEditView()
    {
        InitializeComponent();
    }
}
```

## 📊 数据传递机制

### 输入数据
```csharp
var parameters = new DialogParameters
{
    { "NodeType", DWGNodeTypes.Folder },
    { "IsEditMode", false }
};
```

### 返回数据
```csharp
var parameters = new DialogParameters
{
    { "NodeName", NodeName },
    { "NodeDescription", NodeDescription },
    { "NodeType", NodeType },
    { "NodeIcon", NodeIcon }
};
RequestClose?.Invoke(new DialogResult(ButtonResult.OK, parameters));
```

## 🎨 视觉特性

### 1. **标题栏**
- 自动显示窗口标题
- 系统原生控制按钮
- 支持拖拽移动

### 2. **内容区域**
- 24px 边距提供舒适的视觉空间
- 响应式布局适配不同屏幕尺寸
- 现代化的输入控件样式

### 3. **按钮区域**
- 主要操作按钮（确认）使用 Primary 样式
- 次要操作按钮（取消）使用 Secondary 样式
- 图标 + 文字的组合设计

## 🚀 使用示例

### 添加年份
```csharp
var parameters = new DialogParameters
{
    { "NodeType", DWGNodeTypes.Folder },
    { "IsEditMode", false }
};

_dialogService.ShowDialog("NodeEditView", parameters, result =>
{
    if (result.Result == ButtonResult.OK)
    {
        var nodeName = result.Parameters.GetValue<string>("NodeName");
        var nodeDescription = result.Parameters.GetValue<string>("NodeDescription");
        var nodeIcon = result.Parameters.GetValue<string>("NodeIcon");
        
        // 创建新节点
        var yearNode = TreeNodeFactory.CreateFolder(GetNextNodeId(), nodeName, nodeDescription);
        yearNode.Emoji = nodeIcon;
        TreeData.Add(yearNode);
    }
});
```

### 编辑现有节点
```csharp
var parameters = new DialogParameters
{
    { "NodeData", SelectedNode },
    { "IsEditMode", true }
};

_dialogService.ShowDialog("NodeEditView", parameters, result =>
{
    if (result.Result == ButtonResult.OK)
    {
        // 更新节点数据
        SelectedNode.Name = result.Parameters.GetValue<string>("NodeName");
        SelectedNode.Description = result.Parameters.GetValue<string>("NodeDescription");
        SelectedNode.Emoji = result.Parameters.GetValue<string>("NodeIcon");
    }
});
```

## 🎯 最佳实践

1. **窗口尺寸**: 设置合理的默认尺寸和最小尺寸
2. **居中显示**: 使用 `WindowStartupLocation="CenterOwner"`
3. **任务栏隐藏**: 设置 `ShowInTaskbar="False"` 避免任务栏混乱
4. **响应式设计**: 支持窗口缩放以适配不同屏幕
5. **数据验证**: 在 ViewModel 中实现完整的输入验证

## 🔄 与传统 ContentDialog 的对比

| 特性 | FluentWindow | ContentDialog |
|------|-------------|---------------|
| 外观 | 现代化窗口 | 模态对话框 |
| 交互 | 可拖拽、缩放 | 固定位置 |
| 集成 | 原生窗口体验 | 嵌入式体验 |
| 灵活性 | 高度可定制 | 相对固定 |
| 性能 | 独立窗口进程 | 共享主窗口 |

FluentWindow 提供了更现代化、更灵活的对话框解决方案，特别适合复杂的数据编辑场景。
