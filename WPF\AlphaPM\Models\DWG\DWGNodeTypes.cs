namespace AlphaPM.Models.DWG;

/// <summary>
/// DWG管理中的节点类型常量
/// </summary>
/// <remarks>
/// 🎯 设计原则：
/// - 只包含常量定义，不包含业务逻辑
/// - 所有业务逻辑方法已迁移到TreeNodeDataExtensions
/// - 保持简洁和单一职责
/// </remarks>
public static class DWGNodeTypes
{
    /// <summary>
    /// 年份类型 - 顶层年份节点的特殊标识
    /// </summary>
    public const string Year = "year";
    
    /// <summary>
    /// 项目类型 - 用于具体栋号节点
    /// </summary>
    public const string Project = "project";



    /// <summary>
    /// 建筑类型 - 具体建筑栋号
    /// </summary>
    public const string Building = "building";
}
