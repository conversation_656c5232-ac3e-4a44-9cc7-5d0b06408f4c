namespace AlphaPM.Models.DWG
{
    /// <summary>
    /// DWG项目图标管理器
    /// 统一管理三层级的图标显示
    /// </summary>
    public static class DWGIconManager
    {
        /// <summary>
        /// 顶层图标 - 年份文件夹
        /// </summary>
        public const string YearIcon = "📅";

        /// <summary>
        /// 二层图标 - 项目
        /// </summary>
        public const string ProjectIcon = "📁";

        /// <summary>
        /// 三层图标 - 栋号/建筑
        /// </summary>
        public const string BuildingIcon = "🏢";

        /// <summary>
        /// 获取WPF-UI SymbolIcon名称 - 年份
        /// </summary>
        public const string YearSymbol = "Folder24";
        
        /// <summary>
        /// 获取WPF-UI SymbolIcon名称 - 项目
        /// </summary>
        public const string ProjectSymbol = "Building24";
        
        /// <summary>
        /// 获取WPF-UI SymbolIcon名称 - 栋号
        /// </summary>
        public const string BuildingSymbol = "Tool24";

        /// <summary>
        /// 根据节点层级获取对应的图标
        /// </summary>
        /// <param name="level">节点层级 (1=年份, 2=项目, 3=栋号)</param>
        /// <returns>对应的图标字符</returns>
        public static string GetIconByLevel(int level)
        {
            return level switch
            {
                1 => YearIcon,      // 年份
                2 => ProjectIcon,   // 项目
                3 => BuildingIcon,  // 栋号
                _ => "❓"           // 未知层级
            };
        }

        /// <summary>
        /// 根据节点层级获取对应的SymbolIcon名称
        /// </summary>
        /// <param name="level">节点层级 (1=年份, 2=项目, 3=栋号)</param>
        /// <returns>对应的SymbolIcon名称</returns>
        public static string GetSymbolByLevel(int level)
        {
            return level switch
            {
                1 => YearSymbol,      // 年份
                2 => ProjectSymbol,   // 项目
                3 => BuildingSymbol,  // 栋号
                _ => "Question24"     // 未知层级
            };
        }

        /// <summary>
        /// 根据节点层级获取显示名称
        /// </summary>
        /// <param name="level">节点层级</param>
        /// <returns>层级显示名称</returns>
        public static string GetLevelDisplayName(int level)
        {
            return level switch
            {
                1 => "年份",
                2 => "项目", 
                3 => "栋号",
                _ => "未知"
            };
        }

        /// <summary>
        /// 根据节点层级获取添加操作的描述
        /// </summary>
        /// <param name="level">要添加的节点层级</param>
        /// <returns>添加操作描述</returns>
        public static string GetAddActionDescription(int level)
        {
            return level switch
            {
                1 => "添加年份",
                2 => "添加项目",
                3 => "添加栋号",
                _ => "添加项目"
            };
        }

        /// <summary>
        /// 根据节点层级获取输入提示文本
        /// </summary>
        /// <param name="level">节点层级</param>
        /// <returns>输入提示文本</returns>
        public static (string NamePrompt, string DescPrompt, string NamePlaceholder, string DescPlaceholder) GetInputPrompts(int level)
        {
            return level switch
            {
                1 => (
                    "请输入年份：",
                    "请输入年份说明：", 
                    "例如：2026",
                    "例如：2026年度项目规划"
                ),
                2 => (
                    "请输入项目名称：",
                    "请输入项目说明：",
                    "例如：万科城市花园",
                    "例如：高端住宅项目，总建筑面积50万平方米"
                ),
                3 => (
                    "请输入栋号名称：",
                    "请输入栋号说明：",
                    "例如：1#楼、A栋、商业楼",
                    "例如：高层住宅，32层，户型90-150平方米"
                ),
                _ => ("请输入名称：", "请输入说明：", "", "")
            };
        }

        /// <summary>
        /// 根据节点层级获取默认名称生成规则
        /// </summary>
        /// <param name="level">节点层级</param>
        /// <param name="existingCount">已存在的同级节点数量</param>
        /// <returns>建议的默认名称</returns>
        public static string GetDefaultName(int level, int existingCount)
        {
            return level switch
            {
                1 => (DateTime.Now.Year + 1).ToString(), // 年份：下一年
                2 => $"新项目{DateTime.Now:MMdd}",        // 项目：新项目+日期
                3 => $"{existingCount + 1}#楼",           // 栋号：序号+楼
                _ => $"新项目{existingCount + 1}"
            };
        }

        /// <summary>
        /// 根据节点层级获取默认说明
        /// </summary>
        /// <param name="level">节点层级</param>
        /// <param name="name">节点名称</param>
        /// <returns>建议的默认说明</returns>
        public static string GetDefaultDescription(int level, string name)
        {
            return level switch
            {
                1 => $"{name}年度项目规划",
                2 => "住宅项目",
                3 => "住宅建筑",
                _ => "项目说明"
            };
        }

        /// <summary>
        /// 获取添加操作的标题（带图标）
        /// </summary>
        /// <param name="level">要添加的节点层级</param>
        /// <param name="parentName">父节点名称（可选）</param>
        /// <returns>带图标的操作标题</returns>
        public static string GetAddTitle(int level, string? parentName = null)
        {
            return level switch
            {
                1 => $"{YearIcon} 添加年份",
                2 => string.IsNullOrEmpty(parentName)
                    ? $"{ProjectIcon} 添加项目"
                    : $"{ProjectIcon} 为年份 '{parentName}' 添加项目",
                3 => string.IsNullOrEmpty(parentName)
                    ? $"{BuildingIcon} 添加栋号"
                    : $"{BuildingIcon} 为项目 '{parentName}' 添加栋号",
                _ => "添加项目"
            };
        }

        /// <summary>
        /// 获取编辑操作的标题（带图标）
        /// </summary>
        /// <param name="level">节点层级</param>
        /// <returns>带图标的编辑标题</returns>
        public static string GetEditTitle(int level)
        {
            return level switch
            {
                1 => "✏️ 编辑年份",
                2 => "✏️ 编辑项目",
                3 => "✏️ 编辑栋号",
                _ => "✏️ 编辑节点"
            };
        }
    }
}
