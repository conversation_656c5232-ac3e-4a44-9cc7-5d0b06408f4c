﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using AlphaPM.Data;
using AlphaPM.Extensions;
using AlphaPM.Hellper;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using Prism.Services.Dialogs;
using Zylo.WPF.Controls;
using Zylo.YLog.Runtime;
using AlphaPM.Models;
using AlphaPM.Models.DWG;
using AlphaPM.Services.Base;
using AlphaPM.Services.DWG;
using AlphaPM.Views.DWG;
using Wpf.Ui.Controls;
using Zylo.WPF.Controls.TreeView;
using MessageBox = System.Windows.MessageBox;
using MessageBoxButton = System.Windows.MessageBoxButton;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// DWG管理视图模型 - 三列布局设计
/// 左侧：TreeView 专业分类
/// 中间：ListBox 文件类型
/// 右侧：DataView 文件详情
/// </summary>
public partial class DWGManageViewModel : ObservableValidator, INavigationAware
{
    // 🔥 使用最详细的日志级别 - 输出所有信息
    // private readonly YLoggerInstance _logger = YLogger.ForWarning<DWGManageViewModel>();
    private readonly YLoggerInstance _logger = YLogger.ForDebug<DWGManageViewModel>();
    private readonly IDialogService _dialogService;
    private readonly TreeNodeDataService _treeNodeDataService;
    private readonly DWGNodeDataService _dwgNodeDataService;

    #region 构造函数

    public DWGManageViewModel(IDialogService dialogService, TreeNodeDataService treeNodeDataService, DWGNodeDataService dwgNodeDataService)
    {
        _dialogService = dialogService;
        _treeNodeDataService = treeNodeDataService;
        _dwgNodeDataService = dwgNodeDataService;

        // 初始化拖拽处理器
        DragDropHandler = new TreeViewDragDropHandler(this, _treeNodeDataService);

        // 初始化项目路径（配置已在 AppBootstrapper 中加载）
        ProjectPath = AlphaPM.Data.YDataModel.Current.YdwgData.ProjectPath ?? "未设置项目路径";
    }

    #endregion

    #region 数据属性

    /// <summary>
    /// 项目路径
    /// </summary>
    [ObservableProperty]
    [Description("当前项目路径")]
    public partial string ProjectPath { get; set; } = "";

    /// <summary>
    /// 树形数据源
    /// </summary>
    [ObservableProperty]
    [Description("TreeView-树形数据源")]
    public partial ObservableCollection<TreeNodeData> TreeData { get; set; } = new();

    /// <summary>
    /// 过滤后的树形数据（用于搜索）
    /// </summary>
    [ObservableProperty]
    [Description("TreeView-过滤后的树形数据（用于搜索）")]
    public partial ObservableCollection<TreeNodeData> FilteredTreeData { get; set; } = new();

    /// <summary>
    /// 选中的节点
    /// </summary>
    [ObservableProperty]
    [Description("TreeView-选中的节点")]
    // 当选中节点变化时，自动通知相关计算属性
    [NotifyPropertyChangedFor(nameof(HasSelectedNode))] // 是否有选中节点
    [NotifyPropertyChangedFor(nameof(CanEditSelectedNode))] // 是否可编辑选中节点
    [NotifyPropertyChangedFor(nameof(CanDeleteSelectedNode))] // 是否可删除选中节点
    [NotifyPropertyChangedFor(nameof(HasSelectedBuildingNode))] // 是否选中了栋号级别的节点
    // 当选中节点变化时，自动通知相关命令的 CanExecute 状态（简单依赖）
    [NotifyCanExecuteChangedFor(nameof(AddChildNodeCommand))] // 添加子节点命令（需要有选中节点）
    [NotifyCanExecuteChangedFor(nameof(StartEditNodeCommand))] // 编辑节点命令（需要有选中节点）
    [NotifyCanExecuteChangedFor(nameof(DeleteNodeCommand))] // 删除节点命令（需要有选中节点）
    [NotifyCanExecuteChangedFor(nameof(ManageFileTypesCommand))] // 管理文件类型命令（需要选中栋号级别节点）
    // 注意：AddProjectCommand 和 AddBuildingCommand 有复杂的 CanExecute 逻辑，需要手动通知
    public partial TreeNodeData? SelectedNode { get; set; }

    /// <summary>
    /// 搜索关键词
    /// </summary>
    [ObservableProperty]
    [Description("搜索关键词")]
    public partial string SearchKeyword { get; set; } = string.Empty;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    [Description("状态消息")]
    public partial string StatusMessage { get; set; } = "就绪";

    /// <summary>
    /// 文件总数
    /// </summary>
    [ObservableProperty]
    [Description("文件总数")]
    public partial int TotalFiles { get; set; }

    /// <summary>
    /// 已选择文件数
    /// </summary>
    [ObservableProperty]
    [Description("已选择文件数")]
    public partial int SelectedFiles { get; set; }

    /// <summary>
    /// 右键菜单项集合 - 根据选中节点动态更新
    /// </summary>
    [ObservableProperty]
    [Description("右键菜单项集合")]
    public partial ObservableCollection<ContextMenuItemModel> ContextMenuItems { get; set; } = new();

    /// <summary>
    /// 拖拽处理器 - MVVM模式
    /// </summary>
    public TreeViewDragDropHandler DragDropHandler { get; private set; } = null!;

    #endregion

    #region 基础命令

    /// <summary>
    /// 选择项目路径命令
    /// </summary>
    [RelayCommand]
    private async Task SelectProjectPathAsync()
    {
        try
        {
            StatusMessage = "正在选择项目路径...";

            var result = YSelectWindow.Current.SelectFolder("选择项目路径", YDataModel.Current.YdwgData.ProjectPath);
            if (result.Success)
            {
                YDataModel.Current.YdwgData.ProjectPath = result.SelectedPath;
                // 更新显示的项目路径
                ProjectPath = YDataModel.Current.YdwgData.ProjectPath;

                // 保存配置
                await YDataModel.Current.Save();

                YServiceBase.ConnectionData();
                LoadData();


                StatusMessage = $"项目路径已设置: {ProjectPath}";
                _logger.Info($"✅ 项目路径已设置: {ProjectPath}");
            }
            else
            {
                StatusMessage = "用户取消选择项目路径";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"选择项目路径失败: {ex.Message}";
            _logger.Error($"❌ 选择项目路径失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 刷新命令
    /// </summary>
    [RelayCommand]
    private async Task RefreshAsync()
    {
        await Task.Run(() => LoadData());
        StatusMessage = "数据已刷新";
    }

    /// <summary>
    /// 新建文件命令
    /// </summary>
    [RelayCommand]
    private void CreateNew()
    {
        StatusMessage = "创建新文件功能待实现";
    }

    /// <summary>
    /// 打开文件命令
    /// </summary>
    [RelayCommand]
    private void OpenFile()
    {
        StatusMessage = "打开文件功能待实现";
    }

    /// <summary>
    /// 删除文件命令
    /// </summary>
    [RelayCommand]
    private void DeleteFile()
    {
        StatusMessage = "删除文件功能待实现";
    }

    #endregion

    #region 节点添加命令

    /// <summary>
    /// 添加年份命令 - 顶层节点
    /// </summary>
    [RelayCommand]
    private async Task AddYear()
    {
        try
        {
            // 调用DWGNodeDataService的完整业务逻辑
            var savedNode = await _dwgNodeDataService.AddYearAsync(TreeData, _dialogService);

            if (savedNode != null)
            {
                // 更新UI
                TreeData.Add(savedNode);
                StatusMessage = $"已添加年份: {savedNode.Name}";
                _logger.Info($"添加年份成功: {savedNode.Name}");
            }
            else
            {
                StatusMessage = "取消添加年份";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"添加年份失败: {ex.Message}";
            _logger.Error($"添加年份失败: {ex}");
        }
    }

    /// <summary>
    /// 添加项目命令 - 二层节点（需要选中年份节点）
    /// </summary>
    [Description("添加项目命令（需要选中年份节点）")]
    [RelayCommand(CanExecute = nameof(CanAddProject))]
    private void AddProject()
    {
        if (SelectedNode == null) return;

        AddProjectAsync(SelectedNode);
    }

    /// <summary>
    /// 判断是否可以添加项目（选中的是年份节点）
    /// </summary>
    private bool CanAddProject()
    {
        return SelectedNode != null && TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode) == 1;
    }

    /// <summary>
    /// 添加栋号命令 - 三层节点（需要选中项目节点）
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanAddBuilding))]
    private async Task AddBuilding()
    {
        if (SelectedNode == null) return;

        await AddBuildingAsync(SelectedNode);
    }

    /// <summary>
    /// 判断是否可以添加栋号（选中的是项目节点）
    /// </summary>
    private bool CanAddBuilding()
    {
        return SelectedNode != null && TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode) == 2;
    }

    #endregion

    #region 节点编辑和删除命令

    /// <summary>
    /// 是否有选中的节点
    /// </summary>
    public bool HasSelectedNode => SelectedNode != null;

    /// <summary>
    /// 是否可以编辑选中节点
    /// </summary>
    public bool CanEditSelectedNode => HasSelectedNode;

    /// <summary>
    /// 是否可以删除选中节点
    /// </summary>
    public bool CanDeleteSelectedNode => HasSelectedNode;

    /// <summary>
    /// 是否选中了栋号级别的节点（第三层）
    /// </summary>
    public bool HasSelectedBuildingNode => SelectedNode != null && TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode) == 3;

    /// <summary>
    /// 节点选择变化命令
    /// </summary>
    [RelayCommand]
    private void NodeSelectionChanged(object selectedNode)
    {
        _logger.Debug($"🎯 节点选择变化: {selectedNode?.GetType().Name}");

        if (selectedNode is TreeNodeData node)
        {
            _logger.Info($"📍 选中节点: {node.Name} (ID: {node.Id})");
            StatusMessage = $"已选中: {node.Name}";
        }
        else
        {
            _logger.Info("📍 取消选择");
            StatusMessage = "准备就绪";
        }
    }

    /// <summary>
    /// 节点展开命令
    /// </summary>
    [RelayCommand]
    private void NodeExpanded(object expandedNode)
    {
        if (expandedNode is TreeNodeData node)
        {
            _logger.Debug($"📂 节点展开: {node.Name}");
            node.IsExpanded = true;
        }
    }

    /// <summary>
    /// 节点折叠命令
    /// </summary>
    [RelayCommand]
    private void NodeCollapsed(object collapsedNode)
    {
        if (collapsedNode is TreeNodeData node)
        {
            _logger.Debug($"📁 节点折叠: {node.Name}");
            node.IsExpanded = false;
        }
    }

    /// <summary>
    /// 添加子节点命令 - 根据父节点类型添加不同的子节点
    /// </summary>
    [RelayCommand(CanExecute = nameof(HasSelectedNode))]
    private async Task AddChildNode()
    {
        if (SelectedNode != null)
        {
            var nodeLevel = TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode);

            switch (nodeLevel)
            {
                case 1: // 年份节点，添加项目
                    await AddProjectAsync(SelectedNode);
                    break;
                case 2: // 项目节点，添加栋号
                    await AddBuildingAsync(SelectedNode);
                    break;
                default:
                    StatusMessage = $"无法为 {SelectedNode.Name} 添加子节点";
                    break;
            }
        }
    }

    /// <summary>
    /// 开始编辑节点命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanEditSelectedNode))]
    private async Task StartEditNode()
    {
        if (SelectedNode == null) return;

        var nodeLevel = TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode);
        var editTitle = DWGIconManager.GetEditTitle(nodeLevel);

        var parameters = new DialogParameters
        {
            { "Title", editTitle },
            { "NodeData", SelectedNode },
            { "IsEditMode", true }
        };

        _dialogService.ShowDialog("NodeEditView", parameters, async result =>
        {
            if (result.Result == ButtonResult.OK)
            {
                var nodeName = result.Parameters.GetValue<string>("NodeName");
                var nodeDescription = result.Parameters.GetValue<string>("NodeDescription");

                _logger.Info($"📝 编辑节点 - 名称: '{nodeName}', 描述: '{nodeDescription}'");

                try
                {
                    // 使用 DWGNodeDataService 更新节点（包含同级重名检测）
                    var success = await _dwgNodeDataService.UpdateNodeAsync(SelectedNode, nodeName, nodeDescription);
                    if (success)
                    {
                        StatusMessage = $"已更新{TreeNodeDataExtensions.GetDisplayName(SelectedNode.NodeType)}: {nodeName}";
                        _logger.Info($"✅ 编辑节点成功: {nodeName}，数据库已更新");
                    }
                    else
                    {
                        StatusMessage = "更新失败：数据库保存失败";
                        _logger.Error("❌ 编辑节点失败：数据库保存失败");
                    }
                }
                catch (Exception ex)
                {
                    StatusMessage = $"更新失败: {ex.Message}";
                    _logger.Error($"❌ 编辑节点失败: {ex}");
                }
            }
            else
            {
                StatusMessage = "取消编辑操作";
            }
        });
    }

    /// <summary>
    /// 删除节点命令
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDeleteSelectedNode))]
    private async Task DeleteNode()
    {
        if (SelectedNode != null)
        {
            try
            {
                var nodeLevel = TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode);
                var nodeTypeName = TreeNodeDataExtensions.GetDisplayName(SelectedNode.NodeType);
                var nodeName = SelectedNode.Name;
                var childCount = GetTotalChildCount(SelectedNode);

                // 显示确认对话框
                var confirmed = await YContentDialog.ShowDeleteConfirmDialogAsync(nodeName, nodeTypeName, childCount);

                if (!confirmed)
                {
                    StatusMessage = "取消删除操作";
                    return;
                }

                // 使用 DWGNodeDataService 删除节点
                await _dwgNodeDataService.DeleteNodeAsync(SelectedNode);

                // 从父节点或根集合中移除
                if (SelectedNode.YParent != null)
                {
                    SelectedNode.Parent.Children.Remove(SelectedNode);
                }
                else
                {
                    TreeData.Remove(SelectedNode);
                    // FilteredTreeData 会通过搜索过滤自动更新，不需要手动移除
                }

                // 清空选中状态
                SelectedNode = null;

                // 🔄 更新右键菜单（重要！删除后菜单状态需要更新）
                UpdateContextMenuItems();

                var message = childCount > 0
                    ? $"已删除{nodeTypeName}: {nodeName}（包含 {childCount} 个子项目）"
                    : $"已删除{nodeTypeName}: {nodeName}";

                StatusMessage = message;

                UpdateContextMenuItems();
            }
            catch (Exception ex)
            {
                StatusMessage = $"删除失败: {ex.Message}";
            }
        }
    }

    #endregion

    #region 右键菜单管理

    /// <summary>
    /// 更新右键菜单项集合
    /// </summary>
    private void UpdateContextMenuItems()
    {
        _logger.Debug("🔄 开始更新右键菜单项集合");
        var menuItems = GetContextMenuItems();
        _logger.Debug($"📋 获取到 {menuItems.Count} 个菜单项");

        // 直接替换整个集合，确保绑定更新
        var newCollection = new ObservableCollection<ContextMenuItemModel>();
        foreach (var item in menuItems)
        {
            newCollection.Add(item);
            _logger.Debug($"➕ 添加菜单项: {item.Header}");
        }

        ContextMenuItems = newCollection;
        _logger.Info($"✅ 右键菜单更新完成，共 {ContextMenuItems.Count} 项");
    }

    /// <summary>
    /// 获取当前选中节点的右键菜单项
    /// </summary>
    /// <returns>菜单项列表</returns>
    private List<ContextMenuItemModel> GetContextMenuItems()
    {
        var menuItems = new List<ContextMenuItemModel>();

        if (SelectedNode == null)
        {
            // 没有选中节点，只能添加顶层年份
            _logger.Debug("🚫 无选中节点，添加年份菜单");
            menuItems.Add(new ContextMenuItemModel("📅 添加年份", AddYearCommand));
        }
        else
        {
            var nodeLevel = TreeNodeDataExtensions.GetDWGNodeLevel(SelectedNode);
            _logger.Debug($"🎯 选中节点 '{SelectedNode.Name}', 级别 {nodeLevel}");

            switch (nodeLevel)
            {
                case 1: // 顶层年份节点
                    // 可以添加同级年份，或添加子级项目
                    _logger.Debug("📅 构建年份节点菜单");
                    // menuItems.Add(new ContextMenuItemModel("📅 添加年份", AddYearCommand));
                    menuItems.Add(new ContextMenuItemModel("🏢 添加项目", AddProjectCommand));
                    menuItems.Add(new ContextMenuItemModel("", null)); // 分隔符
                    menuItems.Add(new ContextMenuItemModel("✏️ 编辑年份", StartEditNodeCommand, CanEditSelectedNode));
                    menuItems.Add(new ContextMenuItemModel("🗑️ 删除年份", DeleteNodeCommand, CanDeleteSelectedNode));
                    _logger.Debug($"📅 年份节点菜单项数量: {menuItems.Count}");
                    break;

                case 2: // 二层项目节点
                    // 可以添加同级项目，或添加子级栋号
                    menuItems.Add(new ContextMenuItemModel("🏢 添加项目", AddProjectCommand));
                    menuItems.Add(new ContextMenuItemModel("🏗️ 添加栋号", AddBuildingCommand));
                    menuItems.Add(new ContextMenuItemModel("", null)); // 分隔符
                    menuItems.Add(new ContextMenuItemModel("✏️ 编辑项目", StartEditNodeCommand, CanEditSelectedNode));
                    menuItems.Add(new ContextMenuItemModel("🗑️ 删除项目", DeleteNodeCommand, CanDeleteSelectedNode));
                    break;

                case 3: // 三层栋号节点
                    // 可以添加同级栋号
                    menuItems.Add(new ContextMenuItemModel("🏗️ 添加栋号", AddBuildingCommand));
                    menuItems.Add(new ContextMenuItemModel("", null)); // 分隔符
                    menuItems.Add(new ContextMenuItemModel("✏️ 编辑栋号", StartEditNodeCommand, CanEditSelectedNode));
                    menuItems.Add(new ContextMenuItemModel("🗑️ 删除栋号", DeleteNodeCommand, CanDeleteSelectedNode));
                    break;
            }
        }

        // 通用功能
        menuItems.Add(new ContextMenuItemModel("", null)); // 分隔符
        menuItems.Add(new ContextMenuItemModel("🔄 刷新数据", RefreshCommand));

        return menuItems;
    }

    #endregion

    #region 拖拽后UI更新方法

    /// <summary>
    /// 拖拽操作完成后更新TreeView的UI结构
    /// 注意：此方法必须在Service层更新数据库之前调用，以获取原始的ParentId
    /// </summary>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <param name="originalParentId">原父节点ID（在Service层更新之前保存）</param>
    public void UpdateTreeStructureAfterDragDrop(TreeNodeData sourceNode, TreeNodeData targetNode,
        int? originalParentId = null)
    {
        try
        {
            // 根据Level关系判断操作类型
            if (sourceNode.Level > targetNode.Level)
            {
                // 父子关系改变：需要更新Children集合
                UpdateParentChildRelationInUI(sourceNode, targetNode, originalParentId);
            }
            else if (sourceNode.Level == targetNode.Level)
            {
                // 同级排序：Children集合不变，只是SortOrder改变
                // ObservableCollection会自动处理排序显示
                _logger.Debug($"同级排序完成: {sourceNode.Name} ↔ {targetNode.Name}");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"更新UI结构失败: {ex}");
        }
    }

    /// <summary>
    /// 更新父子关系在UI中的显示
    /// </summary>
    /// <param name="sourceNode">源节点（子节点）</param>
    /// <param name="newParent">新父节点</param>
    /// <param name="originalParentId">原父节点ID</param>
    private void UpdateParentChildRelationInUI(TreeNodeData sourceNode, TreeNodeData newParent, int? originalParentId)
    {
        // 1. 从原父节点的Children中移除
        var originalParent = FindNodeInTreeData(originalParentId);
        if (originalParent != null && originalParent.Children.Contains(sourceNode))
        {
            originalParent.Children.Remove(sourceNode);
            _logger.Debug($"从原父节点移除: {originalParent.Name} → {sourceNode.Name}");
        }
        else if (originalParentId.HasValue)
        {
            _logger.Warning($"未找到原父节点或节点不在原父节点的Children中: ParentId={originalParentId}, SourceNode={sourceNode.Name}");
        }

        // 2. 添加到新父节点的Children中
        if (!newParent.Children.Contains(sourceNode))
        {
            newParent.Children.Add(sourceNode);
            _logger.Debug($"添加到新父节点: {newParent.Name} ← {sourceNode.Name}");
        }
        else
        {
            _logger.Debug($"节点已存在于新父节点中: {newParent.Name} ← {sourceNode.Name}");
        }
    }

    /// <summary>
    /// 在TreeData中查找指定ID的节点
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>找到的节点，如果没找到返回null</returns>
    private TreeNodeData? FindNodeInTreeData(int? nodeId)
    {
        if (!nodeId.HasValue) return null;

        // 递归搜索TreeData中的所有节点
        return FindNodeRecursive(TreeData, nodeId.Value);
    }

    /// <summary>
    /// 递归查找节点
    /// </summary>
    /// <param name="nodes">节点集合</param>
    /// <param name="nodeId">要查找的节点ID</param>
    /// <returns>找到的节点</returns>
    private TreeNodeData? FindNodeRecursive(IEnumerable<TreeNodeData> nodes, int nodeId)
    {
        foreach (var node in nodes)
        {
            if (node.Id == nodeId)
                return node;

            var found = FindNodeRecursive(node.Children, nodeId);
            if (found != null)
                return found;
        }

        return null;
    }

    #endregion

    #region 数据初始化方法

    /// <summary>
    /// 加载数据 - 从数据库加载三级层次结构：年份 -> 项目名 -> 栋号
    /// </summary>
    private async void LoadData()
    {
        await LoadDataAsync();
    }

    /// <summary>
    /// 加载数据 - 公共异步方法，供外部调用
    /// </summary>
    public async Task LoadDataAsync()
    {
        try
        {
            _logger.Debug("开始加载TreeView数据");

            // 清空现有数据
            TreeData.Clear();
            FilteredTreeData.Clear();

            // 初始化数据库（传递DWGNodeDataService用于创建初始数据）
            await _treeNodeDataService.InitializeAsync(_dwgNodeDataService);

            // 从数据库加载树形数据
            var treeData = await _treeNodeDataService.GetTreeDataAsync();

            // 添加到UI集合
            foreach (var node in treeData)
            {
                TreeData.Add(node);
            }

            _logger.Info($"TreeView数据加载完成，根节点数量: {TreeData.Count}");
        }
        catch (Exception ex)
        {
            _logger.Error($"加载TreeView数据失败: {ex.Message}");
            StatusMessage = $"数据加载失败: {ex.Message}";
        }
    }



    /// <summary>
    /// 加载年份数据
    /// </summary>
    private void LoadYearData(TreeNodeData yearNode)
    {
        // 处理年份选择逻辑
        // 可以在这里加载该年份的统计信息、项目概览等
    }

    /// <summary>
    /// 加载项目数据
    /// </summary>
    private void LoadProjectData(TreeNodeData projectNode)
    {
        // 处理项目选择逻辑
        // 可以在这里加载项目的基本信息、进度状态等
    }

    /// <summary>
    /// 加载栋号数据
    /// </summary>
    private void LoadBuildingData(TreeNodeData buildingNode)
    {
        // 处理栋号选择逻辑
        // 这里是最重要的，选中具体栋号时加载对应的DWG文件、图纸分类等
        var projectName = buildingNode.YParent?.Name ?? "未知项目";
        var yearName = buildingNode.YParent?.YParent?.Name ?? "未知年份";

        // 可以在这里触发加载该栋号的：
        // 1. DWG文件列表
        // 2. 图纸分类（平面图、立面图、剖面图等）
        // 3. 文件状态和版本信息
        // 4. 相关的项目文档

        StatusMessage = $"已选择: {yearName}年 {projectName} {buildingNode.Name}";
    }

    #endregion

    #region 节点操作方法

    /// <summary>
    /// 添加项目到指定年份
    /// </summary>
    private async Task AddProjectAsync(TreeNodeData yearNode)
    {
        try
        {
            // 调用DWGNodeDataService的完整业务逻辑
            var savedNode = await _dwgNodeDataService.AddProjectAsync(yearNode, _dialogService);

            if (savedNode != null)
            {
                // 更新UI
                savedNode.YParent = yearNode;
                yearNode.Children.Add(savedNode);
                StatusMessage = $"已为 {yearNode.Name} 添加项目: {savedNode.Name}";
                _logger.Info($"添加项目成功: {yearNode.Name} -> {savedNode.Name}");
            }
            else
            {
                StatusMessage = "取消添加项目";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"添加项目失败: {ex.Message}";
            _logger.Error($"添加项目失败: {ex}");
        }
    }

    /// <summary>
    /// 添加栋号到指定项目
    /// </summary>
    private async Task AddBuildingAsync(TreeNodeData projectNode)
    {
        // //选中数据
        // var selectedNodedata = SelectedNode;
        //
        // _logger.Info($"名称: '{selectedNodedata.Name}'");
        // try
        // {
        //     var parameters = new DialogParameters
        //     {
        //         { "Title", DWGIconManager.GetAddTitle(3, selectedNodedata.Name) },
        //         { "NodeType", DWGNodeTypes.Building },
        //         { "IsEditMode", false }
        //     };
        //
        //     _dialogService.ShowDialog("NodeEditView", parameters, async result =>
        //     {
        //         if (result.Result == ButtonResult.OK)
        //         {
        //             var buildingName = result.Parameters.GetValue<string>("NodeName");
        //             var buildingDesc = result.Parameters.GetValue<string>("NodeDescription");
        //
        //             // 获取用户输入的DWG业务数据
        //             var manager = result.Parameters.GetValue<string>("Manager") ?? "";
        //             var designer = result.Parameters.GetValue<string>("Designer") ?? "";
        //             var buildingType = result.Parameters.GetValue<string>("BuildingType") ?? "";
        //             var buildingArea = result.Parameters.GetValue<decimal>("BuildingArea");
        //             var floorCount = result.Parameters.GetValue<int>("FloorCount");
        //             var buildingStatus = result.Parameters.GetValue<string>("BuildingStatus") ?? "规划中";
        //
        //
        //             // 检查同级重复
        //             var existingBuilding = await _treeNodeDataService.GetByNameAsync(buildingName, selectedNodedata.Id);
        //             if (existingBuilding != null)
        //             {
        //                 StatusMessage = $"栋号 '{buildingName}' 在项目 '{selectedNodedata.Name}' 下已存在";
        //                 return;
        //             }
        //
        //             // 使用TreeNodeDataExtensions创建栋号节点（包含正确的DWG业务数据）
        //             var buildingNode = TreeNodeDataExtensions.CreateDWGBuildingTemplate(
        //                 buildingName, selectedNodedata.Id, "", 0, 0, "规划中", "", "");
        //
        //             // 设置描述和数据库相关属性
        //             buildingNode.Description = buildingDesc;
        //             buildingNode.Id = 0; // 新节点ID为0
        //             buildingNode.SortOrder = selectedNodedata.Children.Count + 1;
        //
        //             // 保存到数据库
        //             var savedNode = await _treeNodeDataService.AddAsync(buildingNode);
        //             if (savedNode != null)
        //             {
        //                 savedNode.Parent = selectedNodedata;
        //
        //                 // 添加到UI
        //                 Application.Current.Dispatcher.Invoke(() => { projectNode.Children.Add(savedNode); });
        //
        //                 StatusMessage = $"已为 {selectedNodedata.Name} 添加栋号: {buildingName}";
        //                 _logger.Info($"✅ 添加栋号成功: {selectedNodedata.Name} -> {buildingName}，数据库ID: {savedNode.Id}");
        //             }
        //             else
        //             {
        //                 StatusMessage = "添加栋号失败：数据库保存失败";
        //                 _logger.Error("❌ 添加栋号失败：数据库保存失败");
        //             }
        //         }
        //         else
        //         {
        //             StatusMessage = "取消添加栋号";
        //         }
        //     });
        // }
        // catch (Exception ex)
        // {
        //     StatusMessage = $"添加栋号失败: {ex.Message}";
        //     _logger.Error($"❌ 添加栋号失败: {ex}");
        // }
    }

    #endregion

    #region 工具方法

    /// <summary>
    /// 获取下一个可用的节点ID
    /// </summary>
    private int GetNextNodeId()
    {
        var maxId = 0;

        foreach (var yearNode in TreeData)
        {
            if (yearNode.Id > maxId) maxId = yearNode.Id;

            foreach (var projectNode in yearNode.Children)
            {
                if (projectNode.Id > maxId) maxId = projectNode.Id;

                foreach (var buildingNode in projectNode.Children)
                {
                    if (buildingNode.Id > maxId) maxId = buildingNode.Id;
                }
            }
        }

        return maxId + 1;
    }

    /// <summary>
    /// 获取节点的总子项目数量（递归计算）
    /// </summary>
    private int GetTotalChildCount(TreeNodeData node)
    {
        var count = node.Children.Count;

        foreach (var child in node.Children)
        {
            count += GetTotalChildCount(child);
        }

        return count;
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 搜索关键词变化时的处理
    /// </summary>
    partial void OnSearchKeywordChanged(string value)
    {
        // 实现搜索过滤逻辑
        StatusMessage = string.IsNullOrEmpty(value) ? "就绪" : $"搜索: {value}";
    }


    /// <summary>
    /// 选中节点变化时的处理 - 根据节点类型进行不同处理
    /// </summary>
    partial void OnSelectedNodeChanged(TreeNodeData? value)
    {
        if (value != null)
        {
            _logger.Info($"现在HasSelectedNode 情况：{HasSelectedNode}");
            var nodeLevel = TreeNodeDataExtensions.GetDWGNodeLevel(value);
            

            // 从 ViewModel 获取树数据（根节点集合）
            var selectedPaths = TreeNodeDataExtensions.BuildDWGNodePath(SelectedNode);


            switch (nodeLevel)
            {
                case 1:
                    // 顶层年份节点
                    StatusMessage = $"选择年份: {value.Name} ({value.Children.Count}个项目)";
                    LoadYearData(value);
                    break;

                case 2:
                    // 二级项目节点
                    StatusMessage = $"选择项目: {value.Name} ({value.Children.Count}栋建筑)";
                    LoadProjectData(value);
                    break;

                case 3:
                    // 三级栋号节点
                    StatusMessage = $"选择栋号: {value.Name} - {value.Description}";
                    LoadBuildingData(value);
                    break;

                default:
                    StatusMessage = $"选择: {value.Name}";
                    break;
            }
        }
        else
        {
            StatusMessage = "就绪";
        }

    

        // 大部分命令状态变化已通过 [NotifyCanExecuteChangedFor] 自动通知
        // 但 AddProject 和 AddBuilding 有复杂逻辑，需要手动通知
        AddProjectCommand.NotifyCanExecuteChanged();
        AddBuildingCommand.NotifyCanExecuteChanged();

        // 更新右键菜单
        UpdateContextMenuItems();
    }

    #endregion


    #region 辅助

    /// <summary>
    /// 计算所有节点数量
    /// </summary>
    private int CountAllNodes(ObservableCollection<TreeNodeData> nodes)
    {
        int count = nodes.Count;
        foreach (var node in nodes)
        {
            if (node.Children != null)
            {
                count += CountAllNodes(node.Children);
            }
        }

        return count;
    }

    /// <summary>
    /// 更新节点到数据库 - 供拖拽处理器调用
    /// </summary>
    public async Task UpdateNodeInDatabaseAsync(TreeNodeData node)
    {
        try
        {
            node.ModifiedAt = DateTime.Now;
            var updatedNode = await _treeNodeDataService.UpdateAsync(node);

            if (updatedNode != null)
            {
                _logger.Info($"✅ 节点数据库更新成功: {node.Name}");
                UpdateContextMenuItems();
            }
            else
            {
                _logger.Error($"❌ 节点数据库更新失败: {node.Name}");
                throw new Exception("数据库更新失败");
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 更新节点到数据库时发生错误: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 导航

    /// <summary>
    /// 导航到此视图时调用
    /// </summary>
    /// <param name="navigationContext">导航上下文，包含导航参数</param>
    public void OnNavigatedTo(NavigationContext navigationContext)
    {
        LoadData();
        UpdateContextMenuItems(); // 初始化右键菜单
    }

    /// <summary>
    /// 判断此视图是否可以作为导航目标
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    /// <returns>如果可以重用此视图实例则返回true，否则返回false</returns>
    public bool IsNavigationTarget(NavigationContext navigationContext)
    {
        return true;
    }

    /// <summary>
    /// 从此视图导航离开时调用
    /// </summary>
    /// <param name="navigationContext">导航上下文</param>
    public void OnNavigatedFrom(NavigationContext navigationContext)
    {
    }

    #endregion
    
    

}

/// <summary>
/// 文件项模型
/// </summary>
public partial class FileItemModel : ObservableObject
{
    [ObservableProperty] public partial string FileName { get; set; } = string.Empty;

    [ObservableProperty] public partial string FileSize { get; set; } = string.Empty;

    [ObservableProperty] public partial DateTime ModifiedTime { get; set; }

    [ObservableProperty] public partial string FilePath { get; set; } = string.Empty;

    [ObservableProperty] public partial bool IsSelected { get; set; }
}