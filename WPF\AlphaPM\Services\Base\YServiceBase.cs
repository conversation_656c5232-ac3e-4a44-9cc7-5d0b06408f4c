﻿using System;
using System.IO;
using AlphaPM.Data;
using Zylo.YData;
using Zylo.YIO.Core;
using Zylo.YLog.Runtime;

namespace AlphaPM.Services.Base;

public  class YServiceBase
{
    // 使用静态方法获取日志实例，避免静态字段问题
    private static YLoggerInstance GetLogger() => YLogger.ForWarning<object>();
    /// <summary>
    /// 项目的数据库名
    /// </summary>
    public static string SqliteProjectName = "AlphaPM";

    /// <summary>
    /// 数据库目录
    /// </summary>
    private static string _appDirectory = string.Empty;

    
    /// <summary>
    /// 连接数据库-路经没有改时，不重设置字符串
    /// </summary>
    public static void ConnectionData()
    {
        try
        {
            // 确定数据库存储路径
            var appDirectory = YDataModel.Current.YdwgData.ProjectPath; // 软件运行目录

            if (_appDirectory != appDirectory)
            {
                
                var dbDirectory =appDirectory;
                if (!YDirectory.DirectoryExists(dbDirectory))
                {
                    YDirectory.CreateDirectory(dbDirectory);
                }

                var dbPath = Path.Combine(dbDirectory, $"{YServiceBase.SqliteProjectName}.db");

                GetLogger().Info($"🗄️ 配置 SQLite 数据库路径: {dbPath}");

                // 配置 SQLite 连接字符串
                var connectionString = $"Data Source={dbPath};Cache=Shared;";

                YData.ConfigureAuto(connectionString, YDataType.Sqlite);
                _appDirectory = appDirectory;
                GetLogger().Info($"🗄️ 数据库切换: {dbPath}");
            }

            GetLogger().Info("✅ YData 配置完成");
        }
        catch (Exception ex)
        {
            GetLogger().Error($"❌ YData 配置失败: {ex.Message}");
            throw;
        }
    }
}