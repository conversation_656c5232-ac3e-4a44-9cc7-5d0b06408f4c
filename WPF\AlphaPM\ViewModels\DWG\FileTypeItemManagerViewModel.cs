using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Windows;
using AlphaPM.Models.DWG;
using AlphaPM.Services.DWG;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Prism.Services.Dialogs;
using Zylo.YLog.Runtime;
using Zylo.WPF.Controls;
using Zylo.WPF.Controls.TreeView;
using Prism.Mvvm;

namespace AlphaPM.ViewModels.DWG;

/// <summary>
/// 文件类型管理器 ViewModel - 现代化 MVVM 实现
/// </summary>
/// <remarks>
/// 🎯 核心功能：
/// - 管理FileTypeItemModel的完整生命周期（增删改查）
/// - 支持通用类型和TreeNode关联类型的管理
/// - 提供智能搜索和过滤功能
/// - 实现可视化的文件类型编辑界面
///
/// 📋 主要属性：
/// - FileTypes: 存储所有文件类型的观察集合
/// - SelectedFileType: 当前用户选中的文件类型
/// - EditingFileType: 正在编辑中的文件类型（支持新建/修改）
/// - IsEditing: 编辑模式状态标志
/// - IsNewMode: 新建模式标志（区分新建和编辑操作）
/// - SearchText: 搜索过滤文本（支持实时过滤）
/// - IsLoading: 加载状态指示器
/// - StatusMessage: 状态栏消息显示
///
/// 🎛️ 核心命令：
/// - LoadDataAsync(): 从数据库异步加载所有文件类型
/// - NewFileType(): 创建新文件类型并进入编辑模式
/// - EditFileType(): 编辑选中的文件类型（克隆对象避免直接修改）
/// - SaveAsync(): 保存文件类型到数据库（支持新建和更新）
/// - DeleteAsync(): 删除选中的文件类型
/// - Cancel(): 取消编辑操作并恢复原始状态
/// - ToggleGeneralMode(): 切换通用/专用模式
///
/// 🔄 数据流程：
/// 1. 初始化时自动调用LoadDataAsync()加载数据
/// 2. 用户操作触发相应命令
/// 3. 命令调用Service层进行业务处理
/// 4. Service层操作数据库并触发事件
/// 5. ViewModel监听事件并更新UI状态
///
/// 🎨 设计模式：
/// - MVVM模式：清晰的视图-视图模型分离
/// - 命令模式：使用RelayCommand处理用户交互
/// - 观察者模式：通过ObservableProperty实现数据绑定
///
/// 🛡️ 数据验证：
/// - 继承ObservableValidator提供验证支持
/// - 使用DataAnnotations进行模型验证
/// - CanExecute方法控制命令可用性
///
/// 🔧 技术特点：
/// - 使用CommunityToolkit.Mvvm自动生成样板代码
/// - 异步优先的操作模式
/// - 完整的错误处理和日志记录
/// - 支持取消操作和状态恢复
/// - 支持通用类型和TreeNode关联管理
/// </remarks>
public partial class FileTypeItemManagerViewModel : ObservableValidator, IDialogAware
{
    #region 字段和服务

    /// <summary>
    /// 数据服务接口
    /// </summary>
    private readonly FileTypeItemService _service;

    /// <summary>
    /// TreeNode数据服务（用于获取TreeNode列表）
    /// </summary>
    private readonly TreeNodeDataService _treeNodeService;

    /// <summary>
    /// 对话框服务
    /// </summary>
    private readonly IDialogService _dialogService;

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly YLoggerInstance _logger = YLogger.ForWarning<FileTypeItemManagerViewModel>();

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数 - 依赖注入
    /// </summary>
    public FileTypeItemManagerViewModel(
        FileTypeItemService service,
        TreeNodeDataService treeNodeService,
        IDialogService dialogService)
    {
        _service = service ?? throw new ArgumentNullException(nameof(service));
        _treeNodeService = treeNodeService ?? throw new ArgumentNullException(nameof(treeNodeService));
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));

        // 初始化时加载数据
        _ = Task.Run(async () =>
        {
            await LoadDataAsync();
            await LoadTreeNodesAsync();
        });
    }

    #endregion

    #region 可观察属性

    /// <summary>
    /// 文件类型列表（从数据库加载的所有类型）
    /// </summary>
    [ObservableProperty]
    [Description("文件类型列表")]
    public partial ObservableCollection<FileTypeItemModel> FileTypes { get; set; } = new();

    /// <summary>
    /// TreeNode列表（用于关联选择）
    /// </summary>
    [ObservableProperty]
    [Description("TreeNode列表")]
    public partial ObservableCollection<TreeNodeData> TreeNodes { get; set; } = new();

    /// <summary>
    /// 选中的文件类型（用户在列表中选择的项）
    /// </summary>
    [ObservableProperty]
    [Description("选中的文件类型")]
    public partial FileTypeItemModel? SelectedFileType { get; set; }

    /// <summary>
    /// 正在编辑的文件类型（编辑模式下的工作副本）
    /// </summary>
    [ObservableProperty]
    [Description("正在编辑的文件类型")]
    public partial FileTypeItemModel? EditingFileType { get; set; }

    /// <summary>
    /// 搜索文本（用于过滤文件类型列表）
    /// </summary>
    [ObservableProperty] 
    private string searchText = string.Empty;

    /// <summary>
    /// 是否正在加载（显示进度指示器）
    /// </summary>
    [ObservableProperty] 
    private bool isLoading;

    /// <summary>
    /// 状态消息（显示在状态栏）
    /// </summary>
    [ObservableProperty] 
    private string statusMessage = "就绪";

    /// <summary>
    /// 是否处于编辑模式（显示编辑表单）
    /// </summary>
    [ObservableProperty] 
    private bool isEditing;

    /// <summary>
    /// 是否为新建模式（用于区分新建和编辑）
    /// </summary>
    [ObservableProperty] 
    private bool isNewMode;

    #endregion

    #region 计算属性

    /// <summary>
    /// 过滤后的文件类型列表
    /// </summary>
    public IEnumerable<FileTypeItemModel> FilteredFileTypes
    {
        get
        {
            if (string.IsNullOrWhiteSpace(SearchText))
                return FileTypes;

            var keyword = SearchText.ToLower();
            return FileTypes.Where(ft =>
                ft.Name.ToLower().Contains(keyword) ||
                ft.Description.ToLower().Contains(keyword));
        }
    }

    /// <summary>
    /// 通用文件类型数量
    /// </summary>
    public int GeneralTypesCount => FileTypes.Count(x => x.IsGeneral);

    /// <summary>
    /// 专用文件类型数量
    /// </summary>
    public int SpecificTypesCount => FileTypes.Count(x => !x.IsGeneral);

    /// <summary>
    /// 启用的文件类型数量
    /// </summary>
    public int EnabledTypesCount => FileTypes.Count(x => x.IsEnabled);

    #endregion

    #region 命令 - 自动生成

    /// <summary>
    /// 加载数据命令 - 从数据库加载所有文件类型
    /// </summary>
    [RelayCommand]
    private async Task LoadDataAsync()
    {
        try
        {
            IsLoading = true;
            StatusMessage = "正在加载文件类型...";

            // 初始化数据库（如果需要的话）
            var types = await _service.GetAllAsync();

            FileTypes.Clear();
            foreach (var type in types) 
                FileTypes.Add(type);

            StatusMessage = $"已加载 {FileTypes.Count} 个文件类型 (通用: {GeneralTypesCount}, 专用: {SpecificTypesCount})";
            
            // 通知计算属性更新
            OnPropertyChanged(nameof(FilteredFileTypes));
            OnPropertyChanged(nameof(GeneralTypesCount));
            OnPropertyChanged(nameof(SpecificTypesCount));
            OnPropertyChanged(nameof(EnabledTypesCount));
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载失败: {ex.Message}";
            _logger.Error($"加载文件类型失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 加载TreeNode数据
    /// </summary>
    [RelayCommand]
    private async Task LoadTreeNodesAsync()
    {
        try
        {
            // 初始化TreeNode数据库（如果需要的话）
            var nodes = await _treeNodeService.GetAllAsync();

            TreeNodes.Clear();
            foreach (var node in nodes)
                TreeNodes.Add(node);

            _logger.Debug($"已加载 {TreeNodes.Count} 个TreeNode");
        }
        catch (Exception ex)
        {
            _logger.Error($"加载TreeNode失败: {ex}");
        }
    }

    /// <summary>
    /// 新建文件类型命令 - 创建新的文件类型并进入编辑模式
    /// </summary>
    [RelayCommand]
    private void NewFileType()
    {
        EditingFileType = new FileTypeItemModel
        {
            Name = "",
            Description = "",
            IsEnabled = true,
            IsDWG = true,
            IsGeneral = true,
            TreeNodeId = null
        };
        IsEditing = true;
        IsNewMode = true;
        StatusMessage = "正在创建新文件类型";
    }

    /// <summary>
    /// 编辑文件类型命令 - 编辑选中的文件类型
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanEdit))]
    private void EditFileType()
    {
        if (SelectedFileType == null) return;

        // 创建编辑副本
        EditingFileType = new FileTypeItemModel
        {
            Id = SelectedFileType.Id,
            Name = SelectedFileType.Name,
            Description = SelectedFileType.Description,
            IsEnabled = SelectedFileType.IsEnabled,
            IsDWG = SelectedFileType.IsDWG,
            IsGeneral = SelectedFileType.IsGeneral,
            TreeNodeId = SelectedFileType.TreeNodeId
        };
        
        IsEditing = true;
        IsNewMode = false;
        StatusMessage = $"正在编辑: {SelectedFileType.Name}";
    }

    /// <summary>
    /// 保存文件类型命令 - 保存编辑中的文件类型到数据库
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task SaveAsync()
    {
        if (EditingFileType == null) return;

        try
        {
            IsLoading = true;
            StatusMessage = "正在保存...";

            bool success = IsNewMode
                ? (await _service.AddAsync(EditingFileType)) != null
                : await _service.UpdateAsync(EditingFileType);

            StatusMessage = success 
                ? (IsNewMode ? "添加成功" : "修改成功") 
                : (IsNewMode ? "添加失败" : "修改失败");

            if (success)
            {
                await LoadDataAsync();
                EditingFileType = null;
                IsEditing = false;
                IsNewMode = false;
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"保存失败: {ex.Message}";
            _logger.Error($"保存文件类型失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 删除文件类型命令 - 删除选中的文件类型
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDelete))]
    private async Task DeleteAsync()
    {
        if (SelectedFileType == null) return;

        // 使用 YMessageBox 显示确认对话框
        var result = await YMessageBox.ShowConfirmAsync(
            $"确定要删除文件类型 '{SelectedFileType.Name}' 吗？\n\n此操作无法撤销。",
            "确认删除",
            "删除",
            "取消");

        if (result != YMessageBoxResult.Primary) return;

        try
        {
            IsLoading = true;
            StatusMessage = "正在删除...";

            var success = await _service.DeleteAsync(SelectedFileType.Id);
            StatusMessage = success ? "删除成功" : "删除失败";

            if (success)
            {
                await LoadDataAsync();
                SelectedFileType = null;
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"删除失败: {ex.Message}";
            _logger.Error($"删除文件类型失败: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 取消编辑命令 - 取消当前编辑操作
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        EditingFileType = null;
        IsEditing = false;
        IsNewMode = false;
        StatusMessage = "已取消编辑";
    }

    /// <summary>
    /// 切换通用模式命令 - 切换编辑项的通用/专用模式
    /// </summary>
    [RelayCommand]
    private void ToggleGeneralMode()
    {
        if (EditingFileType == null) return;

        EditingFileType.IsGeneral = !EditingFileType.IsGeneral;
        
        // 如果切换为通用模式，清除TreeNodeId
        if (EditingFileType.IsGeneral)
        {
            EditingFileType.TreeNodeId = null;
        }

        StatusMessage = EditingFileType.IsGeneral ? "已切换为通用类型" : "已切换为专用类型";
    }

    #endregion

    #region 命令可执行性检查

    /// <summary>
    /// 检查是否可以编辑
    /// </summary>
    private bool CanEdit() => SelectedFileType != null && !IsEditing;

    /// <summary>
    /// 检查是否可以保存
    /// </summary>
    private bool CanSave() => EditingFileType != null && !string.IsNullOrWhiteSpace(EditingFileType.Name);

    /// <summary>
    /// 检查是否可以删除
    /// </summary>
    private bool CanDelete() => SelectedFileType != null && !IsEditing;

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 当 SelectedFileType 属性变化时自动调用
    /// </summary>
    partial void OnSelectedFileTypeChanged(FileTypeItemModel? value)
    {
        EditFileTypeCommand.NotifyCanExecuteChanged();
        DeleteCommand.NotifyCanExecuteChanged();
    }

    /// <summary>
    /// 当 EditingFileType 属性变化时自动调用
    /// </summary>
    partial void OnEditingFileTypeChanged(FileTypeItemModel? value)
    {
        SaveCommand.NotifyCanExecuteChanged();
    }

    /// <summary>
    /// 当 SearchText 属性变化时自动调用
    /// </summary>
    partial void OnSearchTextChanged(string value)
    {
        OnPropertyChanged(nameof(FilteredFileTypes));
    }

    #endregion

    #region IDialogAware 接口实现

    /// <summary>
    /// 对话框标题
    /// </summary>
    public string Title { get; private set; } = "文件类型管理";

    /// <summary>
    /// 对话框关闭请求事件
    /// </summary>
    public event Action<IDialogResult>? RequestClose;

    /// <summary>
    /// 选中的 TreeNode（从对话框参数传入）
    /// </summary>
    [ObservableProperty]
    private TreeNodeData? selectedTreeNode;

    /// <summary>
    /// 是否可以关闭对话框
    /// </summary>
    public bool CanCloseDialog()
    {
        // 如果正在编辑且有未保存的更改，可以在这里添加确认逻辑
        return true;
    }

    /// <summary>
    /// 对话框关闭时调用
    /// </summary>
    public void OnDialogClosed()
    {
        _logger.Info("文件类型管理对话框已关闭");
    }

    /// <summary>
    /// 对话框打开时调用，接收参数
    /// </summary>
    public void OnDialogOpened(IDialogParameters parameters)
    {
        try
        {
            // 设置对话框窗口大小
            if (Application.Current.MainWindow != null)
            {
                var dialogWindow = Application.Current.Windows.OfType<Window>().LastOrDefault();
                if (dialogWindow != null && dialogWindow != Application.Current.MainWindow)
                {
                    // 设置对话框尺寸
                    // dialogWindow.Width = parameters.ContainsKey("Width") ? parameters.GetValue<int>("Width") : 1200;
                    // dialogWindow.Height = parameters.ContainsKey("Height") ? parameters.GetValue<int>("Height") : 800;
                    // dialogWindow.MinWidth = parameters.ContainsKey("MinWidth") ? parameters.GetValue<int>("MinWidth") : 800;
                    // dialogWindow.MinHeight = parameters.ContainsKey("MinHeight") ? parameters.GetValue<int>("MinHeight") : 800;
                    dialogWindow.Width = 800;
                    dialogWindow.Height = 800;
                    // 确保窗口居中显示
                    dialogWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                
                    _logger.Info($"设置对话框尺寸: {dialogWindow.Width}x{dialogWindow.Height}");
                }
            }

            // 获取对话框标题
            if (parameters.ContainsKey("Title"))
            {
                Title = parameters.GetValue<string>("Title");
            }

            // 获取选中的 TreeNode
            if (parameters.ContainsKey("SelectedTreeNode"))
            {
                SelectedTreeNode = parameters.GetValue<TreeNodeData>("SelectedTreeNode");
                _logger.Info($"接收到选中的TreeNode: {SelectedTreeNode?.Name} (ID: {SelectedTreeNode?.Id})");

                // 如果有选中的TreeNode，可以预设编辑表单的TreeNode关联
                if (SelectedTreeNode != null && EditingFileType != null)
                {
                    EditingFileType.TreeNodeId = SelectedTreeNode.Id;
                    EditingFileType.IsGeneral = false; // 默认设为非通用类型
                }
            }

            // 获取TreeNodeId（备用参数）
            if (parameters.ContainsKey("TreeNodeId"))
            {
                var treeNodeId = parameters.GetValue<int>("TreeNodeId");
                _logger.Info($"接收到TreeNodeId: {treeNodeId}");
            }

            _logger.Info($"文件类型管理对话框已打开，标题: {Title}");

            // 加载数据
            _ = LoadDataAsync();
        }
        catch (Exception ex)
        {
            _logger.Error($"处理对话框参数时出错: {ex}");
        }
    }

    /// <summary>
    /// 关闭对话框命令
    /// </summary>
    [RelayCommand]
    private void CloseDialog()
    {
        RequestClose?.Invoke(new DialogResult(ButtonResult.OK));
    }

    /// <summary>
    /// 取消对话框命令
    /// </summary>
    [RelayCommand]
    private void CancelDialog()
    {
        RequestClose?.Invoke(new DialogResult(ButtonResult.Cancel));
    }

    #endregion
}
