using Prism.Services.Dialogs;
using System.Windows;
using Wpf.Ui;
using Wpf.Ui.Appearance;
using Wpf.Ui.Controls;

namespace Zylo.WPF.Windows
{
    /// <summary>
    /// 自定义的 Prism 对话框窗口，使用 WPF-UI 的 FluentWindow 样式
    /// </summary>
    public partial class CustomDialogWindow : FluentWindow, IDialogWindow
    {
        public CustomDialogWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 对话框结果
        /// </summary>
        public IDialogResult Result { get; set; } = new DialogResult();
    }
}
