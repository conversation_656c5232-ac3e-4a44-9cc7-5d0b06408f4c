using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using AlphaPM.Extensions;
using AlphaPM.Models.DWG;
using AlphaPM.Views.DWG;
using Prism.Services.Dialogs;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Services.DWG;

/// <summary>
/// DWG节点数据服务 - DWG项目管理的完整业务逻辑
/// </summary>
/// <remarks>
/// 🎯 职责：
/// - 智能模板创建（包含业务规则）
/// - 添加、修改、删除的完整流程
/// - 数据验证和重复检查
/// - 业务规则处理
/// - 与数据库服务的协调
///
/// 💡 设计原则：
/// - 封装所有DWG相关的业务逻辑
/// - 提供高级的业务操作接口
/// - 处理复杂的数据验证和转换
/// - 协调TreeNodeDataExtensions和TreeNodeDataService
/// </remarks>
public class DWGNodeDataService
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<DWGNodeDataService>();
    private readonly TreeNodeDataService _treeNodeDataService;

    public DWGNodeDataService(TreeNodeDataService treeNodeDataService)
    {
        _treeNodeDataService = treeNodeDataService;
    }

    #region 智能模板创建

    /// <summary>
    /// 创建智能年份模板（自动计算下一年）
    /// </summary>
    /// <param name="treeData">当前树数据</param>
    /// <returns>预填的年份模板</returns>
    public TreeNodeData CreateNextYearTemplate(ObservableCollection<TreeNodeData> treeData)
    {
        var maxYear = GetMaxYearFromTreeData(treeData);
        var nextYear = maxYear + 1;

        _logger.Debug($"当前最大年份: {maxYear}, 预填下一年: {nextYear}");

        // 创建基础年份节点
        var yearNode = new TreeNodeData(nextYear.ToString())
        {
            Description = $"{nextYear}年度项目管理",
            NodeType = DWGNodeTypes.Year,
            Level = 1,
            ParentId = null,
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.CalendarLtr24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        // 设置DWG业务数据（年份节点不包含Manager）
        yearNode.SetDWGData(DWGBusinessData.CreateYearData());

        return yearNode;
    }

    /// <summary>
    /// 创建智能项目模板（基于父年份节点）
    /// </summary>
    /// <param name="yearNode">父年份节点</param>
    /// <param name="projectName">项目名称</param>
    /// <returns>预填的项目模板</returns>
    public TreeNodeData CreateProjectTemplate(TreeNodeData yearNode, string projectName = "")
    {
        if (string.IsNullOrEmpty(projectName))
        {
            var existingCount = yearNode.Children.Count;
            projectName = $"项目{existingCount + 1}";
        }

        var projectNode = new TreeNodeData(projectName)
        {
            Description = $"{yearNode.Name}年度{projectName}",
            NodeType = DWGNodeTypes.Project,
            Level = 2,
            ParentId = yearNode.Id,
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.BuildingBank24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        // 设置DWG业务数据（项目节点包含基础项目管理信息）
        projectNode.SetDWGData(DWGBusinessData.CreateProjectData("", 0, null, null));

        return projectNode;
    }

    /// <summary>
    /// 创建智能栋号模板（基于父项目节点）
    /// </summary>
    /// <param name="projectNode">父项目节点</param>
    /// <param name="buildingName">栋号名称</param>
    /// <returns>预填的栋号模板</returns>
    public TreeNodeData CreateBuildingTemplate(TreeNodeData projectNode, string buildingName = "")
    {
        if (string.IsNullOrEmpty(buildingName))
        {
            var existingCount = projectNode.Children.Count;
            buildingName = $"{(char)('A' + existingCount)}栋";
        }

        var buildingNode = new TreeNodeData(buildingName)
        {
            Description = $"{projectNode.Name} - {buildingName}",
            NodeType = DWGNodeTypes.Building,
            Level = 3,
            ParentId = projectNode.Id,
            WpfUiSymbol = Wpf.Ui.Controls.SymbolRegular.Building24,
            CreatedAt = DateTime.Now,
            ModifiedAt = DateTime.Now
        };

        // 设置DWG业务数据（栋号节点包含完整的建筑和设计信息）
        buildingNode.SetDWGData(DWGBusinessData.CreateBuildingData("", 0, 0, "规划中", "", ""));

        return buildingNode;
    }

    #endregion

    #region 完整的添加节点业务逻辑

    /// <summary>
    /// 添加年份节点的完整业务逻辑
    /// </summary>
    /// <param name="treeData">当前树数据</param>
    /// <param name="dialogService">对话框服务</param>
    /// <returns>添加成功的节点，失败返回null</returns>
    public async Task<TreeNodeData?> AddYearAsync(ObservableCollection<TreeNodeData> treeData, IDialogService dialogService)
    {
        try
        {
            _logger.Debug("开始添加年份节点流程");

            // 1. 创建智能预填模板
            var yearTemplate = CreateNextYearTemplate(treeData);

            var parameters = new DialogParameters
            {
                { "IsEditMode", false },
                { "NodeData", yearTemplate }  // 传递预填的模板数据
            };

            // 2. 显示对话框并等待结果
            TreeNodeData? result = null;
            var dialogCompleted = false;

            dialogService.ShowDialog(nameof(NodeEditView), parameters, dialogResult =>
            {
                try
                {
                    if (dialogResult.Result == ButtonResult.OK)
                    {
                        // 3. 处理对话框结果
                        var task = CreateYearFromDialogAsync(dialogResult, treeData);
                        result = task.GetAwaiter().GetResult();
                    }
                    else
                    {
                        _logger.Debug("用户取消了添加年份操作");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"处理年份添加对话框结果失败: {ex.Message}");
                }
                finally
                {
                    dialogCompleted = true;
                }
            });

            // 4. 等待对话框完成
            while (!dialogCompleted)
            {
                await Task.Delay(50);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.Error($"添加年份节点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 添加项目节点的完整业务逻辑
    /// </summary>
    /// <param name="parentYearNode">父年份节点</param>
    /// <param name="dialogService">对话框服务</param>
    /// <returns>添加成功的节点，失败返回null</returns>
    public async Task<TreeNodeData?> AddProjectAsync(TreeNodeData parentYearNode, IDialogService dialogService)
    {
        try
        {
            _logger.Debug($"开始添加项目节点流程，父节点: {parentYearNode.Name}");

            // 1. 创建智能预填模板
            var projectTemplate = CreateProjectTemplate(parentYearNode);

            var parameters = new DialogParameters
            {
                { "Title", DWGIconManager.GetAddTitle(2) },
                { "NodeType", DWGNodeTypes.Project },
                { "IsEditMode", false },
                { "NodeData", projectTemplate }  // 传递预填的模板数据
            };

            // 2. 显示对话框并等待结果
            TreeNodeData? result = null;
            var dialogCompleted = false;

            dialogService.ShowDialog(nameof(NodeEditView), parameters, dialogResult =>
            {
                try
                {
                    if (dialogResult.Result == ButtonResult.OK)
                    {
                        // 3. 处理对话框结果
                        var task = CreateProjectFromDialogAsync(dialogResult, parentYearNode);
                        result = task.GetAwaiter().GetResult();
                    }
                    else
                    {
                        _logger.Debug("用户取消了添加项目操作");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"处理项目添加对话框结果失败: {ex.Message}");
                }
                finally
                {
                    dialogCompleted = true;
                }
            });

            // 4. 等待对话框完成
            while (!dialogCompleted)
            {
                await Task.Delay(50);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.Error($"添加项目节点失败: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 节点创建业务逻辑

    /// <summary>
    /// 从对话框结果创建年份节点并保存到数据库
    /// </summary>
    /// <param name="dialogResult">对话框结果</param>
    /// <param name="treeData">当前树数据</param>
    /// <returns>保存成功的节点，失败返回null</returns>
    public async Task<TreeNodeData?> CreateYearFromDialogAsync(IDialogResult dialogResult, ObservableCollection<TreeNodeData> treeData)
    {
        try
        {
            if (dialogResult.Result != ButtonResult.OK)
            {
                _logger.Debug("用户取消了添加年份操作");
                return null;
            }

            // 获取用户修改后的节点（NodeEditView应该返回修改后的模板）
            var modifiedNode = dialogResult.Parameters.GetValue<TreeNodeData>("ModifiedNode");
            if (modifiedNode == null)
            {
                throw new InvalidOperationException("NodeEditView 未返回修改后的节点数据");
            }

            _logger.Info($"📝 开始创建顶级节点 - 名称: '{modifiedNode.Name}', 描述: '{modifiedNode.Description}'");

            // 业务验证：检查同级重复（顶级节点之间不能重名）
            if (await IsSiblingNodeExistsAsync(modifiedNode.Name, null))
            {
                throw new InvalidOperationException($"顶级节点 '{modifiedNode.Name}' 已存在");
            }

            // 设置数据库相关属性（只设置数据库需要的属性）
            modifiedNode.Id = 0; // 新节点ID为0
            modifiedNode.ParentId = null;
            modifiedNode.SortOrder = treeData.Count + 1;
            modifiedNode.CreatedAt = DateTime.Now;
            modifiedNode.ModifiedAt = DateTime.Now;

            // 保存到数据库（直接使用用户修改后的节点）
            var savedNode = await _treeNodeDataService.AddAsync(modifiedNode);

            _logger.Info($"年份节点创建成功: {savedNode.Name} (ID: {savedNode.Id})");
            return savedNode;
        }
        catch (Exception ex)
        {
            _logger.Error($"创建年份节点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 从对话框结果创建项目节点并保存到数据库
    /// </summary>
    /// <param name="dialogResult">对话框结果</param>
    /// <param name="parentYearNode">父年份节点</param>
    /// <returns>保存成功的节点，失败返回null</returns>
    public async Task<TreeNodeData?> CreateProjectFromDialogAsync(IDialogResult dialogResult, TreeNodeData parentYearNode)
    {
        try
        {
            if (dialogResult.Result != ButtonResult.OK)
            {
                _logger.Debug("用户取消了添加项目操作");
                return null;
            }

            // 获取用户修改后的节点（NodeEditView应该返回修改后的模板）
            var modifiedNode = dialogResult.Parameters.GetValue<TreeNodeData>("ModifiedNode");
            if (modifiedNode == null)
            {
                throw new InvalidOperationException("NodeEditView 未返回修改后的节点数据");
            }

            var dwgData = modifiedNode.GetDWGData();
            _logger.Info($"📝 开始创建项目节点 - 名称: '{modifiedNode.Name}', 经理: '{dwgData?.Manager}', 预算: {dwgData?.Budget}万元");

            // 业务验证：检查同级重复（同一父节点下不能重名）
            if (await IsSiblingNodeExistsAsync(modifiedNode.Name, parentYearNode.Id))
            {
                throw new InvalidOperationException($"项目 '{modifiedNode.Name}' 在 '{parentYearNode.Name}' 中已存在");
            }

            // 设置数据库相关属性（只设置数据库需要的属性）
            modifiedNode.Id = 0; // 新节点ID为0
            modifiedNode.ParentId = parentYearNode.Id;
            modifiedNode.SortOrder = parentYearNode.Children.Count + 1;
            modifiedNode.CreatedAt = DateTime.Now;
            modifiedNode.ModifiedAt = DateTime.Now;

            // 保存到数据库（直接使用用户修改后的节点）
            var savedNode = await _treeNodeDataService.AddAsync(modifiedNode);

            _logger.Info($"项目节点创建成功: {savedNode.Name} (ID: {savedNode.Id})");
            return savedNode;
        }
        catch (Exception ex)
        {
            _logger.Error($"创建项目节点失败: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 节点更新业务逻辑

    /// <summary>
    /// 从对话框结果更新节点数据并保存到数据库
    /// </summary>
    /// <param name="node">要更新的节点</param>
    /// <param name="dialogResult">对话框结果</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateNodeFromDialogAsync(TreeNodeData node, IDialogResult dialogResult)
    {
        try
        {
            if (dialogResult.Result != ButtonResult.OK)
            {
                _logger.Debug("用户取消了编辑操作");
                return false;
            }

            var parameters = dialogResult.Parameters;

            // 获取基本信息
            var newName = parameters.GetValue<string>("NodeName");
            var newDescription = parameters.GetValue<string>("NodeDescription");

            _logger.Info($"📝 开始更新节点 - 原名称: '{node.Name}' → 新名称: '{newName}'");

            // 业务验证：如果名称改变，检查重复
            if (node.Name != newName)
            {
                if (await IsNodeNameExistsAsync(newName, node.NodeType, node.ParentId, node.Id))
                {
                    throw new InvalidOperationException($"名称 '{newName}' 已存在");
                }
            }

            // 更新基本信息
            node.Name = newName;
            node.Description = newDescription;
            node.ModifiedAt = DateTime.Now;

            // 根据节点类型更新DWG业务数据
            UpdateDWGBusinessDataFromDialog(node, parameters);

            // 保存到数据库
            var success = await _treeNodeDataService.UpdateAsync(node);

            if (success)
            {
                _logger.Info($"节点更新成功: {node.Name} ({node.NodeType})");
            }
            else
            {
                _logger.Warning($"节点更新失败: {node.Name}");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.Error($"更新节点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 根据对话框参数更新节点的DWG业务数据
    /// </summary>
    /// <param name="node">节点</param>
    /// <param name="parameters">对话框参数</param>
    private void UpdateDWGBusinessDataFromDialog(TreeNodeData node, IDialogParameters parameters)
    {
        switch (node.NodeType)
        {
            case DWGNodeTypes.Year:
                // 年份节点只更新基本信息，不更新DWG业务数据
                break;

            case DWGNodeTypes.Project:
                // 更新项目相关的DWG数据
                var manager = parameters.GetValue<string>("Manager") ?? "";
                var budget = parameters.GetValue<decimal>("Budget");
                var startDate = parameters.GetValue<DateTime?>("StartDate");
                var endDate = parameters.GetValue<DateTime?>("EndDate");
                var projectStatus = parameters.GetValue<string>("ProjectStatus") ?? "";

                node.SetDWGManager(manager)
                    .SetDWGBudget(budget)
                    .SetDWGStartDate(startDate)
                    .SetDWGEndDate(endDate)
                    .SetDWGProjectStatus(projectStatus);
                break;

            case DWGNodeTypes.Building:
                // 更新建筑相关的DWG数据
                var buildingManager = parameters.GetValue<string>("Manager") ?? "";
                var buildingArea = parameters.GetValue<decimal>("BuildingArea");
                var floorCount = parameters.GetValue<int>("FloorCount");
                var buildingType = parameters.GetValue<string>("BuildingType") ?? "";
                var buildingStatus = parameters.GetValue<string>("BuildingStatus") ?? "";
                var designer = parameters.GetValue<string>("Designer") ?? "";
                var designStage = parameters.GetValue<string>("DesignStage") ?? "";

                node.SetDWGManager(buildingManager)
                    .SetDWGBuildingArea(buildingArea)
                    .SetDWGFloorCount(floorCount)
                    .SetDWGBuildingType(buildingType)
                    .SetDWGBuildingStatus(buildingStatus)
                    .SetDWGDesigner(designer)
                    .SetDWGDesignStage(designStage);
                break;

            default:
                _logger.Warning($"未知的节点类型: {node.NodeType}");
                break;
        }
    }

    #endregion

    #region 业务验证方法

    /// <summary>
    /// 检查同级节点是否已存在（通用的同级重名检测）
    /// </summary>
    /// <param name="nodeName">节点名称</param>
    /// <param name="parentId">父节点ID，如果为null则检查顶级节点</param>
    /// <returns>是否存在同名的同级节点</returns>
    private async Task<bool> IsSiblingNodeExistsAsync(string nodeName, int? parentId)
    {
        List<TreeNodeData> siblings;

        if (parentId == null)
        {
            // 检查顶级节点（ParentId为null的节点）
            siblings = await _treeNodeDataService.GetRootNodesAsync();
        }
        else
        {
            // 检查指定父节点下的子节点
            siblings = await _treeNodeDataService.GetChildrenAsync(parentId.Value);
        }

        // 检查是否有同名节点（忽略大小写）
        return siblings.Any(node => node.Name.Equals(nodeName, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 检查节点名称是否已存在（用于更新时的重复检查）
    /// </summary>
    /// <param name="nodeName">节点名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="parentId">父节点ID</param>
    /// <param name="excludeId">排除的节点ID（当前节点）</param>
    /// <returns>是否存在</returns>
    private async Task<bool> IsNodeNameExistsAsync(string nodeName, string nodeType, int? parentId, int excludeId)
    {
        if (nodeType == DWGNodeTypes.Year)
        {
            // 年份节点检查全局重复
            var existing = await _treeNodeDataService.GetByNameAsync(nodeName);
            return existing != null && existing.Id != excludeId;
        }
        else
        {
            // 其他节点检查同级重复
            if (parentId.HasValue)
            {
                var siblings = await _treeNodeDataService.GetChildrenAsync(parentId.Value);
                return siblings.Any(s => s.Name.Equals(nodeName, StringComparison.OrdinalIgnoreCase) && s.Id != excludeId);
            }
        }

        return false;
    }

    /// <summary>
    /// 验证年份格式是否有效
    /// </summary>
    /// <param name="yearName">年份名称</param>
    /// <returns>是否有效</returns>
    private bool IsValidYear(string yearName)
    {
        if (int.TryParse(yearName, out var year))
        {
            return year >= 1900 && year <= 2100;
        }
        return false;
    }

    #endregion

    #region 辅助工具方法

    /// <summary>
    /// 获取当前树数据中的最大年份
    /// </summary>
    /// <param name="treeData">树数据</param>
    /// <returns>最大年份，如果没有数据则返回当前年份</returns>
    private int GetMaxYearFromTreeData(ObservableCollection<TreeNodeData> treeData)
    {
        if (treeData == null || treeData.Count == 0)
        {
            return DateTime.Now.Year;
        }

        var maxYear = DateTime.Now.Year;
        foreach (var node in treeData)
        {
            if (int.TryParse(node.Name, out var year))
            {
                if (year > maxYear)
                {
                    maxYear = year;
                }
            }
        }

        return maxYear;
    }

    /// <summary>
    /// 计算年份节点的总预算（所有子项目预算之和）
    /// </summary>
    /// <param name="yearNode">年份节点</param>
    /// <returns>总预算</returns>
    public decimal CalculateYearTotalBudget(TreeNodeData yearNode)
    {
        if (yearNode.NodeType != DWGNodeTypes.Year)
        {
            return 0;
        }

        return yearNode.Children
            .Where(project => project.NodeType == DWGNodeTypes.Project)
            .Sum(project => project.GetDWGBudget());
    }

    /// <summary>
    /// 计算项目节点的建筑总面积（所有子栋号面积之和）
    /// </summary>
    /// <param name="projectNode">项目节点</param>
    /// <returns>总建筑面积</returns>
    public decimal CalculateProjectTotalArea(TreeNodeData projectNode)
    {
        if (projectNode.NodeType != DWGNodeTypes.Project)
        {
            return 0;
        }

        return projectNode.Children
            .Where(building => building.NodeType == DWGNodeTypes.Building)
            .Sum(building => building.GetDWGBuildingArea());
    }

    /// <summary>
    /// 获取节点的完整路径描述
    /// </summary>
    /// <param name="node">节点</param>
    /// <returns>路径描述</returns>
    public string GetNodeFullPath(TreeNodeData node)
    {
        var pathParts = new List<string>();
        var current = node;

        while (current != null)
        {
            pathParts.Insert(0, current.Name);
            current = current.YParent;
        }

        return string.Join(" > ", pathParts);
    }

    /// <summary>
    /// 检查节点是否可以删除（业务规则检查）
    /// </summary>
    /// <param name="node">要删除的节点</param>
    /// <returns>是否可以删除及原因</returns>
    public (bool CanDelete, string Reason) CanDeleteNode(TreeNodeData node)
    {
        switch (node.NodeType)
        {
            case DWGNodeTypes.Year:
                if (node.Children.Any())
                {
                    return (false, "年份节点包含项目，无法删除");
                }
                break;

            case DWGNodeTypes.Project:
                if (node.Children.Any())
                {
                    return (false, "项目节点包含栋号，无法删除");
                }
                break;

            case DWGNodeTypes.Building:
                // 栋号节点可以直接删除
                break;

            default:
                return (false, "未知的节点类型");
        }

        return (true, "");
    }

    #endregion

    #region 节点编辑和删除业务逻辑

    /// <summary>
    /// 更新节点（包含同级重名检测）
    /// </summary>
    /// <param name="node">要更新的节点</param>
    /// <param name="newName">新名称</param>
    /// <param name="newDescription">新描述</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateNodeAsync(TreeNodeData node, string newName, string newDescription)
    {
        try
        {
            // 如果名称有变化，检查同级重名
            if (!node.Name.Equals(newName, StringComparison.OrdinalIgnoreCase))
            {
                if (await IsSiblingNodeExistsAsync(newName, node.ParentId))
                {
                    throw new InvalidOperationException($"名称 '{newName}' 已存在，请使用其他名称");
                }
            }

            // 更新节点数据
            node.Name = newName;
            node.Description = newDescription;
            node.ModifiedAt = DateTime.Now;

            // 保存到数据库
            var success = await _treeNodeDataService.UpdateAsync(node);

            if (success)
            {
                _logger.Info($"✅ 节点更新成功: {newName}");
            }

            return success != null;
        }
        catch (Exception ex)
        {
            _logger.Error($"更新节点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 删除节点
    /// </summary>
    /// <param name="node">要删除的节点</param>
    /// <returns>是否删除成功</returns>
    public async Task<bool> DeleteNodeAsync(TreeNodeData node)
    {
        try
        {
            // 从数据库中删除
            await _treeNodeDataService.DeleteAsync(node.Id);

            _logger.Info($"✅ 节点删除成功: {node.Name}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"删除节点失败: {ex.Message}");
            throw;
        }
    }

    #endregion

    #region 初始数据创建

    /// <summary>
    /// 创建初始示例数据（使用智能模板）
    /// </summary>
    /// <returns>是否创建成功</returns>
    public async Task<bool> CreateInitialDataAsync()
    {
        _logger.Debug("开始创建DWG初始数据（使用智能模板）");

        try
        {
            // 1. 使用智能模板创建2024年份节点
            var emptyTreeData = new ObservableCollection<TreeNodeData>(); // 空集合，会自动创建当前年份
            var year2024Template = CreateNextYearTemplate(emptyTreeData);

            // 手动设置为2024年（演示数据）
            year2024Template.Name = "2024";
            year2024Template.Description = "2024年度项目管理";
            year2024Template.Id = 0;
            year2024Template.SortOrder = 1;

            var savedYear = await _treeNodeDataService.AddAsync(year2024Template);
            _logger.Info($"✅ 创建年份节点: {savedYear.Name} (ID:{savedYear.Id})");

            // 2. 使用智能模板创建示例项目节点
            var projectTemplate = CreateProjectTemplate(savedYear, "示例项目");
            projectTemplate.ParentId = savedYear.Id; // 确保父ID正确
            projectTemplate.Id = 0;
            projectTemplate.SortOrder = 1;

            // 设置项目具体信息
            projectTemplate.SetDWGManager("项目经理")
                           .SetDWGBudget(10000000m)
                           .SetDWGStartDate(DateTime.Now)
                           .SetDWGEndDate(DateTime.Now.AddMonths(12))
                           .SetDWGProjectStatus("进行中");

            var savedProject = await _treeNodeDataService.AddAsync(projectTemplate);
            _logger.Info($"✅ 创建项目节点: {savedProject.Name} (ID:{savedProject.Id})");

            // 3. 使用智能模板创建示例栋号节点
            var buildingTemplate = CreateBuildingTemplate(savedProject, "1#栋");
            buildingTemplate.ParentId = savedProject.Id; // 确保父ID正确
            buildingTemplate.Id = 0;
            buildingTemplate.SortOrder = 1;

            // 设置建筑具体信息
            buildingTemplate.SetDWGManager("建筑师")
                           .SetDWGBuildingType("商业综合体")
                           .SetDWGBuildingArea(15000m)
                           .SetDWGFloorCount(30)
                           .SetDWGBuildingStatus("在建")
                           .SetDWGDesigner("施工图设计师")
                           .SetDWGDesignStage("施工图设计");

            var savedBuilding = await _treeNodeDataService.AddAsync(buildingTemplate);
            _logger.Info($"✅ 创建栋号节点: {savedBuilding.Name} (ID:{savedBuilding.Id})");

            _logger.Info("🎉 DWG初始数据创建完成！（使用智能模板）");
            _logger.Info($"📊 数据结构: 📅 {savedYear.Name}年 → 📁 {savedProject.Name} → 🏢 {savedBuilding.Name}");

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ 创建DWG初始数据失败: {ex.Message}");
            return false;
        }
    }

    #endregion
}



