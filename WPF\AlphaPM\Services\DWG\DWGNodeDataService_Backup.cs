#if false
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using AlphaPM.Extensions;
using AlphaPM.Models.DWG;

using Prism.Services.Dialogs;
using Zylo.WPF.Controls.TreeView;

namespace AlphaPM.Services.DWG;





// ==================== 备份代码开始 ====================
// 重新规划前的原始代码，备份于此
// 日期: 2024年
// 说明: 功能重新规划前的完整代码备份

/// <summary>
/// DWG节点数据服务
/// 实现添加、修改、删除、更新的业务逻辑过程
/// </summary>
public class DWGNodeDataService
{
    private readonly YLoggerInstance _logger = YLogger.ForDebug<DWGNodeDataService>();
    private readonly TreeNodeDataService _treeNodeDataService;

    public DWGNodeDataService(TreeNodeDataService treeNodeDataService)
    {
        _treeNodeDataService = treeNodeDataService;
    }

    #region 年份节点业务逻辑

    /// <summary>
    /// 获取当前最大年份并创建下一年的模板
    /// </summary>
    /// <param name="treeData">当前树数据</param>
    /// <returns>预填的年份模板</returns>
    public TreeNodeData CreateNextYearTemplate(ObservableCollection<TreeNodeData> treeData)
    {
        var maxYear = GetMaxYearFromTreeData(treeData);
        var nextYear = maxYear + 1;

        _logger.Debug($"当前最大年份: {maxYear}, 预填下一年: {nextYear}");

        return TreeNodeDataExtensions.CreateDWGYearTemplate(
            nextYear.ToString(),
            $"{nextYear}年度项目管理"
        );
    }

    /// <summary>
    /// 从对话框结果创建年份节点并保存到数据库
    /// </summary>
    /// <param name="dialogResult">对话框结果</param>
    /// <param name="treeData">当前树数据</param>
    /// <returns>保存成功的节点，失败返回null</returns>
    public async Task<TreeNodeData?> CreateYearFromDialogAsync(IDialogResult dialogResult, ObservableCollection<TreeNodeData> treeData)
    {
        try
        {
            if (dialogResult.Result != ButtonResult.OK)
            {
                _logger.Debug("用户取消了添加年份操作");
                return null;
            }

            // 获取用户输入的数据
            var nodeName = dialogResult.Parameters.GetValue<string>("NodeName");
            var nodeDescription = dialogResult.Parameters.GetValue<string>("NodeDescription");

            _logger.Info($"📝 开始创建年份节点 - 名称: '{nodeName}', 描述: '{nodeDescription}'");

            // 检查重复
            var existingYear = await _treeNodeDataService.GetByNameAsync(nodeName);
            if (existingYear != null)
            {
                _logger.Warning($"年份 '{nodeName}' 已存在");
                throw new InvalidOperationException($"年份 '{nodeName}' 已存在");
            }

            // 使用TreeNodeDataExtensions创建年份节点
            var yearNode = TreeNodeDataExtensions.CreateDWGYearTemplate(nodeName, nodeDescription);

            // 设置数据库相关属性
            yearNode.Id = 0; // 新节点ID为0
            yearNode.SortOrder = treeData.Count + 1;

            // 保存到数据库
            var savedNode = await _treeNodeDataService.AddAsync(yearNode);

            _logger.Info($"年份节点创建成功: {savedNode.Name} (ID: {savedNode.Id})");
            return savedNode;
        }
        catch (Exception ex)
        {
            _logger.Error($"创建年份节点失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 获取当前树数据中的最大年份
    /// </summary>
    /// <param name="treeData">树数据</param>
    /// <returns>最大年份，如果没有数据则返回当前年份</returns>
    private int GetMaxYearFromTreeData(ObservableCollection<TreeNodeData> treeData)
    {
        if (treeData == null || treeData.Count == 0)
        {
            return DateTime.Now.Year;
        }

        var maxYear = DateTime.Now.Year;
        foreach (var node in treeData)
        {
            if (int.TryParse(node.Name, out var year))
            {
                if (year > maxYear)
                {
                    maxYear = year;
                }
            }
        }

        return maxYear;
    }

    #endregion

    /// <summary>
    /// 处理节点编辑结果，更新节点数据
    /// </summary>
    /// <param name="node">要更新的节点</param>
    /// <param name="dialogResult">编辑对话框的结果</param>
    /// <returns>是否更新成功</returns>
    public bool UpdateNodeFromDialogResult(TreeNodeData node, IDialogResult dialogResult)
    {
        try
        {
            if (dialogResult.Result != ButtonResult.OK)
            {
                _logger.Debug("用户取消了编辑操作");
                return false;
            }

            var parameters = dialogResult.Parameters;

            // 更新基本信息
            node.Name = parameters.GetValue<string>("NodeName");
            node.Description = parameters.GetValue<string>("NodeDescription");
            node.ModifiedAt = DateTime.Now;

            // 根据节点类型更新DWG业务数据
            UpdateDWGBusinessData(node, parameters);

            _logger.Info($"节点数据更新成功: {node.Name} ({node.NodeType})");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"更新节点数据失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 创建新节点从编辑结果
    /// </summary>
    /// <param name="dialogResult">编辑对话框的结果</param>
    /// <param name="parentId">父节点ID</param>
    /// <returns>创建的新节点</returns>
    public TreeNodeData? CreateNodeFromDialogResult(IDialogResult dialogResult, int parentId = 0)
    {
        try
        {
            if (dialogResult.Result != ButtonResult.OK)
            {
                _logger.Debug("用户取消了创建操作");
                return null;
            }

            var parameters = dialogResult.Parameters;
            var nodeType = parameters.GetValue<string>("NodeType");
            var nodeName = parameters.GetValue<string>("NodeName");

            TreeNodeData newNode;

            // 根据节点类型创建对应的节点
            switch (nodeType)
            {
                case DWGNodeTypes.Year:
                    newNode = TreeNodeDataExtensions.CreateDWGYearTemplate(nodeName);
                    break;

                case DWGNodeTypes.Project:
                    var manager = parameters.GetValue<string>("Manager");
                    var budget = parameters.GetValue<decimal>("Budget");
                    var startDate = parameters.GetValue<DateTime?>("StartDate");
                    var endDate = parameters.GetValue<DateTime?>("EndDate");
                    newNode = TreeNodeDataExtensions.CreateDWGProjectTemplate(
                        nodeName, parentId, manager, budget, startDate, endDate);
                    break;

                case DWGNodeTypes.Building:
                    var buildingManager = parameters.GetValue<string>("Manager");
                    var designer = parameters.GetValue<string>("Designer");
                    var buildingType = parameters.GetValue<string>("BuildingType");
                    var buildingArea = parameters.GetValue<decimal>("BuildingArea");
                    var floorCount = parameters.GetValue<int>("FloorCount");
                    var buildingStatus = parameters.GetValue<string>("BuildingStatus");
                    newNode = TreeNodeDataExtensions.CreateDWGBuildingTemplate(
                        nodeName, parentId, buildingType, buildingArea, floorCount, 
                        buildingStatus, buildingManager, designer);
                    break;

                default:
                    _logger.Warning($"不支持的节点类型: {nodeType}");
                    return null;
            }

            // 设置描述和其他基本信息
            newNode.Description = parameters.GetValue<string>("NodeDescription");
            newNode.ParentId = parentId;

            // 更新DWG业务数据
            UpdateDWGBusinessData(newNode, parameters);

            _logger.Info($"新节点创建成功: {newNode.Name} ({newNode.NodeType})");
            return newNode;
        }
        catch (Exception ex)
        {
            _logger.Error($"创建节点失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 更新节点的DWG业务数据
    /// </summary>
    /// <param name="node">节点</param>
    /// <param name="parameters">参数</param>
    private void UpdateDWGBusinessData(TreeNodeData node, IDialogParameters parameters)
    {
        var dwgData = node.EnsureDWGData();

        // 根据节点类型更新相应的字段
        switch (node.NodeType)
        {
            case DWGNodeTypes.Year:
                // 年份节点只更新预算（通常由子项目汇总）
                dwgData.Budget = parameters.GetValue<decimal>("Budget");
                // 年份节点不设置Manager
                dwgData.Manager = string.Empty;
                break;

            case DWGNodeTypes.Project:
                // 项目节点更新项目管理相关字段
                dwgData.Manager = parameters.GetValue<string>("Manager");
                dwgData.Budget = parameters.GetValue<decimal>("Budget");
                dwgData.StartDate = parameters.GetValue<DateTime?>("StartDate");
                dwgData.EndDate = parameters.GetValue<DateTime?>("EndDate");
                dwgData.ProjectStatus = parameters.GetValue<string>("ProjectStatus");
                break;

            case DWGNodeTypes.Building:
                // 栋号节点更新所有相关字段
                dwgData.Manager = parameters.GetValue<string>("Manager");
                dwgData.Designer = parameters.GetValue<string>("Designer");
                dwgData.Budget = parameters.GetValue<decimal>("Budget");
                dwgData.StartDate = parameters.GetValue<DateTime?>("StartDate");
                dwgData.EndDate = parameters.GetValue<DateTime?>("EndDate");
                dwgData.ProjectStatus = parameters.GetValue<string>("ProjectStatus");
                dwgData.BuildingType = parameters.GetValue<string>("BuildingType");
                dwgData.BuildingArea = parameters.GetValue<decimal>("BuildingArea");
                dwgData.FloorCount = parameters.GetValue<int>("FloorCount");
                dwgData.BuildingStatus = parameters.GetValue<string>("BuildingStatus");
                dwgData.DesignStage = parameters.GetValue<string>("DesignStage");
                dwgData.DesignProgress = parameters.GetValue<double>("DesignProgress");
                break;
        }

        _logger.Debug($"DWG业务数据更新完成: {node.NodeType} - {node.Name}");
    }

    /// <summary>
    /// 保存节点到数据库
    /// </summary>
    /// <param name="node">要保存的节点</param>
    /// <returns>保存是否成功</returns>
    public async Task<bool> SaveNodeToDatabaseAsync(TreeNodeData node)
    {
        try
        {
            // TODO: 实现数据库保存逻辑
            // 这里应该调用相应的数据服务来保存节点数据
            
            _logger.Info($"节点已保存到数据库: {node.Name} (ID: {node.Id})");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"保存节点到数据库失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 更新节点在数据库中的数据
    /// </summary>
    /// <param name="node">要更新的节点</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UpdateNodeInDatabaseAsync(TreeNodeData node)
    {
        try
        {
            // TODO: 实现数据库更新逻辑
            // 这里应该调用相应的数据服务来更新节点数据
            
            _logger.Info($"节点已在数据库中更新: {node.Name} (ID: {node.Id})");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error($"更新数据库中的节点失败: {ex.Message}");
            return false;
        }
    }
}
// ==================== 备份代码结束 ====================
#endif