using AlphaPM.Models.DWG;
using AlphaPM.Services.Base;
using Zylo.YData;
using Zylo.YLog.Runtime;

namespace AlphaPM.Services.DWG;

/// <summary>
/// FileTypeItemModel 数据服务
/// </summary>
/// <remarks>
/// 🎯 功能特点：
/// - 基于 FileTypeItemModel 的数据库操作
/// - 简化版本，功能齐全但不复杂
/// - 支持基础 CRUD 操作
/// - 自动初始化默认数据
/// 
/// 🔧 技术特点：
/// - 使用 YData ORM 框架
/// - 支持异步操作
/// - 自动数据库表创建
/// - 内置日志记录
/// 
/// 💡 使用场景：
/// - 文件类型管理
/// - 下拉列表数据源
/// - 文件分类功能
/// </remarks>
public class FileTypeItemService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly YLoggerInstance _logger = YLogger.ForDebug<FileTypeItemService>();
    // private readonly YLoggerInstance _logger = YLogger.ForWarning<FileTypeItemService>();
    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数
    /// </summary>
    public FileTypeItemService()
    {
        _logger.Debug("FileTypeItemService 初始化");
        YServiceBase.ConnectionData();
        InitializeAsync();
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化数据库表结构
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            _logger.Debug("开始初始化 FileTypeItemModel 表结构");

            // 使用 YData 的 CodeFirst 功能创建表
            YData.FreeSql.CodeFirst.SyncStructure<FileTypeItemModel>();

            _logger.Info("FileTypeItemModel 表结构初始化完成");

            // 检查是否有初始数据
            var count = await YData.Select<FileTypeItemModel>().CountAsync();
            if (count == 0)
            {
                await CreateInitialDataAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.Error($"初始化 FileTypeItemModel 表结构失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 创建初始数据
    /// </summary>
    private async Task CreateInitialDataAsync()
    {
        _logger.Debug("开始创建初始数据");

        var defaultItems = new List<FileTypeItemModel>
        {
            new() { Name = "DWG图纸", Description = "AutoCAD图纸文件", IsEnabled = true, IsDWG = true, IsGeneral = true, TreeNodeId = null },
            new() { Name = "模型", Description = "便携式文档格式", IsEnabled = true, IsDWG = true, IsGeneral = true, TreeNodeId = null },
            new() { Name = "送审数据", Description = "图像文件", IsEnabled = true, IsDWG = true, IsGeneral = true, TreeNodeId = null },
            new() { Name = "地质资料", Description = "文本文档文件", IsEnabled = true, IsDWG = true, IsGeneral = true, TreeNodeId = null },
            new() { Name = "结构计算书", Description = "压缩包文件", IsEnabled = true, IsDWG = true, IsGeneral = true, TreeNodeId = null },
            new() { Name = "施工资料", Description = "其他类型文件", IsEnabled = true, IsDWG = false, IsGeneral = true, TreeNodeId = null },
            new() { Name = "审图回复", Description = "其他类型文件", IsEnabled = true, IsDWG = false, IsGeneral = true, TreeNodeId = null },
            new() { Name = "甲方发来资料", Description = "其他类型文件", IsEnabled = true, IsDWG = false, IsGeneral = true, TreeNodeId = null },
            new() { Name = "通过审图文件", Description = "其他类型文件", IsEnabled = true, IsDWG = false, IsGeneral = true, TreeNodeId = null },
        };

        foreach (var item in defaultItems)
        {
            await YData.InsertAsync(item);
            _logger.Debug($"创建默认文件类型: {item.Name}");
        }

        _logger.Info($"初始数据创建完成，共创建 {defaultItems.Count} 个文件类型");
    }

    #endregion

    #region 查询操作

    /// <summary>
    /// 获取所有文件类型
    /// </summary>
    public async Task<List<FileTypeItemModel>> GetAllAsync()
    {
        _logger.Debug("获取所有文件类型");

        var items = await YData.Select<FileTypeItemModel>()
            .OrderBy(x => x.Id)
            .ToListAsync();

        _logger.Debug($"获取到 {items.Count} 个文件类型");
        return items;
    }

    /// <summary>
    /// 获取启用的文件类型
    /// </summary>
    public async Task<List<FileTypeItemModel>> GetEnabledAsync()
    {
        _logger.Debug("获取启用的文件类型");

        var items = await YData.Select<FileTypeItemModel>()
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.Id)
            .ToListAsync();

        _logger.Debug($"获取到 {items.Count} 个启用的文件类型");
        return items;
    }

    /// <summary>
    /// 根据ID获取文件类型
    /// </summary>
    public async Task<FileTypeItemModel?> GetByIdAsync(int id)
    {
        _logger.Debug($"获取文件类型，ID: {id}");

        var item = await YData.GetAsync<FileTypeItemModel>(id);

        if (item == null)
        {
            _logger.Warning($"未找到文件类型，ID: {id}");
        }

        return item;
    }

    /// <summary>
    /// 根据名称获取文件类型
    /// </summary>
    public async Task<FileTypeItemModel?> GetByNameAsync(string name)
    {
        _logger.Debug($"根据名称获取文件类型: {name}");

        var item = await YData.Select<FileTypeItemModel>()
            .Where(x => x.Name == name)
            .FirstAsync();

        if (item == null)
        {
            _logger.Warning($"未找到文件类型: {name}");
        }

        return item;
    }

    /// <summary>
    /// 搜索文件类型
    /// </summary>
    public async Task<List<FileTypeItemModel>> SearchAsync(string keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
        {
            return await GetAllAsync();
        }

        _logger.Debug($"搜索文件类型: {keyword}");

        var items = await YData.Select<FileTypeItemModel>()
            .Where(x => x.Name.Contains(keyword) || x.Description.Contains(keyword))
            .OrderBy(x => x.Id)
            .ToListAsync();

        _logger.Debug($"搜索到 {items.Count} 个匹配的文件类型");
        return items;
    }

    /// <summary>
    /// 获取通用文件类型（IsGeneral=true）
    /// </summary>
    public async Task<List<FileTypeItemModel>> GetGeneralTypesAsync()
    {
        _logger.Debug("获取通用文件类型");

        var items = await YData.Select<FileTypeItemModel>()
            .Where(x => x.IsGeneral == true)
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.Id)
            .ToListAsync();

        _logger.Debug($"获取到 {items.Count} 个通用文件类型");
        return items;
    }

    /// <summary>
    /// 根据TreeNodeId获取关联的文件类型
    /// </summary>
    public async Task<List<FileTypeItemModel>> GetByTreeNodeIdAsync(int treeNodeId)
    {
        _logger.Debug($"获取TreeNodeId关联的文件类型: {treeNodeId}");

        var items = await YData.Select<FileTypeItemModel>()
            .Where(x => x.TreeNodeId == treeNodeId)
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.Id)
            .ToListAsync();

        _logger.Debug($"获取到 {items.Count} 个关联的文件类型");
        return items;
    }

    /// <summary>
    /// 获取可用的文件类型（通用类型 + 指定TreeNode的专用类型）
    /// </summary>
    public async Task<List<FileTypeItemModel>> GetAvailableTypesAsync(int? treeNodeId = null)
    {
        _logger.Debug($"获取可用的文件类型，TreeNodeId: {treeNodeId}");

        var query = YData.Select<FileTypeItemModel>()
            .Where(x => x.IsEnabled);

        // 通用类型 + 指定TreeNode的专用类型
        if (treeNodeId.HasValue)
        {
            query = query.Where(x => x.IsGeneral == true || x.TreeNodeId == treeNodeId.Value);
        }
        else
        {
            // 只返回通用类型
            query = query.Where(x => x.IsGeneral == true);
        }

        var items = await query.OrderBy(x => x.Id).ToListAsync();

        _logger.Debug($"获取到 {items.Count} 个可用的文件类型");
        return items;
    }

    #endregion

    #region 添加操作

    /// <summary>
    /// 添加文件类型
    /// </summary>
    public async Task<FileTypeItemModel> AddAsync(FileTypeItemModel item)
    {
        _logger.Debug($"添加文件类型: {item.Name}");

        // 检查名称是否已存在
        var existing = await GetByNameAsync(item.Name);
        if (existing != null)
        {
            throw new InvalidOperationException($"文件类型名称已存在: {item.Name}");
        }

        // 处理关联逻辑
        if (item.IsGeneral)
        {
            // 通用类型不关联特定TreeNode
            item.TreeNodeId = null;
            _logger.Debug($"设置为通用类型: {item.Name}");
        }
        else if (item.TreeNodeId.HasValue)
        {
            // 验证TreeNodeId是否存在（这里可以添加验证逻辑）
            _logger.Debug($"关联到TreeNodeId: {item.TreeNodeId.Value}");
        }

        var insertedId = await YData.InsertAsync(item);
        item.Id = insertedId;

        _logger.Info($"文件类型添加成功: {item.Name}, ID: {insertedId}, 通用: {item.IsGeneral}, TreeNodeId: {item.TreeNodeId}");
        return item;
    }

    #endregion

    #region 修改操作

    /// <summary>
    /// 更新文件类型
    /// </summary>
    public async Task<bool> UpdateAsync(FileTypeItemModel item)
    {
        _logger.Debug($"更新文件类型: {item.Name}, ID: {item.Id}");

        // 检查是否存在
        var existing = await GetByIdAsync(item.Id);
        if (existing == null)
        {
            _logger.Warning($"要更新的文件类型不存在，ID: {item.Id}");
            return false;
        }

        // 检查名称冲突（排除自己）
        var nameConflict = await YData.Select<FileTypeItemModel>()
            .Where(x => x.Name == item.Name && x.Id != item.Id)
            .FirstAsync();

        if (nameConflict != null)
        {
            throw new InvalidOperationException($"文件类型名称已存在: {item.Name}");
        }

        // 处理关联逻辑
        if (item.IsGeneral)
        {
            // 通用类型不关联特定TreeNode
            item.TreeNodeId = null;
            _logger.Debug($"更新为通用类型: {item.Name}");
        }
        else if (item.TreeNodeId.HasValue)
        {
            // 验证TreeNodeId是否存在（这里可以添加验证逻辑）
            _logger.Debug($"更新关联到TreeNodeId: {item.TreeNodeId.Value}");
        }

        var affectedRows = await YData.UpdateAsync(item);
        var success = affectedRows > 0;

        if (success)
        {
            _logger.Info($"文件类型更新成功: {item.Name}, 通用: {item.IsGeneral}, TreeNodeId: {item.TreeNodeId}");
        }
        else
        {
            _logger.Warning($"文件类型更新失败: {item.Name}");
        }

        return success;
    }

    /// <summary>
    /// 切换启用状态
    /// </summary>
    public async Task<bool> ToggleEnabledAsync(int id)
    {
        _logger.Debug($"切换文件类型启用状态，ID: {id}");

        var item = await GetByIdAsync(id);
        if (item == null)
        {
            return false;
        }

        item.IsEnabled = !item.IsEnabled;
        return await UpdateAsync(item);
    }

    #endregion

    #region 删除操作

    /// <summary>
    /// 删除文件类型
    /// </summary>
    public async Task<bool> DeleteAsync(int id)
    {
        _logger.Debug($"删除文件类型，ID: {id}");

        var item = await GetByIdAsync(id);
        if (item == null)
        {
            _logger.Warning($"要删除的文件类型不存在，ID: {id}");
            return false;
        }

        var affectedRows = await YData.DeleteAsync<FileTypeItemModel>(id);
        var success = affectedRows > 0;

        if (success)
        {
            _logger.Info($"文件类型删除成功: {item.Name}");
        }
        else
        {
            _logger.Warning($"文件类型删除失败: {item.Name}");
        }

        return success;
    }

    /// <summary>
    /// 批量删除
    /// </summary>
    public async Task<int> DeleteBatchAsync(List<int> ids)
    {
        _logger.Debug($"批量删除文件类型，数量: {ids.Count}");

        var deletedCount = 0;
        foreach (var id in ids)
        {
            if (await DeleteAsync(id))
            {
                deletedCount++;
            }
        }

        _logger.Info($"批量删除完成，成功删除 {deletedCount}/{ids.Count} 个文件类型");
        return deletedCount;
    }

    #endregion

    #region 辅助功能

    /// <summary>
    /// 获取文件类型统计信息
    /// </summary>
    public async Task<(int Total, int Enabled, int Disabled)> GetStatisticsAsync()
    {
        _logger.Debug("获取文件类型统计信息");

        var all = await GetAllAsync();
        var total = all.Count;
        var enabled = all.Count(x => x.IsEnabled);
        var disabled = total - enabled;

        _logger.Debug($"统计信息 - 总数: {total}, 启用: {enabled}, 禁用: {disabled}");
        return (total, enabled, disabled);
    }

    /// <summary>
    /// 检查名称是否可用
    /// </summary>
    public async Task<bool> IsNameAvailableAsync(string name, int? excludeId = null)
    {
        _logger.Debug($"检查名称是否可用: {name}");

        var query = YData.Select<FileTypeItemModel>()
            .Where(x => x.Name == name);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var exists = await query.AnyAsync();
        return !exists;
    }

    /// <summary>
    /// 重置为默认数据
    /// </summary>
    public async Task ResetToDefaultAsync()
    {
        _logger.Debug("重置为默认数据");

        // 清空现有数据
        await YData.Delete<FileTypeItemModel>().ExecuteAffrowsAsync();

        // 重新创建默认数据
        await CreateInitialDataAsync();

        _logger.Info("重置为默认数据完成");
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    public async Task<List<FileTypeItemModel>> ExportDataAsync()
    {
        _logger.Debug("导出文件类型数据");
        return await GetAllAsync();
    }

    /// <summary>
    /// 导入数据
    /// </summary>
    public async Task<int> ImportDataAsync(List<FileTypeItemModel> items, bool clearExisting = false)
    {
        _logger.Debug($"导入文件类型数据，数量: {items.Count}, 清空现有: {clearExisting}");

        if (clearExisting)
        {
            await YData.Delete<FileTypeItemModel>().ExecuteAffrowsAsync();
        }

        var importedCount = 0;
        foreach (var item in items)
        {
            try
            {
                // 重置ID，让数据库自动分配
                item.Id = 0;

                // 检查名称冲突
                if (!await IsNameAvailableAsync(item.Name))
                {
                    _logger.Warning($"跳过重复名称: {item.Name}");
                    continue;
                }

                await YData.InsertAsync(item);
                importedCount++;
            }
            catch (Exception ex)
            {
                _logger.Error($"导入文件类型失败: {item.Name}, 错误: {ex.Message}");
            }
        }

        _logger.Info($"导入完成，成功导入 {importedCount}/{items.Count} 个文件类型");
        return importedCount;
    }

    #endregion
}
