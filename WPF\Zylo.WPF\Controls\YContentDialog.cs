using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace Zylo.WPF.Controls
{
    /// <summary>
    /// 封装的 WPF-UI ContentDialog，简化使用
    /// </summary>
    public static class YContentDialog
    {
        /// <summary>
        /// 显示输入对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="prompt">提示文本</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>输入结果 (success, inputText)</returns>
        public static async Task<(bool success, string inputText)> ShowInputAsync(string title, string prompt = "", string defaultValue = "")
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = title,
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            var stackPanel = new StackPanel();

            if (!string.IsNullOrEmpty(prompt))
            {
                stackPanel.Children.Add(new System.Windows.Controls.TextBlock 
                { 
                    Text = prompt, 
                    Margin = new Thickness(0, 0, 0, 8),
                    FontSize = 14
                });
            }

            var textBox = new Wpf.Ui.Controls.TextBox
            {
                Text = defaultValue,
                Margin = new Thickness(0, 0, 0, 8),
                MinWidth = 300
            };
            stackPanel.Children.Add(textBox);

            dialog.Content = stackPanel;

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();

            if (result == Wpf.Ui.Controls.ContentDialogResult.Primary)
            {
                return (true, textBox.Text?.Trim() ?? "");
            }

            return (false, "");
        }

        /// <summary>
        /// 显示重命名对话框
        /// </summary>
        /// <param name="currentName">当前名称（不含扩展名）</param>
        /// <param name="currentExtension">当前扩展名</param>
        /// <param name="isFile">是否为文件</param>
        /// <returns>重命名结果 (success, newName, newExtension)</returns>
        public static async Task<(bool success, string newName, string newExtension)> ShowRenameAsync(
            string currentName, string currentExtension, bool isFile)
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = "重命名",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            var stackPanel = new StackPanel();

            // 文件名输入
            stackPanel.Children.Add(new System.Windows.Controls.TextBlock 
            { 
                Text = "请输入新名称:", 
                Margin = new Thickness(0, 0, 0, 8),
                FontSize = 14
            });

            var nameTextBox = new Wpf.Ui.Controls.TextBox
            {
                Text = currentName,
                Margin = new Thickness(0, 0, 0, 8),
                MinWidth = 300
            };
            stackPanel.Children.Add(nameTextBox);

            // 如果是文件，添加扩展名选项
            Wpf.Ui.Controls.TextBox extensionTextBox = null;
            System.Windows.Controls.CheckBox changeExtensionCheckBox = null;

            if (isFile)
            {
                changeExtensionCheckBox = new System.Windows.Controls.CheckBox
                {
                    Content = "同时修改文件扩展名",
                    Margin = new Thickness(0, 8, 0, 8),
                    IsChecked = false
                };
                stackPanel.Children.Add(changeExtensionCheckBox);

                stackPanel.Children.Add(new System.Windows.Controls.TextBlock 
                { 
                    Text = "文件扩展名:", 
                    Margin = new Thickness(0, 8, 0, 4),
                    FontSize = 12
                });

                extensionTextBox = new Wpf.Ui.Controls.TextBox
                {
                    Text = currentExtension,
                    Margin = new Thickness(0, 0, 0, 8),
                    MinWidth = 300,
                    IsEnabled = false
                };
                stackPanel.Children.Add(extensionTextBox);

                // 绑定复选框事件
                changeExtensionCheckBox.Checked += (s, e) => extensionTextBox.IsEnabled = true;
                changeExtensionCheckBox.Unchecked += (s, e) =>
                {
                    extensionTextBox.IsEnabled = false;
                    extensionTextBox.Text = currentExtension;
                };
            }

            dialog.Content = stackPanel;

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();

            if (result == Wpf.Ui.Controls.ContentDialogResult.Primary)
            {
                var newName = nameTextBox.Text?.Trim();
                var newExtension = currentExtension;

                if (isFile && changeExtensionCheckBox?.IsChecked == true && extensionTextBox != null)
                {
                    var ext = extensionTextBox.Text?.Trim();
                    if (!string.IsNullOrEmpty(ext))
                    {
                        if (!ext.StartsWith("."))
                        {
                            ext = "." + ext;
                        }
                        newExtension = ext;
                    }
                }

                return (true, newName ?? "", newExtension);
            }

            return (false, "", "");
        }

        /// <summary>
        /// 显示内容对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="content">内容控件</param>
        /// <returns>对话框结果</returns>
        public static async Task<Wpf.Ui.Controls.ContentDialogResult> ShowContentAsync(string title, object content)
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = title,
                Content = content,
                PrimaryButtonText = "确定",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            SetupContentDialog(dialog);
            return await dialog.ShowAsync();
        }

        /// <summary>
        /// 显示名称和说明输入对话框（通用版本）
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="namePrompt">名称输入提示</param>
        /// <param name="descPrompt">说明输入提示</param>
        /// <param name="namePlaceholder">名称占位符</param>
        /// <param name="descPlaceholder">说明占位符</param>
        /// <param name="defaultName">默认名称</param>
        /// <param name="defaultDesc">默认说明</param>
        /// <returns>用户输入的名称和说明，如果取消则返回null</returns>
        public static async Task<(string Name, string Description)?> ShowNameDescInputDialogAsync(
            string title,
            string namePrompt,
            string descPrompt,
            string namePlaceholder = "",
            string descPlaceholder = "",
            string defaultName = "",
            string defaultDesc = "")
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = title,
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Primary
            };

            var stackPanel = new StackPanel();

            // 名称输入
            stackPanel.Children.Add(new TextBlock
            {
                Text = namePrompt,
                Margin = new Thickness(0, 0, 0, 8),
                FontSize = 14
            });

            var nameTextBox = new Wpf.Ui.Controls.TextBox
            {
                Text = defaultName,
                PlaceholderText = namePlaceholder,
                Margin = new Thickness(0, 0, 0, 16),
                MinWidth = 350
            };
            stackPanel.Children.Add(nameTextBox);

            // 说明输入
            stackPanel.Children.Add(new TextBlock
            {
                Text = descPrompt,
                Margin = new Thickness(0, 0, 0, 8),
                FontSize = 14
            });

            var descTextBox = new Wpf.Ui.Controls.TextBox
            {
                Text = defaultDesc,
                PlaceholderText = descPlaceholder,
                Margin = new Thickness(0, 0, 0, 8),
                MinWidth = 350,
                Height = 60,
                TextWrapping = TextWrapping.Wrap,
                AcceptsReturn = true,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto
            };
            stackPanel.Children.Add(descTextBox);

            dialog.Content = stackPanel;

            // 设置焦点到名称输入框
            nameTextBox.Loaded += (s, e) => nameTextBox.Focus();

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();

            if (result == Wpf.Ui.Controls.ContentDialogResult.Primary)
            {
                var name = nameTextBox.Text?.Trim() ?? "";
                var description = descTextBox.Text?.Trim() ?? "";
                return (name, description);
            }

            return null;
        }

        /// <summary>
        /// 显示年份输入对话框（专用于DWG管理）
        /// </summary>
        /// <param name="existingYears">已存在的年份列表</param>
        /// <param name="defaultYear">默认年份</param>
        /// <param name="defaultDesc">默认说明</param>
        /// <returns>用户输入的年份信息，如果取消或无效则返回null</returns>
        public static async Task<(string Name, string Description)?> ShowYearInputDialogAsync(
            IEnumerable<string> existingYears,
            string? defaultYear = null,
            string? defaultDesc = null)
        {
            defaultYear ??= (DateTime.Now.Year + 1).ToString();
            defaultDesc ??= $"{defaultYear}年度项目规划";

            while (true)
            {
                var result = await ShowNameDescInputDialogAsync(
                    "📂 添加新年份",
                    "请输入年份（1900-2100）：",
                    "请输入年份说明：",
                    "例如：2026",
                    "例如：2026年度项目规划",
                    defaultYear,
                    defaultDesc
                );

                if (result == null)
                    return null; // 用户取消

                var (name, description) = result.Value;

                // 验证年份
                if (string.IsNullOrWhiteSpace(name))
                {
                    await ShowErrorDialogAsync("输入错误", "年份不能为空！");
                    continue;
                }

                if (!int.TryParse(name.Trim(), out var year) || year < 1900 || year > 2100)
                {
                    await ShowErrorDialogAsync("输入错误", "请输入有效的年份（1900-2100）！");
                    defaultYear = name; // 保持用户输入
                    defaultDesc = description;
                    continue;
                }

                var yearStr = year.ToString();

                // 检查重复
                if (existingYears.Contains(yearStr))
                {
                    await ShowErrorDialogAsync("重复年份", $"年份 {yearStr} 已存在，请输入其他年份！");
                    defaultYear = yearStr; // 保持用户输入，方便修改
                    defaultDesc = description;
                    continue;
                }

                return (yearStr, description);
            }
        }

        /// <summary>
        /// 显示项目名称输入对话框
        /// </summary>
        /// <param name="yearName">所属年份</param>
        /// <param name="existingProjects">已存在的项目名称列表</param>
        /// <param name="defaultName">默认项目名称</param>
        /// <param name="defaultDesc">默认项目说明</param>
        /// <returns>用户输入的项目信息，如果取消或无效则返回null</returns>
        public static async Task<(string Name, string Description)?> ShowProjectInputDialogAsync(
            string yearName,
            IEnumerable<string> existingProjects,
            string? defaultName = null,
            string? defaultDesc = null)
        {
            defaultName ??= $"新项目{DateTime.Now:MMdd}";
            defaultDesc ??= "住宅项目";

            while (true)
            {
                var result = await ShowNameDescInputDialogAsync(
                    $"🏢 为 {yearName} 年添加项目",
                    "请输入项目名称：",
                    "请输入项目说明：",
                    "例如：万科城市花园",
                    "例如：高端住宅项目，总建筑面积50万平方米",
                    defaultName,
                    defaultDesc
                );

                if (result == null)
                    return null; // 用户取消

                var (projectName, description) = result.Value;

                if (string.IsNullOrWhiteSpace(projectName))
                {
                    await ShowErrorDialogAsync("输入错误", "项目名称不能为空！");
                    defaultName = projectName;
                    defaultDesc = description;
                    continue;
                }

                // 检查重复
                if (existingProjects.Contains(projectName))
                {
                    await ShowErrorDialogAsync("重复项目", $"项目 {projectName} 已存在，请输入其他名称！");
                    defaultName = projectName; // 保持用户输入，方便修改
                    defaultDesc = description;
                    continue;
                }

                return (projectName, description);
            }
        }

        /// <summary>
        /// 显示栋号输入对话框
        /// </summary>
        /// <param name="projectName">所属项目</param>
        /// <param name="existingBuildings">已存在的栋号列表</param>
        /// <param name="defaultName">默认栋号</param>
        /// <param name="defaultDesc">默认栋号说明</param>
        /// <returns>用户输入的栋号信息，如果取消或无效则返回null</returns>
        public static async Task<(string Name, string Description)?> ShowBuildingInputDialogAsync(
            string projectName,
            IEnumerable<string> existingBuildings,
            string? defaultName = null,
            string? defaultDesc = null)
        {
            var buildingCount = existingBuildings.Count() + 1;
            defaultName ??= $"{buildingCount}#楼";
            defaultDesc ??= "住宅建筑";

            while (true)
            {
                var result = await ShowNameDescInputDialogAsync(
                    $"🏗️ 为项目 {projectName} 添加栋号",
                    "请输入栋号名称：",
                    "请输入栋号说明：",
                    "例如：1#楼、A栋、商业楼",
                    "例如：高层住宅，32层，户型90-150平方米",
                    defaultName,
                    defaultDesc
                );

                if (result == null)
                    return null; // 用户取消

                var (buildingName, description) = result.Value;

                if (string.IsNullOrWhiteSpace(buildingName))
                {
                    await ShowErrorDialogAsync("输入错误", "栋号名称不能为空！");
                    defaultName = buildingName;
                    defaultDesc = description;
                    continue;
                }

                // 检查重复
                if (existingBuildings.Contains(buildingName))
                {
                    await ShowErrorDialogAsync("重复栋号", $"栋号 {buildingName} 已存在，请输入其他名称！");
                    defaultName = buildingName; // 保持用户输入，方便修改
                    defaultDesc = description;
                    continue;
                }

                return (buildingName, description);
            }
        }

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="message">错误信息</param>
        /// <returns>Task</returns>
        public static async Task ShowErrorDialogAsync(string title, string message)
        {
            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = $"❌ {title}",
                Content = new TextBlock
                {
                    Text = message,
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 400,
                    Foreground = System.Windows.Media.Brushes.Red,
                    Margin = new Thickness(0, 8, 0, 8)
                },
                PrimaryButtonText = "确定"
            };

            SetupContentDialog(dialog);
            await dialog.ShowAsync();
        }

        /// <summary>
        /// 显示确认删除对话框
        /// </summary>
        /// <param name="itemName">要删除的项目名称</param>
        /// <param name="itemType">项目类型（年份、项目、栋号）</param>
        /// <param name="childCount">子项目数量</param>
        /// <returns>用户是否确认删除</returns>
        public static async Task<bool> ShowDeleteConfirmDialogAsync(string itemName, string itemType, int childCount = 0)
        {
            var message = childCount > 0
                ? $"确定要删除{itemType} \"{itemName}\" 吗？\n\n⚠️ 这将同时删除其下的 {childCount} 个子项目！"
                : $"确定要删除{itemType} \"{itemName}\" 吗？";

            var dialog = new Wpf.Ui.Controls.ContentDialog
            {
                Title = $"🗑️ 删除{itemType}",
                Content = new TextBlock
                {
                    Text = message,
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 400,
                    Margin = new Thickness(0, 8, 0, 8)
                },
                PrimaryButtonText = "删除",
                CloseButtonText = "取消",
                DefaultButton = Wpf.Ui.Controls.ContentDialogButton.Close
            };

            SetupContentDialog(dialog);
            var result = await dialog.ShowAsync();
            return result == Wpf.Ui.Controls.ContentDialogResult.Primary;
        }

        /// <summary>
        /// 设置 ContentDialog 的 DialogHost
        /// </summary>
        private static void SetupContentDialog(Wpf.Ui.Controls.ContentDialog dialog)
        {
            if (Application.Current.MainWindow != null)
            {
                var contentPresenter = Application.Current.MainWindow.FindName("RootContentDialogPresenter") as ContentPresenter;
                if (contentPresenter != null)
                {
#pragma warning disable CS0618 // 类型或成员已过时
                    dialog.DialogHost = contentPresenter;
#pragma warning restore CS0618 // 类型或成员已过时
                }
            }
        }
    }
}
