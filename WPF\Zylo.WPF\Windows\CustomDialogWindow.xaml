<ui:FluentWindow
    Height="500"
    MinHeight="400"
    MinWidth="500"
    ResizeMode="CanResize"
    ShowInTaskbar="False"
    Title="{Binding Title}"
    Width="600"
    WindowStartupLocation="CenterOwner"
    prism:Dialog.WindowStartupLocation="CenterOwner"
    x:Class="Zylo.WPF.Windows.CustomDialogWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:prism="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--  使用 WPF-UI 的现代化样式  -->
    <Grid Margin="0">
        <!--  内容区域  -->
        <ContentPresenter Content="{Binding}" />
    </Grid>
</ui:FluentWindow>
