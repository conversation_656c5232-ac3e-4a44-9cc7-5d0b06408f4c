using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AlphaPM.Models.Per;

/// <summary>
/// DWG项目人员模型
/// 统一管理项目中的所有人员信息，避免重复输入
/// </summary>
/// <remarks>
/// 🎯 设计目标：
/// - 统一管理项目人员信息
/// - 支持不同角色和职位
/// - 避免重复输入人员信息
/// - 支持人员信息的复用
/// 
/// 👥 人员角色：
/// - 年度负责人（总监级别）
/// - 项目经理
/// - 建筑负责人
/// - 设计人员
/// - 其他角色
/// 
/// 💡 使用方式：
/// ```csharp
/// // 创建人员
/// var personnel = new Per
/// {
///     Name = "张工程师",
///     Position = "高级建筑师",
///     Department = "设计部",
///     Phone = "138****8888",
///     Email = "<EMAIL>"
/// };
/// 
/// // 在业务数据中引用
/// dwgData.ManagerId = personnel.Id;
/// dwgData.DesignerId = personnel.Id;
/// ```
/// </remarks>
[Table("Per")]
public partial class Personnel : ObservableValidator
{
    #region 基本信息

    /// <summary>
    /// 人员ID
    /// </summary>
    [Key]
    [ObservableProperty]
    public partial int Id { get; set; }

    /// <summary>
    /// 所属项目ID - 人员归属于具体项目
    /// </summary>
    [ObservableProperty]
    [Description("所属项目ID")]
    public partial int? ProjectId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    [Required(ErrorMessage = "姓名不能为空")]
    [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
    [ObservableProperty]
    [Description("姓名")]
    public partial string Name { get; set; } = string.Empty;

    /// <summary>
    /// 工号/员工编号
    /// </summary>
    [StringLength(20)]
    [ObservableProperty]
    [Description("工号")]
    public partial string EmployeeNumber { get; set; } = string.Empty;

    /// <summary>
    /// 职位
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("职位")]
    public partial string Position { get; set; } = string.Empty;

    /// <summary>
    /// 部门
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("部门")]
    public partial string Department { get; set; } = string.Empty;

    #endregion

    #region 联系信息

    /// <summary>
    /// 手机号码
    /// </summary>
    [StringLength(20)]
    [Phone(ErrorMessage = "手机号码格式不正确")]
    [ObservableProperty]
    [Description("手机号码")]
    public partial string Phone { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    [StringLength(100)]
    [EmailAddress(ErrorMessage = "邮箱地址格式不正确")]
    [ObservableProperty]
    [Description("邮箱地址")]
    public partial string Email { get; set; } = string.Empty;

    /// <summary>
    /// 办公电话
    /// </summary>
    [StringLength(20)]
    [ObservableProperty]
    [Description("办公电话")]
    public partial string OfficePhone { get; set; } = string.Empty;

    /// <summary>
    /// 办公地址
    /// </summary>
    [StringLength(200)]
    [ObservableProperty]
    [Description("办公地址")]
    public partial string OfficeAddress { get; set; } = string.Empty;

    #endregion

    #region 专业信息

    /// <summary>
    /// 专业领域
    /// 如：建筑设计、结构设计、机电设计、项目管理等
    /// </summary>
    [StringLength(100)]
    [ObservableProperty]
    [Description("专业领域")]
    public partial string Specialty { get; set; } = string.Empty;

    /// <summary>
    /// 职业资格
    /// 如：一级建筑师、一级建造师、高级工程师等
    /// </summary>
    [StringLength(200)]
    [ObservableProperty]
    [Description("职业资格")]
    public partial string Qualifications { get; set; } = string.Empty;

    /// <summary>
    /// 工作年限
    /// </summary>
    [Range(0, 50)]
    [ObservableProperty]
    [Description("工作年限")]
    public partial int WorkYears { get; set; } = 0;

    #endregion

    #region 权限管理

    /// <summary>
    /// 用户角色
    /// 如：超级管理员、项目总监、项目经理、设计师、普通用户等
    /// </summary>
    [StringLength(50)]
    [ObservableProperty]
    [Description("用户角色")]
    public partial string UserRole { get; set; } = string.Empty;

    /// <summary>
    /// 权限级别
    /// 1=超级管理员, 2=总监级, 3=经理级, 4=设计师级, 5=普通用户
    /// </summary>
    [Range(1, 10)]
    [ObservableProperty]
    [Description("权限级别")]
    public partial int PermissionLevel { get; set; } = 5;

    /// <summary>
    /// 可访问的功能模块
    /// 用逗号分隔的功能模块代码，如："DWG_MANAGE,PROJECT_CREATE,DESIGN_EDIT"
    /// </summary>
    [StringLength(500)]
    [ObservableProperty]
    [Description("可访问的功能模块")]
    public partial string AccessibleModules { get; set; } = string.Empty;

    /// <summary>
    /// 数据权限范围
    /// ALL=全部数据, DEPARTMENT=本部门数据, SELF=个人数据
    /// </summary>
    [StringLength(20)]
    [ObservableProperty]
    [Description("数据权限范围")]
    public partial string DataScope { get; set; } = "SELF";

    /// <summary>
    /// 是否为系统管理员
    /// </summary>
    [ObservableProperty]
    [Description("是否为系统管理员")]
    public partial bool IsSystemAdmin { get; set; } = false;

    #endregion

    #region 状态信息

    /// <summary>
    /// 是否在职
    /// </summary>
    [ObservableProperty]
    [Description("是否在职")]
    public partial bool IsActive { get; set; } = true;

    /// <summary>
    /// 入职时间
    /// </summary>
    [ObservableProperty]
    [Description("入职时间")]
    public partial DateTime? HireDate { get; set; }

    /// <summary>
    /// 离职时间
    /// </summary>
    [ObservableProperty]
    [Description("离职时间")]
    public partial DateTime? LeaveDate { get; set; }

    #endregion

    #region 扩展信息

    /// <summary>
    /// 备注信息
    /// </summary>
    [StringLength(500)]
    [ObservableProperty]
    [Description("备注信息")]
    public partial string Remarks { get; set; } = string.Empty;

    /// <summary>
    /// 头像路径
    /// </summary>
    [StringLength(200)]
    [ObservableProperty]
    [Description("头像路径")]
    public partial string AvatarPath { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [ObservableProperty]
    public partial DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改时间
    /// </summary>
    [ObservableProperty]
    public partial DateTime ModifiedAt { get; set; } = DateTime.Now;

    #endregion

    #region 计算属性

    /// <summary>
    /// 显示名称（姓名 + 职位）
    /// </summary>
    public string DisplayName
    {
        get
        {
            if (string.IsNullOrEmpty(Position))
                return Name;
            return $"{Name}({Position})";
        }
    }

    /// <summary>
    /// 完整联系信息
    /// </summary>
    public string ContactInfo
    {
        get
        {
            var contacts = new List<string>();
            if (!string.IsNullOrEmpty(Phone)) contacts.Add($"手机: {Phone}");
            if (!string.IsNullOrEmpty(Email)) contacts.Add($"邮箱: {Email}");
            if (!string.IsNullOrEmpty(OfficePhone)) contacts.Add($"办公: {OfficePhone}");
            return string.Join(" | ", contacts);
        }
    }

    /// <summary>
    /// 专业描述
    /// </summary>
    public string ProfessionalDescription
    {
        get
        {
            var parts = new List<string>();
            if (!string.IsNullOrEmpty(Specialty)) parts.Add(Specialty);
            if (WorkYears > 0) parts.Add($"{WorkYears}年经验");
            if (!string.IsNullOrEmpty(Qualifications)) parts.Add(Qualifications);
            return string.Join(" | ", parts);
        }
    }

    #endregion

    #region 权限检查方法

    /// <summary>
    /// 检查是否有指定模块的访问权限
    /// </summary>
    /// <param name="moduleCode">模块代码</param>
    /// <returns>是否有权限</returns>
    public bool HasModuleAccess(string moduleCode)
    {
        if (IsSystemAdmin) return true;
        if (string.IsNullOrEmpty(AccessibleModules)) return false;

        var modules = AccessibleModules.Split(',', StringSplitOptions.RemoveEmptyEntries);
        return modules.Contains(moduleCode, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 检查权限级别是否满足要求
    /// </summary>
    /// <param name="requiredLevel">要求的权限级别</param>
    /// <returns>是否满足权限要求</returns>
    public bool HasPermissionLevel(int requiredLevel)
    {
        if (IsSystemAdmin) return true;
        return PermissionLevel <= requiredLevel; // 数字越小权限越高
    }

    /// <summary>
    /// 检查数据访问权限
    /// </summary>
    /// <param name="targetDataScope">目标数据范围</param>
    /// <returns>是否有访问权限</returns>
    public bool HasDataAccess(string targetDataScope)
    {
        if (IsSystemAdmin) return true;

        return DataScope switch
        {
            "ALL" => true,
            "DEPARTMENT" => targetDataScope == "DEPARTMENT" || targetDataScope == "SELF",
            "SELF" => targetDataScope == "SELF",
            _ => false
        };
    }

    /// <summary>
    /// 获取可访问的模块列表
    /// </summary>
    /// <returns>模块代码列表</returns>
    public string[] GetAccessibleModuleList()
    {
        if (string.IsNullOrEmpty(AccessibleModules))
            return Array.Empty<string>();

        return AccessibleModules.Split(',', StringSplitOptions.RemoveEmptyEntries)
                               .Select(m => m.Trim())
                               .ToArray();
    }

    /// <summary>
    /// 添加模块访问权限
    /// </summary>
    /// <param name="moduleCode">模块代码</param>
    public void AddModuleAccess(string moduleCode)
    {
        if (HasModuleAccess(moduleCode)) return;

        var modules = GetAccessibleModuleList().ToList();
        modules.Add(moduleCode);
        AccessibleModules = string.Join(",", modules);
    }

    /// <summary>
    /// 移除模块访问权限
    /// </summary>
    /// <param name="moduleCode">模块代码</param>
    public void RemoveModuleAccess(string moduleCode)
    {
        var modules = GetAccessibleModuleList().Where(m =>
            !string.Equals(m, moduleCode, StringComparison.OrdinalIgnoreCase)).ToArray();
        AccessibleModules = string.Join(",", modules);
    }

    #endregion

    #region 重写方法

    /// <summary>
    /// 转换为字符串
    /// </summary>
    public override string ToString()
    {
        return DisplayName;
    }

    #endregion

    #region 静态工厂方法

    /// <summary>
    /// 创建项目经理
    /// </summary>
    public static Personnel CreateProjectManager(string name, string phone = "", string email = "")
    {
        return new Personnel
        {
            Name = name,
            Position = "项目经理",
            Phone = phone,
            Email = email,
            Specialty = "项目管理",
            UserRole = Permissions.ManagerTemplate.Role,
            PermissionLevel = Permissions.ManagerTemplate.Level,
            AccessibleModules = Permissions.ManagerTemplate.GetModulesString(),
            DataScope = Permissions.ManagerTemplate.DataScope,
            IsSystemAdmin = Permissions.ManagerTemplate.IsSystemAdmin
        };
    }

    /// <summary>
    /// 创建设计人员
    /// </summary>
    public static Personnel CreateDesigner(string name, string specialty, string phone = "", string email = "")
    {
        return new Personnel
        {
            Name = name,
            Position = "设计师",
            Phone = phone,
            Email = email,
            Specialty = specialty,
            UserRole = Permissions.DesignerTemplate.Role,
            PermissionLevel = Permissions.DesignerTemplate.Level,
            AccessibleModules = Permissions.DesignerTemplate.GetModulesString(),
            DataScope = Permissions.DesignerTemplate.DataScope,
            IsSystemAdmin = Permissions.DesignerTemplate.IsSystemAdmin
        };
    }

    /// <summary>
    /// 创建总监
    /// </summary>
    public static Personnel CreateSupervisor(string name, string phone = "", string email = "")
    {
        return new Personnel
        {
            Name = name,
            Position = "总监",
            Phone = phone,
            Email = email,
            Specialty = "项目管理",
            UserRole = Permissions.SupervisorTemplate.Role,
            PermissionLevel = Permissions.SupervisorTemplate.Level,
            AccessibleModules = Permissions.SupervisorTemplate.GetModulesString(),
            DataScope = Permissions.SupervisorTemplate.DataScope,
            IsSystemAdmin = Permissions.SupervisorTemplate.IsSystemAdmin
        };
    }

    /// <summary>
    /// 创建系统管理员
    /// </summary>
    public static Personnel CreateSystemAdmin(string name, string phone = "", string email = "")
    {
        return new Personnel
        {
            Name = name,
            Position = "系统管理员",
            Phone = phone,
            Email = email,
            Specialty = "系统管理",
            UserRole = Permissions.SuperAdminTemplate.Role,
            PermissionLevel = Permissions.SuperAdminTemplate.Level,
            AccessibleModules = Permissions.SuperAdminTemplate.GetModulesString(),
            DataScope = Permissions.SuperAdminTemplate.DataScope,
            IsSystemAdmin = Permissions.SuperAdminTemplate.IsSystemAdmin
        };
    }

    /// <summary>
    /// 创建软件管理员
    /// </summary>
    public static Personnel CreateSoftwareAdmin(string name, string phone = "", string email = "")
    {
        return new Personnel
        {
            Name = name,
            Position = "软件管理员",
            Phone = phone,
            Email = email,
            Specialty = "软件管理",
            UserRole = Permissions.SoftwareAdminTemplate.Role,
            PermissionLevel = Permissions.SoftwareAdminTemplate.Level,
            AccessibleModules = Permissions.SoftwareAdminTemplate.GetModulesString(),
            DataScope = Permissions.SoftwareAdminTemplate.DataScope,
            IsSystemAdmin = Permissions.SoftwareAdminTemplate.IsSystemAdmin
        };
    }

    /// <summary>
    /// 创建普通用户
    /// </summary>
    public static Personnel CreateUser(string name, string phone = "", string email = "")
    {
        return new Personnel
        {
            Name = name,
            Position = "普通用户",
            Phone = phone,
            Email = email,
            UserRole = Permissions.UserTemplate.Role,
            PermissionLevel = Permissions.UserTemplate.Level,
            AccessibleModules = Permissions.UserTemplate.GetModulesString(),
            DataScope = Permissions.UserTemplate.DataScope,
            IsSystemAdmin = Permissions.UserTemplate.IsSystemAdmin
        };
    }

    #endregion
}
