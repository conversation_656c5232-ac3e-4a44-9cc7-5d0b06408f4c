﻿using System.Configuration;
using System.Data;
using System.Windows;

namespace AlphaPM;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    public readonly YLoggerInstance _logger = YLogger.ForSilent<App>();
    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 🎯 使用 Zylo.WPF 扩展方法，一行代码搞定！
            this.UseZyloMvvm<AppBootstrapper>();
        }
        catch (Exception ex)
        {
            _logger.Error($"❌ AlphaPM 应用程序启动失败: {ex.Message}");

            Shutdown(1);
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        base.OnExit(e);
    }
}