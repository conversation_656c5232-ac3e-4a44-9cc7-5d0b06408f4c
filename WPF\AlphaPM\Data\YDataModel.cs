using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using AlphaPM.Services.Window;
using CommunityToolkit.Mvvm.ComponentModel;
using Zylo.YIO.Core;
using Zylo.YIO.Extensions;

namespace AlphaPM.Data
{
    /// <summary>
    /// 软件级参考保存模型
    /// 包含项目路径、模型路径等配置信息
    /// 使用 CommunityToolkit.Mvvm 的 ObservableObject 基类
    /// 支持 WPF 数据绑定和 JSON 序列化
    ///
    /// 提供应用级单例访问：YDataModel.Current
    /// </summary>
    public partial class YDataModel : ObservableObject
    {
        #region q

        
        /// <summary>
        /// DWG数据
        /// </summary>
        [ObservableProperty]
        [Description("DWG数据")]
        public partial YDWGData YdwgData { get; set; } =new ();
        

        #endregion
        
        private static  string _DefauntappPath = Path.Combine(YCommonPaths.ApplicationDataDirectory,"Json");
        #region 应用级单例

        /// <summary>
        /// 应用级单例实例
        /// </summary>
        private static YDataModel? _current;

        /// <summary>
        /// 单例锁
        /// </summary>
        private static readonly object _lock = new();

        /// <summary>
        /// 获取应用级单例实例
        /// </summary>
        public static YDataModel Current
        {
            get
            {
                if (_current == null)
                {
                    lock (_lock)
                    {
                        _current ??= new YDataModel();
                    }
                }

                return _current;
            }
        }

        /// <summary>
        /// 初始化应用级单例（从文件加载）
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>是否初始化成功</returns>
        public static async Task<bool> InitializeAsync(string filePath = "AppConfig.json")
        {
            // 电脑 确保目录存在
            if (!YDirectory.DirectoryExists(AppPath))
            {
                YDirectory.CreateDirectory(AppPath);
            }

            //软件下数据目录
            YDirectory.CreateDirectory(_DefauntappPath);

            try
            {
                lock (_lock)
                {
                    _current = null; // 重置单例
                }

                var loaded = await Load(filePath);

                lock (_lock)
                {
                    _current = loaded;
                }

                return true;
            }
            catch
            {
                lock (_lock)
                {
                    _current = new YDataModel();
                }

                return false;
            }
        }

        #endregion


        #region 通用设置存储

        /// <summary>
        /// 通用设置字典，用于存储各种配置项
        /// </summary>
        [JsonPropertyName("Settings")]
        public Dictionary<string, object> Settings { get; set; } = new();

        /// <summary>
        /// 设置配置项
        /// </summary>
        /// <typeparam name="T">配置项类型</typeparam>
        /// <param name="key">配置项键</param>
        /// <param name="value">配置项值</param>
        public void SetSetting<T>(string key, T value)
        {
            if (value != null)
            {
                Settings[key] = value;
            }
            else
            {
                Settings.Remove(key);
            }
        }

        /// <summary>
        /// 获取配置项
        /// </summary>
        /// <typeparam name="T">配置项类型</typeparam>
        /// <param name="key">配置项键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置项值</returns>
        public T GetSetting<T>(string key, T defaultValue = default!)
        {
            if (!Settings.TryGetValue(key, out var value))
                return defaultValue;

            try
            {
                // 处理 JsonElement 类型（从 JSON 反序列化时）
                if (value is JsonElement jsonElement)
                {
                    return JsonSerializer.Deserialize<T>(jsonElement.GetRawText()) ?? defaultValue;
                }

                // 直接类型转换
                if (value is T directValue)
                    return directValue;

                // 尝试转换
                return (T)Convert.ChangeType(value, typeof(T)) ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        #endregion



        #region 便捷方法

        /// <summary>
        /// 添加自定义路径
        /// </summary>
        /// <param name="key">路径键</param>
        /// <param name="path">路径值</param>
        public void AddCustomPath(string key, string path)
        {
            SetSetting($"CustomPath_{key}", path);
        }

        /// <summary>
        /// 获取自定义路径
        /// </summary>
        /// <param name="key">路径键</param>
        /// <returns>路径值</returns>
        public string GetCustomPath(string key)
        {
            return GetSetting($"CustomPath_{key}", string.Empty);
        }



        /// <summary>
        /// 版本信息
        /// </summary>
        public string Version
        {
            get => GetSetting("Version", "1.0.0");
            set => SetSetting("Version", value);
        }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified
        {
            get => GetSetting("LastModified", DateTime.Now);
            set => SetSetting("LastModified", value);
        }

        /// <summary>
        /// 克隆当前模型
        /// </summary>
        /// <returns>克隆的模型</returns>
        public YDataModel Clone()
        {
            var json = JsonSerializer.Serialize(this);
            return JsonSerializer.Deserialize<YDataModel>(json) ?? new YDataModel();
        }

        /// <summary>
        /// 保存到 JSON 文件 - 简单方法
        /// </summary>
        /// <param name="filePath">文件路径，默认为 "YDataModel.json"</param>
        /// <returns>是否保存成功</returns>
        public async Task<bool> Save(string filePath = "AppConfig.json")
        {
            filePath = Path.Combine(AppPath, filePath);

            return _current.YSave(filePath);

          
        }

        private static string AppPath =>
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "AlphaPM");

        private static string AppJsonName = "AppConfig.json";


        /// <summary>
        /// 从 JSON 文件加载 - 简单方法
        /// </summary>
        /// <param name="filePath">文件路径，默认为 "YDataModel.json"</param>
        /// <returns>加载的模型，失败时返回新实例</returns>
        public static async Task<YDataModel> Load(string filePath = "AppConfig.json")
        {
            filePath = Path.Combine(AppPath, filePath);
            return _current.YLoad(filePath);
          
        }

        #endregion
    }
}