using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using AlphaPM.Data;
using AlphaPM.Services.Window;

// TODO: 添加 YConfigHelper 引用
// using Zylo.YData.Helpers;

namespace AlphaPM.Services
{
    /// <summary>
    /// 参考保存服务
    /// 管理软件级参考保存模型的持久化和操作
    /// </summary>
    public class YDataService
    {
        private const string DefaultConfigFileName = "reference_config.json";
        private readonly string _configDirectory;
        private readonly string _configFilePath;

        public YDataService(string? configDirectory = null)
        {
            _configDirectory = configDirectory ?? 
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "AlphaPM");
            _configFilePath = Path.Combine(_configDirectory, DefaultConfigFileName);
            
            EnsureConfigDirectoryExists();
        }

        #region 配置文件操作

        /// <summary>
        /// 保存参考模型到配置文件
        /// </summary>
        /// <param name="model">参考模型</param>
        /// <returns>操作结果</returns>
        public async Task<OperationResult> SaveReferenceModelAsync(YDataModel model)
        {
            try
            {
                // 添加最后修改时间到设置中
                model.SetSetting("LastModified", DateTime.Now);
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(model, options);
                await File.WriteAllTextAsync(_configFilePath, json);

                return new OperationResult { Success = true };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = $"保存配置失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 从配置文件加载参考模型
        /// </summary>
        /// <returns>参考模型和操作结果</returns>
        public async Task<(YDataModel? Model, OperationResult Result)> LoadReferenceModelAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    var defaultModel = new YDataModel();
                    return (defaultModel, new OperationResult 
                    { 
                        Success = true, 
                        Message = "使用默认配置" 
                    });
                }

                var json = await File.ReadAllTextAsync(_configFilePath);
                var model = JsonSerializer.Deserialize<YDataModel>(json);

                if (model == null)
                {
                    return (new YDataModel(), new OperationResult
                    {
                        Success = false,
                        ErrorMessage = "配置文件格式错误"
                    });
                }

                return (model, new OperationResult { Success = true });
            }
            catch (Exception ex)
            {
                return (new YDataModel(), new OperationResult
                {
                    Success = false,
                    ErrorMessage = $"加载配置失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 备份当前配置
        /// </summary>
        /// <returns>操作结果</returns>
        public async Task<OperationResult> BackupConfigAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    return new OperationResult
                    {
                        Success = false,
                        ErrorMessage = "配置文件不存在"
                    };
                }

                var backupFileName = $"reference_config_backup_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var backupPath = Path.Combine(_configDirectory, "Backups", backupFileName);
                
                var backupDir = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDir) && !Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                File.Copy(_configFilePath, backupPath, true);

                return new OperationResult 
                { 
                    Success = true,
                    Message = $"配置已备份到: {backupPath}"
                };
            }
            catch (Exception ex)
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = $"备份失败: {ex.Message}"
                };
            }
        }

        #endregion

        #region 文件操作集成

        /// <summary>
        /// 选择项目路径
        /// </summary>
        /// <param name="currentModel">当前模型</param>
        /// <returns>更新后的模型和操作结果</returns>
        public (YDataModel Model, OperationResult Result) SelectProjectPath(YDataModel currentModel)
        {
            var result = WindowsFileOperationService.SelectFolderDialog(
                "选择项目路径",
                currentModel.YdwgData.ProjectPath);

            if (result.Success)
            {
                currentModel.YdwgData.ProjectPath = result.SelectedPath;
                return (currentModel, new OperationResult { Success = true });
            }

            return (currentModel, new OperationResult
            {
                Success = false,
                ErrorMessage = result.ErrorMessage ?? "用户取消选择"
            });
        }

        /// <summary>
        /// 选择模型文件
        /// </summary>
        /// <param name="currentModel">当前模型</param>
        /// <returns>更新后的模型和操作结果</returns>
        public (YDataModel Model, OperationResult Result) SelectModelFile(YDataModel yDataModel)
        {

            var currentModel = yDataModel.YdwgData;
            var filter = "模型文件|*.dwg;*.dxf;*.3dm;*.step;*.iges|" +
                        "AutoCAD文件|*.dwg;*.dxf|" +
                        "Rhino文件|*.3dm|" +
                        "STEP文件|*.step;*.stp|" +
                        "IGES文件|*.iges;*.igs|" +
                        "所有文件|*.*";

            var initialDir = !string.IsNullOrEmpty(currentModel.ModelPath) 
                ? Path.GetDirectoryName(currentModel.ModelPath) 
                : currentModel.ProjectPath;

            var result = WindowsFileOperationService.OpenFileDialog(
                "选择模型文件",
                filter,
                initialDir);

            if (result.Success)
            {
                currentModel.ModelPath = result.SelectedFile;
                return (yDataModel, new OperationResult { Success = true });
            }

            return (yDataModel, new OperationResult
            {
                Success = false,
                ErrorMessage = result.ErrorMessage ?? "用户取消选择"
            });
        }

        /// <summary>
        /// 在资源管理器中打开项目路径
        /// </summary>
        /// <param name="model">参考模型</param>
        /// <returns>操作结果</returns>
        public OperationResult OpenProjectInExplorer(YDataModel yDataModel)
        {
            var model = yDataModel.YdwgData;
            if (string.IsNullOrEmpty(model.ProjectPath))
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = "项目路径未设置"
                };
            }

            return WindowsFileOperationService.OpenFolderInExplorer(model.ProjectPath);
        }

        /// <summary>
        /// 在资源管理器中打开模型文件
        /// </summary>
        /// <param name="model">参考模型</param>
        /// <returns>操作结果</returns>
        public OperationResult OpenModelInExplorer(YDataModel yDataModel)
        {
            var model = yDataModel.YdwgData;
            if (string.IsNullOrEmpty(model.ModelPath))
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = "模型路径未设置"
                };
            }

            return WindowsFileOperationService.OpenFileInExplorer(model.ModelPath);
        }

        /// <summary>
        /// 使用默认程序打开模型文件
        /// </summary>
        /// <param name="model">参考模型</param>
        /// <returns>操作结果</returns>
        public OperationResult OpenModelWithDefaultProgram(YDataModel yDataModel)
        {
            var model = yDataModel.YdwgData;
            if (string.IsNullOrEmpty(model.ModelPath))
            {
                return new OperationResult
                {
                    Success = false,
                    ErrorMessage = "模型路径未设置"
                };
            }

            return WindowsFileOperationService.OpenFileWithDefaultProgram(model.ModelPath);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 确保配置目录存在
        /// </summary>
        private void EnsureConfigDirectoryExists()
        {
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
            }
        }

        #endregion
    }
}
