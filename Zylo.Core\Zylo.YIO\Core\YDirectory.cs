using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;

namespace Zylo.YIO.Core
{
    /// <summary>
    /// YDirectory - 企业级目录操作工具类（静态版本）
    ///
    /// 功能特性：
    /// - 🔧 基础目录操作：创建、删除、检查存在性、临时目录管理
    /// - 📋 高级目录操作：复制、移动、重命名、批量操作
    /// - 🛤️ 智能路径处理：路径组合、规范化、相对路径计算
    /// - 🎯 项目模板创建：支持 WebApp、ConsoleApp、Library、Desktop 等项目类型
    /// - 🔄 目录同步功能：增量同步、文件比较、统计信息
    /// - 👁️ 可视化功能：目录树生成、结构展示
    /// - 🎨 WPF 绑定支持：TreeView 数据绑定、属性通知、智能图标
    /// - 🧹 维护功能：空目录清理、结构优化
    /// - 🛡️ 安全特性：参数验证、异常处理、权限检查
    ///
    /// 支持的项目类型：
    /// - WebApp: Controllers, Views, Models, wwwroot, Data, Services
    /// - ConsoleApp: Models, Services, Utils, Config, Data, Logs
    /// - Library: Models, Services, Interfaces, Extensions, Utils
    /// - Desktop: Views, ViewModels, Models, Services, Resources, Assets
    /// - General: src, docs, tests, config, data, logs, temp
    ///
    /// 使用示例：
    /// <code>
    /// // 基础操作
    /// YDirectory.CreateDirectory(@"C:\MyProject\src");
    /// YDirectory.CreateProjectStructure(@"C:\NewProject", ProjectType.WebApp);
    ///
    /// // 高级操作
    /// YDirectory.CopyDirectory(@"C:\Source", @"C:\Backup", recursive: true);
    /// var syncResult = YDirectory.SynchronizeDirectories(@"C:\Source", @"C:\Target");
    ///
    /// // 路径处理
    /// var normalized = YDirectory.NormalizePath(@"C:/Mixed\Path//Structure");
    /// var relative = YDirectory.GetRelativePath(@"C:\Base", @"C:\Base\Sub\Dir");
    ///
    /// // 可视化
    /// var tree = YDirectory.GetDirectoryTree(@"C:\MyProject", maxDepth: 3);
    /// Console.WriteLine(tree);
    ///
    /// // WPF 绑定
    /// var wpfData = YDirectory.GetDirectoryTreeForWPF(@"C:\MyProject", maxDepth: 3, includeFiles: true);
    /// myTreeView.ItemsSource = wpfData;
    /// </code>
    ///
    /// 性能特点：
    /// - 智能缓存和优化算法
    /// - 支持大目录和深层结构
    /// - 内存友好的流式处理
    /// - 跨平台路径处理
    ///
    /// 安全特性：
    /// - 全面的参数验证
    /// - 异常安全的操作
    /// - 权限检查和处理
    /// - 防止路径遍历攻击
    /// </summary>

    public static partial class YDirectory
    {
        #region 基础目录操作 - 创建和存在性检查

        /// <summary>
        /// 创建目录（智能创建，支持幂等操作）
        ///
        /// 如果目录已存在，则直接返回成功；如果不存在，则创建目录。
        /// 该方法是幂等的，多次调用相同路径不会产生副作用。
        /// </summary>
        /// <param name="directoryPath">要创建的目录路径，支持相对路径和绝对路径</param>
        /// <returns>创建成功或目录已存在返回 true，失败返回 false</returns>
        /// <exception cref="ArgumentException">当目录路径为空或无效时抛出</exception>
        /// <example>
        /// <code>
        /// // 创建单个目录
        /// bool success = YDirectory.CreateDirectory(@"C:\MyProject");
        ///
        /// // 创建深层目录结构（会自动创建所有父目录）
        /// bool deepSuccess = YDirectory.CreateDirectory(@"C:\Projects\MyApp\src\Controllers");
        /// </code>
        /// </example>
        public static bool CreateDirectory(string directoryPath)
        {
            try
            {
                // 参数验证 - 确保路径不为空
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空或仅包含空白字符", nameof(directoryPath));

                // 幂等性检查 - 如果目录已存在，直接返回成功
                if (Directory.Exists(directoryPath))
                    return true; // 目录已存在，视为成功操作

                // 执行目录创建 - Directory.CreateDirectory 会自动创建所有必要的父目录
                Directory.CreateDirectory(directoryPath);
                return true;
            }
            catch (ArgumentException)
            {
                // 重新抛出参数异常，让调用者处理
                throw;
            }
            catch (UnauthorizedAccessException ex)
            {
                // 权限不足的情况
                Console.WriteLine($"创建目录权限不足 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (DirectoryNotFoundException ex)
            {
                // 目录不存在异常
                Console.WriteLine($"目录路径不存在 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                // IO异常（磁盘空间不足、路径过长等）
                Console.WriteLine($"创建目录IO异常 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                // 其他未预期的异常
                Console.WriteLine($"创建目录失败 {directoryPath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 递归创建目录（显式递归方法，与 CreateDirectory 功能相同）
        ///
        /// 注意：.NET 的 Directory.CreateDirectory 本身就是递归的，
        /// 此方法主要为了 API 的明确性和向后兼容性。
        /// </summary>
        /// <param name="directoryPath">要创建的目录路径</param>
        /// <returns>创建成功返回 true，失败返回 false</returns>
        /// <remarks>
        /// 此方法与 CreateDirectory 在功能上完全相同，
        /// 因为 .NET 的 Directory.CreateDirectory 本身就支持递归创建。
        /// 提供此方法主要是为了 API 的语义明确性。
        /// </remarks>
        public static bool CreateDirectoryRecursive(string directoryPath)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                // 调用底层递归创建方法
                // Directory.CreateDirectory 本身就是递归的，会自动创建所有父目录
                Directory.CreateDirectory(directoryPath);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"递归创建目录失败 {directoryPath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查目录是否存在
        ///
        /// 安全的目录存在性检查，处理各种边界情况。
        /// </summary>
        /// <param name="directoryPath">要检查的目录路径</param>
        /// <returns>目录存在返回 true，不存在或路径无效返回 false</returns>
        /// <remarks>
        /// 此方法会安全处理以下情况：
        /// - null 或空字符串路径
        /// - 无效的路径格式
        /// - 权限不足的路径
        /// - 网络路径的连接问题
        /// </remarks>
        public static bool DirectoryExists(string directoryPath)
        {
            // 快速检查 - 空路径直接返回 false
            if (string.IsNullOrWhiteSpace(directoryPath))
                return false;

            try
            {
                // 调用系统 API 检查目录存在性
                return Directory.Exists(directoryPath);
            }
            catch
            {
                // 任何异常都视为目录不存在（如权限不足、路径格式错误等）
                return false;
            }
        }

        /// <summary>
        /// 条件创建目录（仅当目录不存在时创建）
        ///
        /// 这是一个便利方法，结合了存在性检查和创建操作。
        /// 相比直接调用 CreateDirectory，此方法的语义更加明确。
        /// </summary>
        /// <param name="directoryPath">要创建的目录路径</param>
        /// <returns>创建成功或目录已存在返回 true，失败返回 false</returns>
        /// <example>
        /// <code>
        /// // 确保日志目录存在
        /// YDirectory.CreateIfNotExists(@"C:\Logs\2024");
        ///
        /// // 确保配置目录存在
        /// YDirectory.CreateIfNotExists(Path.Combine(appPath, "Config"));
        /// </code>
        /// </example>
        public static bool CreateIfNotExists(string directoryPath)
        {
            // 先检查目录是否已存在
            if (DirectoryExists(directoryPath))
                return true; // 目录已存在，无需创建

            // 目录不存在，尝试创建
            return CreateDirectory(directoryPath);
        }

        /// <summary>
        /// 创建安全的临时目录
        ///
        /// 在系统临时目录中创建一个具有唯一名称的临时目录。
        /// 使用 GUID 确保目录名称的唯一性，避免冲突。
        /// </summary>
        /// <param name="prefix">目录名前缀，用于标识临时目录的用途</param>
        /// <returns>创建的临时目录完整路径，失败返回空字符串</returns>
        /// <example>
        /// <code>
        /// // 创建默认临时目录
        /// string tempDir = YDirectory.CreateTempDirectory();
        /// // 结果类似：C:\Users\<USER>\AppData\Local\Temp\temp_a1b2c3d4e5f6...
        ///
        /// // 创建带自定义前缀的临时目录
        /// string logTempDir = YDirectory.CreateTempDirectory("LogProcessing");
        /// // 结果类似：C:\Users\<USER>\AppData\Local\Temp\LogProcessing_a1b2c3d4e5f6...
        ///
        /// // 使用完毕后记得清理
        /// if (Directory.Exists(tempDir))
        ///     Directory.Delete(tempDir, true);
        /// </code>
        /// </example>
        /// <remarks>
        /// 安全特性：
        /// - 使用系统临时目录，确保有写入权限
        /// - GUID 确保目录名称唯一性
        /// - 自动处理路径组合和格式化
        ///
        /// 注意事项：
        /// - 临时目录不会自动清理，使用完毕后需要手动删除
        /// - 建议在 using 语句或 try-finally 块中使用
        /// - 长时间运行的应用应定期清理临时目录
        /// </remarks>
        public static string CreateTempDirectory(string prefix = "temp")
        {
            try
            {
                // 获取系统临时目录路径
                var tempPath = Path.GetTempPath();

                // 生成唯一的目录名称
                // 使用 GUID 的 32 位十六进制表示（无连字符）确保唯一性
                var uniqueName = $"{prefix}_{Guid.NewGuid():N}";

                // 组合完整路径
                var fullPath = Path.Combine(tempPath, uniqueName);

                // 创建临时目录
                Directory.CreateDirectory(fullPath);

                return fullPath;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"创建临时目录权限不足: {ex.Message}");
                return string.Empty;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"创建临时目录IO异常: {ex.Message}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建临时目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        #endregion

        #region 目录删除操作 - 安全删除和清理

        /// <summary>
        /// 删除目录（支持递归和非递归模式）
        ///
        /// 提供灵活的目录删除功能，支持安全的递归删除和非递归删除。
        /// 具有幂等性，多次删除同一目录不会产生错误。
        /// </summary>
        /// <param name="directoryPath">要删除的目录路径</param>
        /// <param name="recursive">是否递归删除子目录和文件。false 时只删除空目录</param>
        /// <returns>删除成功返回 true，失败返回 false</returns>
        /// <exception cref="ArgumentException">当目录路径为空时抛出</exception>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 删除空目录
        /// bool success1 = dirOps.DeleteDirectory(@"C:\EmptyDir", recursive: false);
        ///
        /// // 递归删除目录及其所有内容
        /// bool success2 = dirOps.DeleteDirectory(@"C:\ProjectDir", recursive: true);
        /// </code>
        /// </example>
        /// <remarks>
        /// 安全特性：
        /// - 幂等操作：目录不存在时返回成功
        /// - 权限检查：自动处理权限不足的情况
        /// - 文件锁处理：处理文件被占用的情况
        ///
        /// 注意事项：
        /// - recursive=false 时，目录必须为空才能删除成功
        /// - recursive=true 时，会强制删除所有内容，请谨慎使用
        /// - 删除系统重要目录前请确认路径正确性
        /// </remarks>
        public static bool DeleteDirectory(string directoryPath, bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                // 幂等性检查 - 目录不存在视为删除成功
                if (!Directory.Exists(directoryPath))
                    return true; // 目录不存在，视为删除成功

                // 执行删除操作
                Directory.Delete(directoryPath, recursive);
                return true;
            }
            catch (ArgumentException)
            {
                // 重新抛出参数异常
                throw;
            }
            catch (UnauthorizedAccessException ex)
            {
                // 权限不足
                Console.WriteLine($"删除目录权限不足 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (DirectoryNotFoundException ex)
            {
                // 目录不存在（并发删除的情况）
                Console.WriteLine($"目录已被删除 {directoryPath}: {ex.Message}");
                return true; // 目录不存在，视为删除成功
            }
            catch (IOException ex)
            {
                // IO异常（文件被占用、目录不为空等）
                Console.WriteLine($"删除目录IO异常 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                // 其他未预期异常
                Console.WriteLine($"删除目录失败 {directoryPath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 递归删除目录及其所有内容（便利方法）
        ///
        /// 这是 DeleteDirectory(path, recursive: true) 的便利方法，
        /// 提供更明确的语义表达。
        /// </summary>
        /// <param name="directoryPath">要删除的目录路径</param>
        /// <returns>删除成功返回 true，失败返回 false</returns>
        /// <example>
        /// <code>
        /// // 强制删除整个项目目录
        /// bool success = dirOps.DeleteDirectoryRecursive(@"C:\TempProject");
        /// </code>
        /// </example>
        public static bool DeleteDirectoryRecursive(string directoryPath)
        {
            return DeleteDirectory(directoryPath, recursive: true);
        }

        /// <summary>
        /// 安全删除空目录
        ///
        /// 只删除完全为空的目录，如果目录包含任何文件或子目录则拒绝删除。
        /// 这是一个安全的删除方法，可以防止意外删除重要内容。
        /// </summary>
        /// <param name="directoryPath">要删除的目录路径</param>
        /// <returns>删除成功返回 true，目录不为空或失败返回 false</returns>
        /// <example>
        /// <code>
        /// // 只删除空目录，安全操作
        /// bool success = YDirectory.DeleteEmptyDirectory(@"C:\EmptyTempDir");
        /// if (!success)
        /// {
        ///     Console.WriteLine("目录不为空或删除失败");
        /// }
        /// </code>
        /// </example>
        public static bool DeleteEmptyDirectory(string directoryPath)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                // 目录存在性检查
                if (!Directory.Exists(directoryPath))
                    return true; // 目录不存在，视为删除成功

                // 空目录检查 - 使用 GetFileSystemEntries 检查是否包含任何内容
                if (Directory.GetFileSystemEntries(directoryPath).Length > 0)
                    return false; // 目录不为空，拒绝删除

                // 执行删除 - 使用非递归模式确保安全
                Directory.Delete(directoryPath, false);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除空目录失败 {directoryPath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清空目录内容但保留目录本身
        ///
        /// 删除指定目录中的所有文件和子目录，但保留目录结构本身。
        /// 这对于清理缓存目录、临时目录等场景非常有用。
        /// </summary>
        /// <param name="directoryPath">要清空的目录路径</param>
        /// <returns>清空成功返回 true，失败返回 false</returns>
        /// <example>
        /// <code>
        /// // 清空缓存目录
        /// bool success = YDirectory.DeleteDirectoryContents(@"C:\App\Cache");
        ///
        /// // 清空临时工作目录
        /// bool success2 = YDirectory.DeleteDirectoryContents(@"C:\Temp\WorkDir");
        /// </code>
        /// </example>
        /// <remarks>
        /// 操作特点：
        /// - 保留目录本身，只删除内容
        /// - 递归删除所有子目录和文件
        /// - 处理只读文件和隐藏文件
        /// - 安全处理文件锁定情况
        ///
        /// 适用场景：
        /// - 清理缓存目录
        /// - 重置工作目录
        /// - 清空日志目录
        /// - 准备目录用于新的操作
        /// </remarks>
        public static bool DeleteDirectoryContents(string directoryPath)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                // 目录存在性检查
                if (!Directory.Exists(directoryPath))
                    return true; // 目录不存在，视为清空成功

                // 第一步：删除所有文件
                // 获取目录中的所有文件（不包括子目录中的文件）
                var files = Directory.GetFiles(directoryPath);
                foreach (var file in files)
                {
                    try
                    {
                        // 处理只读文件 - 先移除只读属性
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.IsReadOnly)
                        {
                            fileInfo.IsReadOnly = false;
                        }

                        // 删除文件
                        File.Delete(file);
                    }
                    catch (Exception fileEx)
                    {
                        Console.WriteLine($"删除文件失败 {file}: {fileEx.Message}");
                        // 继续处理其他文件，不因单个文件失败而中断
                    }
                }

                // 第二步：递归删除所有子目录
                // 获取目录中的所有子目录
                var subDirectories = Directory.GetDirectories(directoryPath);
                foreach (var subDir in subDirectories)
                {
                    try
                    {
                        // 递归删除子目录及其所有内容
                        Directory.Delete(subDir, true);
                    }
                    catch (Exception dirEx)
                    {
                        Console.WriteLine($"删除子目录失败 {subDir}: {dirEx.Message}");
                        // 继续处理其他目录，不因单个目录失败而中断
                    }
                }

                return true;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"删除目录内容权限不足 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"删除目录内容IO异常 {directoryPath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除目录内容失败 {directoryPath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 目录复制和移动操作 - 高级目录管理

        /// <summary>
        /// 复制目录及其内容（支持递归和非递归模式）
        ///
        /// 提供完整的目录复制功能，支持文件覆盖、递归复制子目录等高级特性。
        /// 复制过程中会保持原始的目录结构和文件属性。
        /// </summary>
        /// <param name="sourceDirectory">源目录路径，必须是存在的目录</param>
        /// <param name="destinationDirectory">目标目录路径，如果不存在会自动创建</param>
        /// <param name="recursive">是否递归复制子目录，默认为 true</param>
        /// <returns>复制成功返回 true，失败返回 false</returns>
        /// <exception cref="ArgumentException">当源目录或目标目录路径为空时抛出</exception>
        /// <exception cref="DirectoryNotFoundException">当源目录不存在时抛出</exception>
        /// <example>
        /// <code>
        /// // 递归复制整个目录结构
        /// bool success1 = YDirectory.CopyDirectory(@"C:\Source", @"C:\Backup", recursive: true);
        ///
        /// // 只复制根目录的文件，不复制子目录
        /// bool success2 = YDirectory.CopyDirectory(@"C:\Source", @"C:\FilesOnly", recursive: false);
        /// </code>
        /// </example>
        /// <remarks>
        /// 复制特性：
        /// - 自动创建目标目录及其父目录
        /// - 文件覆盖：同名文件会被覆盖
        /// - 递归复制：可选择是否复制子目录
        /// - 结构保持：完整保持源目录的层级结构
        /// - 异常安全：复制失败不会影响源目录
        ///
        /// 性能考虑：
        /// - 大文件复制可能耗时较长
        /// - 深层目录结构会增加复制时间
        /// - 网络驱动器复制速度较慢
        /// </remarks>
        public static bool CopyDirectory(string sourceDirectory, string destinationDirectory, bool recursive = true)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(sourceDirectory))
                    throw new ArgumentException("源目录路径不能为空", nameof(sourceDirectory));

                if (string.IsNullOrWhiteSpace(destinationDirectory))
                    throw new ArgumentException("目标目录路径不能为空", nameof(destinationDirectory));

                // 源目录存在性检查
                if (!Directory.Exists(sourceDirectory))
                    throw new DirectoryNotFoundException($"源目录不存在: {sourceDirectory}");

                // 创建目标目录（如果不存在）
                Directory.CreateDirectory(destinationDirectory);

                // 第一步：复制当前目录中的所有文件
                var files = Directory.GetFiles(sourceDirectory);
                foreach (var file in files)
                {
                    // 获取文件名（不包含路径）
                    var fileName = Path.GetFileName(file);
                    // 构建目标文件路径
                    var destFile = Path.Combine(destinationDirectory, fileName);
                    // 复制文件，覆盖已存在的文件
                    File.Copy(file, destFile, overwrite: true);
                }

                // 第二步：递归复制子目录（如果启用递归）
                if (recursive)
                {
                    var subDirectories = Directory.GetDirectories(sourceDirectory);
                    foreach (var subDir in subDirectories)
                    {
                        // 获取子目录名称
                        var dirName = Path.GetFileName(subDir);
                        // 构建目标子目录路径
                        var destSubDir = Path.Combine(destinationDirectory, dirName);
                        // 递归调用复制子目录
                        CopyDirectory(subDir, destSubDir, recursive);
                    }
                }

                return true;
            }
            catch (ArgumentException)
            {
                // 重新抛出参数异常
                throw;
            }
            catch (DirectoryNotFoundException)
            {
                // 重新抛出目录不存在异常
                throw;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"复制目录权限不足 {sourceDirectory} -> {destinationDirectory}: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"复制目录IO异常 {sourceDirectory} -> {destinationDirectory}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制目录失败 {sourceDirectory} -> {destinationDirectory}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移动目录到新位置
        ///
        /// 将整个目录从源位置移动到目标位置，包括所有文件和子目录。
        /// 移动操作是原子性的，要么全部成功，要么保持原状。
        /// </summary>
        /// <param name="sourceDirectory">源目录路径，必须是存在的目录</param>
        /// <param name="destinationDirectory">目标目录路径，如果父目录不存在会自动创建</param>
        /// <returns>移动成功返回 true，失败返回 false</returns>
        /// <exception cref="ArgumentException">当源目录或目标目录路径为空时抛出</exception>
        /// <exception cref="DirectoryNotFoundException">当源目录不存在时抛出</exception>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 移动目录到新位置
        /// bool success = dirOps.MoveDirectory(@"C:\OldLocation\MyDir", @"C:\NewLocation\MyDir");
        ///
        /// // 移动并重命名
        /// bool success2 = dirOps.MoveDirectory(@"C:\Projects\OldName", @"C:\Projects\NewName");
        /// </code>
        /// </example>
        /// <remarks>
        /// 移动特性：
        /// - 原子操作：要么全部成功，要么保持原状
        /// - 自动创建：目标路径的父目录会自动创建
        /// - 跨驱动器：支持跨驱动器移动（实际为复制+删除）
        /// - 权限保持：保持原始的文件和目录权限
        ///
        /// 注意事项：
        /// - 移动后源目录将不存在
        /// - 跨驱动器移动可能较慢
        /// - 目标位置不能已存在同名目录
        /// </remarks>
        public static bool MoveDirectory(string sourceDirectory, string destinationDirectory)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(sourceDirectory))
                    throw new ArgumentException("源目录路径不能为空", nameof(sourceDirectory));

                if (string.IsNullOrWhiteSpace(destinationDirectory))
                    throw new ArgumentException("目标目录路径不能为空", nameof(destinationDirectory));

                // 源目录存在性检查
                if (!Directory.Exists(sourceDirectory))
                    throw new DirectoryNotFoundException($"源目录不存在: {sourceDirectory}");

                // 确保目标目录的父目录存在
                var parentDir = Path.GetDirectoryName(destinationDirectory);
                if (!string.IsNullOrEmpty(parentDir) && !Directory.Exists(parentDir))
                {
                    Directory.CreateDirectory(parentDir);
                }

                // 执行移动操作
                Directory.Move(sourceDirectory, destinationDirectory);
                return true;
            }
            catch (ArgumentException)
            {
                // 重新抛出参数异常
                throw;
            }
            catch (DirectoryNotFoundException)
            {
                // 重新抛出目录不存在异常
                throw;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"移动目录权限不足 {sourceDirectory} -> {destinationDirectory}: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"移动目录IO异常 {sourceDirectory} -> {destinationDirectory}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移动目录失败 {sourceDirectory} -> {destinationDirectory}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重命名目录（在同一父目录下更改名称）
        ///
        /// 在不改变目录位置的情况下更改目录名称。
        /// 这是移动操作的特殊情况，只改变名称不改变位置。
        /// </summary>
        /// <param name="directoryPath">要重命名的目录路径</param>
        /// <param name="newName">新的目录名称（不包含路径）</param>
        /// <returns>重命名成功返回 true，失败返回 false</returns>
        /// <exception cref="ArgumentException">当目录路径或新名称为空时抛出</exception>
        /// <exception cref="DirectoryNotFoundException">当目录不存在时抛出</exception>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 重命名目录
        /// bool success = dirOps.RenameDirectory(@"C:\Projects\OldName", "NewName");
        /// // 结果：C:\Projects\OldName -> C:\Projects\NewName
        /// </code>
        /// </example>
        /// <remarks>
        /// 重命名特性：
        /// - 位置不变：只改变名称，不改变父目录
        /// - 原子操作：要么成功要么保持原状
        /// - 名称验证：自动验证新名称的有效性
        /// - 冲突检查：如果新名称已存在会失败
        ///
        /// 限制条件：
        /// - 新名称不能包含路径分隔符
        /// - 新名称不能与同级目录重名
        /// - 不能重命名系统保护的目录
        /// </remarks>
        public static bool RenameDirectory(string directoryPath, string newName)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (string.IsNullOrWhiteSpace(newName))
                    throw new ArgumentException("新名称不能为空", nameof(newName));

                // 验证新名称不包含路径分隔符
                if (newName.Contains(Path.DirectorySeparatorChar) || newName.Contains(Path.AltDirectorySeparatorChar))
                {
                    throw new ArgumentException("新名称不能包含路径分隔符", nameof(newName));
                }

                // 目录存在性检查
                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                // 构建新的完整路径
                var parentDir = Path.GetDirectoryName(directoryPath);
                var newPath = Path.Combine(parentDir ?? "", newName);

                // 检查新路径是否已存在
                if (Directory.Exists(newPath))
                {
                    Console.WriteLine($"重命名失败：目标名称已存在 {newPath}");
                    return false;
                }

                // 执行重命名（实际是移动操作）
                Directory.Move(directoryPath, newPath);
                return true;
            }
            catch (ArgumentException)
            {
                // 重新抛出参数异常
                throw;
            }
            catch (DirectoryNotFoundException)
            {
                // 重新抛出目录不存在异常
                throw;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"重命名目录权限不足 {directoryPath} -> {newName}: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"重命名目录IO异常 {directoryPath} -> {newName}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重命名目录失败 {directoryPath} -> {newName}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 路径处理和高级扩展功能 - 智能路径管理

        /// <summary>
        /// 智能路径组合（自动处理路径分隔符和空值）
        ///
        /// 将多个路径组件智能组合成一个完整路径，自动处理路径分隔符、
        /// 空值过滤和路径格式标准化。
        /// </summary>
        /// <param name="paths">要组合的路径组件数组</param>
        /// <returns>组合后的标准化路径，如果所有组件都无效则返回空字符串</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 基础路径组合
        /// string path1 = dirOps.CombinePaths("C:", "Users", "Documents");
        /// // 结果: C:\Users\<USER>\Users\Documents
        ///
        /// // 处理混合分隔符
        /// string path3 = dirOps.CombinePaths("C:/Projects", "MyApp\\src");
        /// // 结果: C:\Projects\MyApp\src
        /// </code>
        /// </example>
        /// <remarks>
        /// 智能特性：
        /// - 自动过滤 null 和空白字符串
        /// - 统一路径分隔符为系统标准
        /// - 处理相对路径和绝对路径
        /// - 防止路径注入攻击
        ///
        /// 性能优化：
        /// - 预先过滤无效路径组件
        /// - 使用系统优化的 Path.Combine 方法
        /// - 避免不必要的字符串操作
        /// </remarks>
        public static string CombinePaths(params string[] paths)
        {
            // 参数验证 - 检查输入数组
            if (paths == null || paths.Length == 0)
                return "";

            // 过滤有效路径 - 移除 null 和空白字符串
            var validPaths = paths.Where(p => !string.IsNullOrWhiteSpace(p)).ToArray();
            if (validPaths.Length == 0)
                return "";

            // 使用系统方法组合路径 - 自动处理分隔符和格式
            return Path.Combine(validPaths);
        }

        /// <summary>
        /// 规范化路径（统一分隔符，移除多余的分隔符）
        ///
        /// 将任意格式的路径转换为标准化的系统路径格式，
        /// 处理混合分隔符、重复分隔符、末尾分隔符等问题。
        /// </summary>
        /// <param name="path">要规范化的原始路径</param>
        /// <returns>规范化后的路径，如果输入为空则返回空字符串</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 混合分隔符规范化
        /// string path1 = dirOps.NormalizePath("C:/Users\\Documents/Test");
        /// // 结果: C:\Users\<USER>\Test
        ///
        /// // 重复分隔符处理
        /// string path2 = dirOps.NormalizePath("C:\\\\Users\\\\\\<USER>\Users\Documents
        ///
        /// // 末尾分隔符处理（非根目录）
        /// string path3 = dirOps.NormalizePath("C:\Users\<USER>\\");
        /// // 结果: C:\Users\<USER>\\");
        /// // 结果: C:\
        /// </code>
        /// </example>
        /// <remarks>
        /// 规范化特性：
        /// - 统一分隔符：将正斜杠和反斜杠统一为系统标准分隔符
        /// - 移除重复：连续的分隔符会被合并为单个
        /// - 智能末尾：非根目录移除末尾分隔符，根目录保留
        /// - 跨平台：适用于 Windows、Linux 和 macOS 路径
        ///
        /// 特殊处理：
        /// - 根目录（如 "C:\"、"/"）保留末尾分隔符
        /// - 空路径返回空字符串
        /// - 无效字符不会被特别处理（由系统 API 处理）
        /// </remarks>
        public static string NormalizePath(string path)
        {
            // 参数验证 - 空路径处理
            if (string.IsNullOrWhiteSpace(path))
                return "";

            // 第一步：统一分隔符 - 将所有分隔符替换为系统标准分隔符
            // 这处理了混合使用正斜杠(/)和反斜杠(\)的情况
            var normalized = path.Replace('/', Path.DirectorySeparatorChar)
                .Replace('\\', Path.DirectorySeparatorChar);

            // 第二步：移除重复的分隔符 - 如 C:\\\\Users 变为 C:\Users
            // 循环替换，直到没有连续的分隔符
            while (normalized.Contains(Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar))
            {
                normalized = normalized.Replace(
                    Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar,
                    Path.DirectorySeparatorChar.ToString());
            }

            // 第三步：处理末尾分隔符 - 根目录保留，非根目录移除
            if (normalized.Length > 1 && normalized.EndsWith(Path.DirectorySeparatorChar))
            {
                // 检查是否是根目录（如 C:\ 或 /）
                // Windows 驱动器根目录：形如 "C:\"，长度为3，第2个字符是冒号
                bool isWindowsRoot = normalized.Length == 3 &&
                                     normalized[1] == ':' &&
                                     normalized[2] == Path.DirectorySeparatorChar;

                // Unix 根目录：单个斜杠 "/"
                bool isUnixRoot = normalized.Length == 1 &&
                                  normalized[0] == Path.DirectorySeparatorChar;

                // 如果不是根目录，则移除末尾分隔符
                if (!(isWindowsRoot || isUnixRoot))
                {
                    normalized = normalized.TrimEnd(Path.DirectorySeparatorChar);
                }
            }

            return normalized;
        }

        /// <summary>
        /// 计算从起始路径到目标路径的相对路径
        ///
        /// 计算两个路径之间的相对关系，返回从起始路径导航到目标路径的相对路径。
        /// 支持跨目录、父子目录、同级目录等各种路径关系。
        /// </summary>
        /// <param name="fromPath">起始路径（相对于此路径计算）</param>
        /// <param name="toPath">目标路径（要到达的路径）</param>
        /// <returns>从起始路径到目标路径的相对路径</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 子目录相对路径
        /// string rel1 = dirOps.GetRelativePath(@"C:\Projects", @"C:\Projects\MyApp\src");
        /// // 结果: MyApp\src
        ///
        /// // 父目录相对路径
        /// string rel2 = dirOps.GetRelativePath(@"C:\Projects\MyApp\src", @"C:\Projects");
        /// // 结果: ..\..
        ///
        /// // 同级目录相对路径
        /// string rel3 = dirOps.GetRelativePath(@"C:\Projects\App1", @"C:\Projects\App2");
        /// // 结果: ..\App2
        ///
        /// // 跨驱动器路径（返回绝对路径）
        /// string rel4 = dirOps.GetRelativePath(@"C:\Projects", @"D:\Data");
        /// // 结果: D:\Data
        /// </code>
        /// </example>
        /// <remarks>
        /// 计算特性：
        /// - 自动处理路径格式差异
        /// - 支持绝对路径和相对路径输入
        /// - 跨平台兼容（Windows、Linux、macOS）
        /// - 智能处理驱动器差异
        ///
        /// 边界情况：
        /// - 空路径：返回目标路径
        /// - 相同路径：返回当前目录标识符
        /// - 跨驱动器：返回目标路径的绝对路径
        /// - 无效路径：返回目标路径作为后备
        ///
        /// 性能考虑：
        /// - 使用 URI 类进行精确计算
        /// - 自动处理路径规范化
        /// - 异常安全的实现
        /// </remarks>
        public static string GetRelativePath(string fromPath, string toPath)
        {
            try
            {
                // 参数验证 - 空路径处理
                if (string.IsNullOrWhiteSpace(fromPath) || string.IsNullOrWhiteSpace(toPath))
                    return toPath ?? "";

                // 路径规范化 - 转换为绝对路径并添加目录分隔符
                // 为起始路径添加分隔符确保它被视为目录而不是文件
                var fromUri = new Uri(Path.GetFullPath(fromPath) + Path.DirectorySeparatorChar);
                var toUri = new Uri(Path.GetFullPath(toPath));

                // 计算相对 URI - 使用 .NET 内置的相对路径计算
                var relativeUri = fromUri.MakeRelativeUri(toUri);

                // URI 解码 - 处理编码的特殊字符
                var relativePath = Uri.UnescapeDataString(relativeUri.ToString());

                // 分隔符标准化 - 将 URI 的正斜杠转换为系统分隔符
                return relativePath.Replace('/', Path.DirectorySeparatorChar);
            }
            catch (ArgumentException)
            {
                // 路径格式无效时返回目标路径
                return toPath ?? "";
            }
            catch (UriFormatException)
            {
                // URI 格式错误时返回目标路径
                return toPath ?? "";
            }
            catch (Exception)
            {
                // 其他异常时返回目标路径作为后备
                return toPath ?? "";
            }
        }

        /// <summary>
        /// 批量创建目录结构（高效的批量目录创建）
        ///
        /// 在指定的基础路径下批量创建多个目录，支持深层目录结构创建。
        /// 只创建不存在的目录，已存在的目录会被跳过，提供幂等性保证。
        /// </summary>
        /// <param name="basePath">基础路径，所有目录将在此路径下创建</param>
        /// <param name="directoryNames">要创建的目录名称数组，支持相对路径</param>
        /// <returns>实际创建的目录数量（不包括已存在的目录）</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 创建项目基础结构
        /// int created = dirOps.CreateDirectoryStructure(@"C:\MyProject",
        ///     "src", "docs", "tests", "config");
        /// // 创建: C:\MyProject\src, C:\MyProject\docs 等
        ///
        /// // 创建深层结构
        /// int created2 = dirOps.CreateDirectoryStructure(@"C:\WebApp",
        ///     "Controllers", "Views", "Models",
        ///     "wwwroot/css", "wwwroot/js", "wwwroot/images");
        /// // 自动创建嵌套目录结构
        ///
        /// Console.WriteLine($"创建了 {created} 个新目录");
        /// </code>
        /// </example>
        /// <remarks>
        /// 批量特性：
        /// - 幂等操作：重复调用不会产生副作用
        /// - 智能跳过：已存在的目录会被跳过
        /// - 深层支持：支持创建多层嵌套目录
        /// - 原子性：单个目录创建失败不影响其他目录
        ///
        /// 性能优化：
        /// - 批量处理减少系统调用开销
        /// - 预先验证避免无效操作
        /// - 异常隔离确保部分成功
        ///
        /// 使用场景：
        /// - 项目初始化：创建标准项目结构
        /// - 批量部署：创建多个工作目录
        /// - 模板应用：应用预定义的目录模板
        /// </remarks>
        public static int CreateDirectoryStructure(string basePath, params string[] directoryNames)
        {
            try
            {
                // 参数验证 - 基础路径检查
                if (string.IsNullOrWhiteSpace(basePath))
                    return 0;

                var createdCount = 0;

                // 批量处理 - 遍历所有目录名称
                foreach (var dirName in directoryNames)
                {
                    // 跳过无效的目录名称
                    if (string.IsNullOrWhiteSpace(dirName))
                        continue;

                    try
                    {
                        // 构建完整路径 - 组合基础路径和目录名
                        var fullPath = Path.Combine(basePath, dirName);

                        // 存在性检查 - 只创建不存在的目录
                        if (!Directory.Exists(fullPath))
                        {
                            // 创建目录 - 自动创建所有必要的父目录
                            Directory.CreateDirectory(fullPath);
                            createdCount++;
                        }
                        // 已存在的目录被跳过，不计入创建数量
                    }
                    catch (Exception dirEx)
                    {
                        // 单个目录创建失败不影响其他目录
                        Console.WriteLine($"创建目录失败 {dirName}: {dirEx.Message}");
                        // 继续处理下一个目录
                    }
                }

                return createdCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量创建目录结构失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 创建标准项目目录结构（基于项目类型的模板化创建）
        ///
        /// 根据指定的项目类型自动创建相应的标准目录结构，
        /// 支持多种常见的项目类型和开发框架。
        /// </summary>
        /// <param name="projectPath">项目根路径，将在此路径下创建项目结构</param>
        /// <param name="projectType">项目类型，决定创建哪种目录结构模板</param>
        /// <returns>创建成功返回 true，失败返回 false</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 创建 Web 应用项目结构
        /// bool success1 = dirOps.CreateProjectStructure(@"C:\MyWebApp", ProjectType.WebApp);
        /// // 创建: Controllers, Views, Models, wwwroot, Data, Services 等
        ///
        /// // 创建控制台应用项目结构
        /// bool success2 = dirOps.CreateProjectStructure(@"C:\MyConsoleApp", ProjectType.ConsoleApp);
        /// // 创建: Models, Services, Utils, Config, Data, Logs
        ///
        /// // 创建类库项目结构
        /// bool success3 = dirOps.CreateProjectStructure(@"C:\MyLibrary", ProjectType.Library);
        /// // 创建: Models, Services, Interfaces, Extensions, Utils
        /// </code>
        /// </example>
        /// <remarks>
        /// 支持的项目类型：
        /// - WebApp: ASP.NET Core Web 应用（MVC 模式）
        /// - ConsoleApp: 控制台应用程序
        /// - Library: 类库项目
        /// - Desktop: 桌面应用程序（MVVM 模式）
        /// - General: 通用项目结构
        ///
        /// 模板特性：
        /// - 基于最佳实践的目录结构
        /// - 支持现代开发框架
        /// - 可扩展的模板系统
        /// - 自动创建嵌套目录
        /// </remarks>
        public static bool CreateProjectStructure(string projectPath, ProjectType projectType = ProjectType.General)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(projectPath))
                    return false;

                // 根据项目类型选择目录模板
                var directories = projectType switch
                {
                    // Web 应用项目 - ASP.NET Core MVC 结构
                    ProjectType.WebApp => new[]
                    {
                        "Controllers", // MVC 控制器
                        "Views", // 视图文件
                        "Models", // 数据模型
                        "wwwroot", // 静态资源根目录
                        "wwwroot/css", // 样式文件
                        "wwwroot/js", // JavaScript 文件
                        "wwwroot/images", // 图片资源
                        "Data", // 数据访问层
                        "Services" // 业务服务层
                    },

                    // 控制台应用项目 - 简洁的控制台应用结构
                    ProjectType.ConsoleApp => new[]
                    {
                        "Models", // 数据模型
                        "Services", // 业务服务
                        "Utils", // 工具类
                        "Config", // 配置文件
                        "Data", // 数据文件
                        "Logs" // 日志文件
                    },

                    // 类库项目 - 面向接口的类库结构
                    ProjectType.Library => new[]
                    {
                        "Models", // 数据模型
                        "Services", // 服务实现
                        "Interfaces", // 接口定义
                        "Extensions", // 扩展方法
                        "Utils" // 工具类
                    },

                    // 桌面应用项目 - MVVM 模式结构
                    ProjectType.Desktop => new[]
                    {
                        "Views", // 视图（MVVM）
                        "ViewModels", // 视图模型
                        "Models", // 数据模型
                        "Services", // 业务服务
                        "Resources", // 资源文件
                        "Assets" // 静态资源
                    },

                    // 通用项目 - 适用于各种项目的通用结构
                    _ => new[]
                    {
                        "src", // 源代码
                        "docs", // 文档
                        "tests", // 测试
                        "config", // 配置
                        "data", // 数据
                        "logs", // 日志
                        "temp" // 临时文件
                    }
                };

                // 批量创建目录结构
                CreateDirectoryStructure(projectPath, directories);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建项目结构失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 项目类型枚举
        ///
        /// 定义支持的项目类型，每种类型对应不同的目录结构模板。
        /// </summary>
        public enum ProjectType
        {
            /// <summary>通用项目 - 适用于各种项目的基础结构</summary>
            General,

            /// <summary>Web 应用 - ASP.NET Core MVC 项目结构</summary>
            WebApp,

            /// <summary>控制台应用 - 简洁的控制台应用结构</summary>
            ConsoleApp,

            /// <summary>类库项目 - 面向接口的类库结构</summary>
            Library,

            /// <summary>桌面应用 - MVVM 模式的桌面应用结构</summary>
            Desktop
        }

        #endregion

        #region 目录维护功能 - 清理和优化

        /// <summary>
        /// 递归清理空目录（自底向上删除所有空目录）
        ///
        /// 从指定目录开始，递归检查并删除所有空的子目录。
        /// 采用深度优先的清理策略，确保彻底清理嵌套的空目录结构。
        /// </summary>
        /// <param name="directoryPath">要清理的根目录路径</param>
        /// <returns>实际删除的空目录数量</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 清理项目中的空目录
        /// int deleted = dirOps.CleanupEmptyDirectories(@"C:\MyProject");
        /// Console.WriteLine($"清理了 {deleted} 个空目录");
        ///
        /// // 清理临时目录
        /// int tempDeleted = dirOps.CleanupEmptyDirectories(@"C:\Temp");
        /// </code>
        /// </example>
        /// <remarks>
        /// 清理特性：
        /// - 递归处理：深度优先遍历所有子目录
        /// - 自底向上：先清理子目录，再检查父目录
        /// - 安全清理：只删除完全为空的目录
        /// - 统计功能：返回实际删除的目录数量
        ///
        /// 清理策略：
        /// 1. 递归清理所有子目录
        /// 2. 检查当前目录是否为空
        /// 3. 如果为空则删除当前目录
        /// 4. 返回删除计数
        ///
        /// 安全保证：
        /// - 不会删除包含文件的目录
        /// - 不会删除包含非空子目录的目录
        /// - 异常安全，单个目录失败不影响整体清理
        /// </remarks>
        public static int CleanupEmptyDirectories(string directoryPath)
        {
            try
            {
                // 目录存在性检查
                if (!Directory.Exists(directoryPath))
                    return 0;

                var deletedCount = 0;

                // 第一步：获取所有子目录
                var subdirectories = Directory.GetDirectories(directoryPath);

                // 第二步：递归清理所有子目录（深度优先）
                foreach (var subdir in subdirectories)
                {
                    try
                    {
                        // 递归调用，清理子目录
                        deletedCount += CleanupEmptyDirectories(subdir);
                    }
                    catch (Exception subdirEx)
                    {
                        // 单个子目录清理失败不影响其他目录
                        Console.WriteLine($"清理子目录失败 {subdir}: {subdirEx.Message}");
                    }
                }

                // 第三步：检查当前目录是否为空
                // 使用 EnumerateFileSystemEntries 高效检查目录内容
                if (!Directory.EnumerateFileSystemEntries(directoryPath).Any())
                {
                    try
                    {
                        // 目录为空，执行删除
                        Directory.Delete(directoryPath);
                        deletedCount++;
                    }
                    catch (Exception deleteEx)
                    {
                        // 删除失败（可能是权限问题或目录被占用）
                        Console.WriteLine($"删除空目录失败 {directoryPath}: {deleteEx.Message}");
                    }
                }

                return deletedCount;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"清理空目录权限不足 {directoryPath}: {ex.Message}");
                return 0;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"清理空目录IO异常 {directoryPath}: {ex.Message}");
                return 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理空目录失败 {directoryPath}: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region 目录同步功能 - 增量同步和备份

        /// <summary>
        /// 智能目录同步（单向增量同步）
        ///
        /// 将源目录的内容同步到目标目录，支持增量同步、文件比较和可选的清理功能。
        /// 只同步变更的文件，提供高效的同步性能和详细的操作统计。
        /// </summary>
        /// <param name="sourceDirectory">源目录路径，同步的数据来源</param>
        /// <param name="targetDirectory">目标目录路径，同步的数据目标</param>
        /// <param name="deleteExtra">是否删除目标目录中源目录没有的文件和目录</param>
        /// <returns>包含详细同步统计信息的结果对象</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 基础同步（不删除多余文件）
        /// var result1 = dirOps.SynchronizeDirectories(@"C:\Source", @"C:\Backup");
        /// Console.WriteLine($"复制: {result1.CopiedFiles}, 更新: {result1.UpdatedFiles}");
        ///
        /// // 完整同步（删除多余文件）
        /// var result2 = dirOps.SynchronizeDirectories(@"C:\Source", @"C:\Mirror", deleteExtra: true);
        /// Console.WriteLine($"删除: {result2.DeletedFiles} 文件, {result2.DeletedDirectories} 目录");
        /// </code>
        /// </example>
        /// <remarks>
        /// 同步特性：
        /// - 增量同步：只处理变更的文件，跳过未修改的文件
        /// - 智能比较：基于修改时间和文件大小判断是否需要更新
        /// - 结构保持：完整保持源目录的层级结构
        /// - 可选清理：支持删除目标目录中多余的文件和目录
        ///
        /// 同步策略：
        /// 1. 遍历源目录所有文件
        /// 2. 比较文件修改时间和大小
        /// 3. 复制新文件，更新变更文件，跳过未变更文件
        /// 4. 可选删除目标目录中多余的文件和空目录
        ///
        /// 性能优化：
        /// - 智能跳过未变更文件
        /// - 批量目录创建
        /// - 高效的文件比较算法
        /// </remarks>
        public static DirectorySyncResult SynchronizeDirectories(string sourceDirectory, string targetDirectory,
            bool deleteExtra = false)
        {
            // 初始化同步结果对象
            var result = new DirectorySyncResult
            {
                SourceDirectory = sourceDirectory,
                TargetDirectory = targetDirectory
            };

            try
            {
                // 第一步：验证源目录存在性
                if (!Directory.Exists(sourceDirectory))
                {
                    result.ErrorMessage = "源目录不存在";
                    return result;
                }

                // 第二步：确保目标目录存在
                Directory.CreateDirectory(targetDirectory);

                // 第三步：获取源目录所有文件（递归）
                var sourceFiles = Directory.GetFiles(sourceDirectory, "*", SearchOption.AllDirectories);

                // 第四步：逐个处理源文件
                foreach (var sourceFile in sourceFiles)
                {
                    try
                    {
                        // 计算相对路径 - 保持目录结构
                        var relativePath = Path.GetRelativePath(sourceDirectory, sourceFile);
                        var targetFile = Path.Combine(targetDirectory, relativePath);
                        var targetDir = Path.GetDirectoryName(targetFile);

                        // 确保目标文件的目录存在
                        if (!string.IsNullOrEmpty(targetDir))
                        {
                            Directory.CreateDirectory(targetDir);
                        }

                        // 获取源文件信息用于比较
                        var sourceInfo = new FileInfo(sourceFile);
                        var needsCopy = true;

                        // 第五步：智能文件比较
                        if (File.Exists(targetFile))
                        {
                            var targetInfo = new FileInfo(targetFile);

                            // 比较修改时间和文件大小
                            // 如果目标文件更新且大小相同，则跳过复制
                            if (sourceInfo.LastWriteTime <= targetInfo.LastWriteTime &&
                                sourceInfo.Length == targetInfo.Length)
                            {
                                needsCopy = false;
                                result.SkippedFiles++;
                            }
                            else
                            {
                                // 文件需要更新
                                result.UpdatedFiles++;
                            }
                        }
                        else
                        {
                            // 新文件需要复制
                            result.CopiedFiles++;
                        }

                        // 第六步：执行文件复制（如果需要）
                        if (needsCopy)
                        {
                            File.Copy(sourceFile, targetFile, overwrite: true);
                        }
                    }
                    catch (Exception fileEx)
                    {
                        // 单个文件处理失败不影响整体同步
                        Console.WriteLine($"同步文件失败 {sourceFile}: {fileEx.Message}");
                    }
                }

                // 第七步：清理多余文件（可选）
                if (deleteExtra)
                {
                    // 获取目标目录所有文件
                    var targetFiles = Directory.GetFiles(targetDirectory, "*", SearchOption.AllDirectories);

                    foreach (var targetFile in targetFiles)
                    {
                        try
                        {
                            // 计算目标文件对应的源文件路径
                            var relativePath = Path.GetRelativePath(targetDirectory, targetFile);
                            var sourceFile = Path.Combine(sourceDirectory, relativePath);

                            // 如果源目录中不存在对应文件，则删除目标文件
                            if (!File.Exists(sourceFile))
                            {
                                File.Delete(targetFile);
                                result.DeletedFiles++;
                            }
                        }
                        catch (Exception deleteEx)
                        {
                            // 单个文件删除失败不影响整体清理
                            Console.WriteLine($"删除多余文件失败 {targetFile}: {deleteEx.Message}");
                        }
                    }

                    // 第八步：清理空目录
                    try
                    {
                        result.DeletedDirectories = CleanupEmptyDirectories(targetDirectory);
                    }
                    catch (Exception cleanupEx)
                    {
                        Console.WriteLine($"清理空目录失败: {cleanupEx.Message}");
                    }
                }

                // 同步成功完成
                result.Success = true;
            }
            catch (UnauthorizedAccessException ex)
            {
                result.ErrorMessage = $"权限不足: {ex.Message}";
                Console.WriteLine($"目录同步权限不足: {ex.Message}");
            }
            catch (DirectoryNotFoundException ex)
            {
                result.ErrorMessage = $"目录不存在: {ex.Message}";
                Console.WriteLine($"目录同步失败，目录不存在: {ex.Message}");
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"IO异常: {ex.Message}";
                Console.WriteLine($"目录同步IO异常: {ex.Message}");
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                Console.WriteLine($"目录同步失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 目录同步操作结果
        ///
        /// 包含目录同步操作的详细统计信息和执行状态，
        /// 提供完整的同步过程分析和结果报告。
        /// </summary>
        public class DirectorySyncResult
        {
            /// <summary>同步操作是否成功完成</summary>
            public bool Success { get; set; }

            /// <summary>源目录路径</summary>
            public string SourceDirectory { get; set; } = "";

            /// <summary>目标目录路径</summary>
            public string TargetDirectory { get; set; } = "";

            /// <summary>新复制的文件数量</summary>
            public int CopiedFiles { get; set; }

            /// <summary>更新的文件数量（已存在但内容变更）</summary>
            public int UpdatedFiles { get; set; }

            /// <summary>跳过的文件数量（未变更）</summary>
            public int SkippedFiles { get; set; }

            /// <summary>删除的文件数量（仅在 deleteExtra=true 时）</summary>
            public int DeletedFiles { get; set; }

            /// <summary>删除的目录数量（仅在 deleteExtra=true 时）</summary>
            public int DeletedDirectories { get; set; }

            /// <summary>错误信息（如果同步失败）</summary>
            public string? ErrorMessage { get; set; }

            /// <summary>
            /// 总处理文件数量（复制 + 更新 + 跳过）
            /// </summary>
            public int TotalProcessedFiles => CopiedFiles + UpdatedFiles + SkippedFiles;

            /// <summary>
            /// 实际变更文件数量（复制 + 更新）
            /// </summary>
            public int TotalChangedFiles => CopiedFiles + UpdatedFiles;

            /// <summary>
            /// 总删除项目数量（文件 + 目录）
            /// </summary>
            public int TotalDeletedItems => DeletedFiles + DeletedDirectories;
        }

        #endregion

        #region 目录可视化功能 - 树形结构展示

        /// <summary>
        /// 生成可视化目录树结构（文本格式）
        ///
        /// 以树形结构展示目录的层级关系，使用图标和缩进清晰显示
        /// 目录和文件的组织结构，支持深度限制和文件数量控制。
        /// </summary>
        /// <param name="directoryPath">要分析的目录路径</param>
        /// <param name="maxDepth">最大遍历深度，防止过深的目录结构导致输出过长</param>
        /// <returns>格式化的目录树文本，如果目录不存在或发生错误则返回错误信息</returns>
        /// <example>
        /// <code>
        /// var dirOps = new YDirectory();
        ///
        /// // 生成项目目录树
        /// string tree = dirOps.GetDirectoryTree(@"C:\MyProject", maxDepth: 3);
        /// Console.WriteLine(tree);
        ///
        /// /* 输出示例:
        /// 📁 MyProject
        ///   📁 src
        ///     📁 Controllers
        ///       📄 HomeController.cs
        ///       📄 ApiController.cs
        ///     📁 Models
        ///       📄 User.cs
        ///   📁 wwwroot
        ///     📁 css
        ///     📁 js
        ///   📄 Program.cs
        ///   📄 appsettings.json
        /// */
        /// </code>
        /// </example>
        /// <remarks>
        /// 可视化特性：
        /// - 图标标识：📁 表示目录，📄 表示文件
        /// - 层级缩进：通过缩进清晰显示目录层级关系
        /// - 深度控制：防止过深的目录结构导致输出过长
        /// - 文件限制：每个目录最多显示10个文件，避免输出过多
        /// - 统计提示：显示隐藏文件的数量
        ///
        /// 性能考虑：
        /// - 深度限制避免无限递归
        /// - 文件数量限制控制输出大小
        /// - 异常安全的目录访问
        /// - 内存友好的字符串构建
        ///
        /// 适用场景：
        /// - 项目结构展示
        /// - 目录内容分析
        /// - 文档生成
        /// - 调试和诊断
        /// </remarks>
        public static string GetDirectoryTree(string directoryPath, int maxDepth = 5)
        {
            try
            {
                // 目录存在性检查
                if (!Directory.Exists(directoryPath))
                    return "目录不存在";

                // 使用 StringBuilder 高效构建字符串
                var tree = new StringBuilder();

                // 调用递归方法构建目录树
                BuildDirectoryTree(directoryPath, tree, "", 0, maxDepth);

                return tree.ToString();
            }
            catch (UnauthorizedAccessException ex)
            {
                return $"获取目录树权限不足: {ex.Message}";
            }
            catch (IOException ex)
            {
                return $"获取目录树IO异常: {ex.Message}";
            }
            catch (Exception ex)
            {
                return $"获取目录树失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 递归构建目录树结构（内部辅助方法）
        ///
        /// 使用深度优先算法递归遍历目录结构，生成格式化的树形文本。
        /// 采用缩进和图标来清晰表示目录层级关系。
        /// </summary>
        /// <param name="directoryPath">当前处理的目录路径</param>
        /// <param name="tree">用于构建结果的字符串构建器</param>
        /// <param name="indent">当前层级的缩进字符串</param>
        /// <param name="currentDepth">当前遍历深度</param>
        /// <param name="maxDepth">最大允许深度</param>
        private static void BuildDirectoryTree(string directoryPath, StringBuilder tree, string indent, int currentDepth,
            int maxDepth)
        {
            // 深度限制检查 - 防止过深遍历
            if (currentDepth >= maxDepth)
                return;

            try
            {
                // 第一步：显示当前目录
                var dirInfo = new DirectoryInfo(directoryPath);
                tree.AppendLine($"{indent}📁 {dirInfo.Name}");

                // 第二步：计算下一级的缩进
                var newIndent = indent + "  ";

                // 第三步：递归处理所有子目录
                var subdirectories = Directory.GetDirectories(directoryPath);
                foreach (var subdir in subdirectories)
                {
                    try
                    {
                        // 递归调用处理子目录
                        BuildDirectoryTree(subdir, tree, newIndent, currentDepth + 1, maxDepth);
                    }
                    catch (Exception subdirEx)
                    {
                        // 单个子目录访问失败不影响其他目录
                        var subdirName = Path.GetFileName(subdir);
                        tree.AppendLine($"{newIndent}📁 {subdirName} ❌ 访问失败: {subdirEx.Message}");
                    }
                }

                // 第四步：显示当前目录中的文件（限制数量）
                var files = Directory.GetFiles(directoryPath);
                var displayCount = Math.Min(files.Length, 10); // 最多显示10个文件

                for (int i = 0; i < displayCount; i++)
                {
                    var fileName = Path.GetFileName(files[i]);
                    tree.AppendLine($"{newIndent}📄 {fileName}");
                }

                // 第五步：如果文件数量超过限制，显示省略提示
                if (files.Length > 10)
                {
                    tree.AppendLine($"{newIndent}... 还有 {files.Length - 10} 个文件");
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                // 权限不足的情况
                tree.AppendLine($"{indent}❌ 权限不足: {ex.Message}");
            }
            catch (DirectoryNotFoundException ex)
            {
                // 目录不存在的情况（可能在遍历过程中被删除）
                tree.AppendLine($"{indent}❌ 目录不存在: {ex.Message}");
            }
            catch (IOException ex)
            {
                // IO异常（如网络驱动器断开）
                tree.AppendLine($"{indent}❌ IO异常: {ex.Message}");
            }
            catch (Exception ex)
            {
                // 其他未预期的异常
                tree.AppendLine($"{indent}❌ 访问失败: {ex.Message}");
            }
        }

        #endregion

        #region WPF 绑定和 UI 集成功能 - 数据绑定支持

        /// <summary>
        /// 生成适用于 WPF TreeView 绑定的目录树数据结构
        ///
        /// 将目录结构转换为适合 WPF TreeView 控件数据绑定的层次化对象集合。
        /// 支持延迟加载、图标绑定和属性通知，提供流畅的用户界面体验。
        /// </summary>
        /// <param name="directoryPath">要分析的目录路径</param>
        /// <param name="maxDepth">最大遍历深度，防止过深的目录结构</param>
        /// <param name="includeFiles">是否包含文件节点</param>
        /// <returns>适用于 WPF 绑定的目录树节点集合</returns>
        /// <example>
        /// <code>
        /// // 获取 WPF 绑定数据
        /// var treeNodes = YDirectory.GetDirectoryTreeForWPF(@"C:\MyProject", maxDepth: 3, includeFiles: true);
        ///
        /// // 在 WPF 中绑定到 TreeView
        /// myTreeView.ItemsSource = treeNodes;
        ///
        /// // XAML 绑定示例:
        /// // &lt;TreeView ItemsSource="{Binding DirectoryNodes}"&gt;
        /// //   &lt;TreeView.ItemTemplate&gt;
        /// //     &lt;HierarchicalDataTemplate ItemsSource="{Binding Children}"&gt;
        /// //       &lt;StackPanel Orientation="Horizontal"&gt;
        /// //         &lt;Image Source="{Binding IconPath}" Width="16" Height="16"/&gt;
        /// //         &lt;TextBlock Text="{Binding Name}" Margin="5,0"/&gt;
        /// //       &lt;/StackPanel&gt;
        /// //     &lt;/HierarchicalDataTemplate&gt;
        /// //   &lt;/TreeView.ItemTemplate&gt;
        /// // &lt;/TreeView&gt;
        /// </code>
        /// </example>
        /// <remarks>
        /// WPF 绑定特性：
        /// - 层次化数据：支持 HierarchicalDataTemplate 绑定
        /// - 属性通知：实现 INotifyPropertyChanged 接口
        /// - 图标支持：提供文件和目录的图标路径
        /// - 延迟加载：支持按需加载子节点
        /// - 性能优化：避免一次性加载过多数据
        ///
        /// 适用场景：
        /// - WPF 文件浏览器
        /// - 项目结构展示
        /// - 目录选择对话框
        /// - 文档管理系统
        /// </remarks>
        public static List<DirectoryTreeNode> GetDirectoryTreeForWPF(string directoryPath, int maxDepth = 5,
            bool includeFiles = true)
        {
            var result = new List<DirectoryTreeNode>();

            try
            {
                // 目录存在性检查
                if (!Directory.Exists(directoryPath))
                    return result;

                // 创建根节点
                var rootNode = BuildWPFTreeNode(directoryPath, 0, maxDepth, includeFiles);
                if (rootNode != null)
                {
                    result.Add(rootNode);
                }
            }
            catch (Exception ex)
            {
                // 创建错误节点
                result.Add(new DirectoryTreeNode
                {
                    Name = $"错误: {ex.Message}",
                    FullPath = directoryPath,
                    IsDirectory = true,
                    IconPath = "❌", // 使用 Unicode 错误图标
                    Children = new List<DirectoryTreeNode>()
                });
            }

            return result;
        }

        /// <summary>
        /// 递归构建 WPF 树节点（内部辅助方法）
        /// </summary>
        /// <param name="path">当前路径</param>
        /// <param name="currentDepth">当前深度</param>
        /// <param name="maxDepth">最大深度</param>
        /// <param name="includeFiles">是否包含文件</param>
        /// <returns>构建的树节点</returns>
        private static DirectoryTreeNode? BuildWPFTreeNode(string path, int currentDepth, int maxDepth, bool includeFiles)
        {
            try
            {
                // 深度限制检查
                if (currentDepth >= maxDepth)
                    return null;

                var info = new DirectoryInfo(path);
                var node = new DirectoryTreeNode
                {
                    Name = info.Name,
                    FullPath = info.FullName,
                    IsDirectory = true,
                    IconPath = GetDirectoryIcon(info.Name, hasError: false, useUnicodeAsFallback: true),
                    Children = new List<DirectoryTreeNode>()
                };

                // 添加子目录
                var subdirectories = Directory.GetDirectories(path);
                foreach (var subdir in subdirectories.Take(50)) // 限制子目录数量
                {
                    var childNode = BuildWPFTreeNode(subdir, currentDepth + 1, maxDepth, includeFiles);
                    if (childNode != null)
                    {
                        node.Children.Add(childNode);
                    }
                }

                // 添加文件（如果需要）
                if (includeFiles && currentDepth < maxDepth - 1)
                {
                    var files = Directory.GetFiles(path);
                    foreach (var file in files.Take(20)) // 限制文件数量
                    {
                        var fileInfo = new FileInfo(file);
                        var fileNode = new DirectoryTreeNode
                        {
                            Name = fileInfo.Name,
                            FullPath = fileInfo.FullName,
                            IsDirectory = false,
                            IconPath = GetFileIcon(fileInfo.Extension, useUnicodeAsFallback: true),
                            FileSize = fileInfo.Length,
                            LastModified = fileInfo.LastWriteTime,
                            Children = new List<DirectoryTreeNode>()
                        };
                        node.Children.Add(fileNode);
                    }

                    // 如果文件太多，添加提示节点
                    if (files.Length > 20)
                    {
                        node.Children.Add(new DirectoryTreeNode
                        {
                            Name = $"... 还有 {files.Length - 20} 个文件",
                            FullPath = path,
                            IsDirectory = false,
                            IconPath = "📋", // 使用 Unicode 图标
                            Children = new List<DirectoryTreeNode>()
                        });
                    }
                }

                return node;
            }
            catch (Exception ex)
            {
                // 返回错误节点
                return new DirectoryTreeNode
                {
                    Name = $"{Path.GetFileName(path)} (访问失败)",
                    FullPath = path,
                    IsDirectory = true,
                    IconPath = GetDirectoryIcon(Path.GetFileName(path), hasError: true, useUnicodeAsFallback: true),
                    ErrorMessage = ex.Message,
                    Children = new List<DirectoryTreeNode>()
                };
            }
        }

        /// <summary>
        /// 根据文件扩展名获取对应的图标标识符
        ///
        /// 优先返回图标库标识符（如 FontAwesome），如果没有图标库则使用 Unicode 字符作为后备。
        /// 这样既支持专业的图标库，又确保在任何环境下都有可用的图标显示。
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <param name="useUnicodeAsFallback">是否使用 Unicode 字符作为后备（默认 true）</param>
        /// <returns>图标标识符，优先返回图标库标识符，否则返回 Unicode 字符</returns>
        private static string GetFileIcon(string extension, bool useUnicodeAsFallback = true)
        {
            var ext = extension.ToLower();

            // 如果使用 Unicode 作为后备（推荐用于目录树显示）
            if (useUnicodeAsFallback)
            {
                return ext switch
                {
                    // 代码文件 - 使用 Unicode 字符确保兼容性
                    ".cs" => "📄", // C# 代码
                    ".xaml" => "🎨", // XAML 界面
                    ".xml" => "📄", // XML 文档
                    ".json" => "📄", // JSON 数据
                    ".js" => "📄", // JavaScript
                    ".ts" => "📄", // TypeScript
                    ".html" => "🌐", // HTML 网页
                    ".css" => "🎨", // CSS 样式
                    ".sql" => "🗄️", // SQL 脚本

                    // 文档文件
                    ".txt" => "📝", // 文本文件
                    ".md" => "📝", // Markdown
                    ".pdf" => "📕", // PDF 文档
                    ".doc" or ".docx" => "📘", // Word 文档
                    ".xls" or ".xlsx" => "📊", // Excel 表格
                    ".ppt" or ".pptx" => "📊", // PowerPoint

                    // 媒体文件
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".svg" => "🖼️", // 图片
                    ".mp3" or ".wav" or ".flac" or ".aac" => "🎵", // 音频
                    ".mp4" or ".avi" or ".mkv" or ".mov" => "🎬", // 视频

                    // 压缩文件
                    ".zip" or ".rar" or ".7z" or ".tar" or ".gz" => "📦",

                    // 可执行文件
                    ".exe" or ".msi" => "⚙️",
                    ".dll" or ".so" => "🔧",
                    ".bat" or ".cmd" or ".sh" => "⚡",

                    // 配置文件
                    ".config" or ".ini" or ".conf" => "⚙️",
                    ".yml" or ".yaml" => "⚙️",

                    // 数据库文件
                    ".db" or ".sqlite" or ".mdb" => "🗄️",

                    // 默认文件图标
                    _ => "📄"
                };
            }
            else
            {
                // 返回图标库标识符（如 FontAwesome、Material Icons）
                return ext switch
                {
                    // 代码文件 - FontAwesome 图标类名
                    ".cs" => "fa-file-code",
                    ".xaml" => "fa-file-code",
                    ".xml" => "fa-file-code",
                    ".json" => "fa-file-code",
                    ".js" => "fa-file-code",
                    ".ts" => "fa-file-code",
                    ".html" => "fa-file-code",
                    ".css" => "fa-file-code",
                    ".sql" => "fa-database",

                    // 文档文件
                    ".txt" => "fa-file-text",
                    ".md" => "fa-file-text",
                    ".pdf" => "fa-file-pdf",
                    ".doc" or ".docx" => "fa-file-word",
                    ".xls" or ".xlsx" => "fa-file-excel",
                    ".ppt" or ".pptx" => "fa-file-powerpoint",

                    // 媒体文件
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".svg" => "fa-file-image",
                    ".mp3" or ".wav" or ".flac" or ".aac" => "fa-file-audio",
                    ".mp4" or ".avi" or ".mkv" or ".mov" => "fa-file-video",

                    // 压缩文件
                    ".zip" or ".rar" or ".7z" or ".tar" or ".gz" => "fa-file-archive",

                    // 可执行文件
                    ".exe" or ".msi" => "fa-cog",
                    ".dll" or ".so" => "fa-cogs",
                    ".bat" or ".cmd" or ".sh" => "fa-terminal",

                    // 配置文件
                    ".config" or ".ini" or ".conf" => "fa-cog",
                    ".yml" or ".yaml" => "fa-cog",

                    // 数据库文件
                    ".db" or ".sqlite" or ".mdb" => "fa-database",

                    // 默认文件图标
                    _ => "fa-file"
                };
            }
        }

        /// <summary>
        /// 获取目录图标标识符
        ///
        /// 目录图标推荐使用 Unicode 字符，因为目录类型相对固定，
        /// Unicode 图标既直观又无需外部依赖。
        /// </summary>
        /// <param name="directoryName">目录名称</param>
        /// <param name="hasError">是否有访问错误</param>
        /// <param name="useUnicodeAsFallback">是否使用 Unicode 字符（推荐 true）</param>
        /// <returns>目录图标标识符</returns>
        private static string GetDirectoryIcon(string directoryName, bool hasError = false, bool useUnicodeAsFallback = true)
        {
            if (hasError)
                return useUnicodeAsFallback ? "❌" : "fa-exclamation-triangle";

            var dirName = directoryName.ToLower();

            if (useUnicodeAsFallback)
            {
                // 使用 Unicode 字符 - 推荐用于目录（直观且无依赖）
                return dirName switch
                {
                    "src" or "source" => "📁", // 源代码目录
                    "bin" or "obj" => "🔧", // 编译输出目录
                    "docs" or "documentation" => "📚", // 文档目录
                    "tests" or "test" => "🧪", // 测试目录
                    "images" or "img" => "🖼️", // 图片目录
                    "css" or "styles" => "🎨", // 样式目录
                    "js" or "javascript" => "📄", // JavaScript 目录
                    "config" or "configuration" => "⚙️", // 配置目录
                    "logs" or "log" => "📋", // 日志目录
                    "temp" or "tmp" => "🗂️", // 临时目录
                    "backup" or "bak" => "💾", // 备份目录
                    "wwwroot" or "public" => "🌐", // Web 根目录
                    "controllers" => "🎮", // 控制器目录
                    "models" => "📊", // 模型目录
                    "views" => "👁️", // 视图目录
                    "services" => "🔧", // 服务目录
                    "data" => "🗄️", // 数据目录
                    "assets" or "resources" => "📦", // 资源目录
                    "lib" or "libraries" => "📚", // 库目录
                    "tools" or "utils" => "🔧", // 工具目录
                    _ => "📁" // 默认目录图标
                };
            }
            else
            {
                // 使用图标库标识符（如 FontAwesome）
                return dirName switch
                {
                    "src" or "source" => "fa-folder-open",
                    "bin" or "obj" => "fa-cogs",
                    "docs" or "documentation" => "fa-book",
                    "tests" or "test" => "fa-flask",
                    "images" or "img" => "fa-images",
                    "css" or "styles" => "fa-paint-brush",
                    "js" or "javascript" => "fa-code",
                    "config" or "configuration" => "fa-cog",
                    "logs" or "log" => "fa-list-alt",
                    "temp" or "tmp" => "fa-folder",
                    "backup" or "bak" => "fa-save",
                    "wwwroot" or "public" => "fa-globe",
                    "controllers" => "fa-gamepad",
                    "models" => "fa-cubes",
                    "views" => "fa-eye",
                    "services" => "fa-cogs",
                    "data" => "fa-database",
                    "assets" or "resources" => "fa-archive",
                    "lib" or "libraries" => "fa-book",
                    "tools" or "utils" => "fa-wrench",
                    _ => "fa-folder"
                };
            }
        }

        #endregion
    }

    /// <summary>
    /// WPF TreeView 绑定的目录树节点数据模型
    ///
    /// 实现了适用于 WPF 数据绑定的目录树节点，支持层次化数据绑定、
    /// 属性通知和延迟加载等 WPF 特性。
    /// </summary>
    public class DirectoryTreeNode : INotifyPropertyChanged
    {
        private string _name = "";
        private bool _isExpanded = false;
        private bool _isSelected = false;

        /// <summary>显示名称</summary>
        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        /// <summary>完整路径</summary>
        public string FullPath { get; set; } = "";

        /// <summary>是否为目录</summary>
        public bool IsDirectory { get; set; }

        /// <summary>图标路径</summary>
        public string IconPath { get; set; } = "";

        /// <summary>文件大小（仅文件有效）</summary>
        public long FileSize { get; set; }

        /// <summary>最后修改时间</summary>
        public DateTime LastModified { get; set; }

        /// <summary>错误信息（如果访问失败）</summary>
        public string? ErrorMessage { get; set; }

        /// <summary>子节点集合</summary>
        public List<DirectoryTreeNode> Children { get; set; } = new();

        /// <summary>是否展开</summary>
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                _isExpanded = value;
                OnPropertyChanged();
            }
        }

        /// <summary>是否选中</summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }

        /// <summary>格式化的文件大小</summary>
        public string FormattedFileSize
        {
            get
            {
                if (!IsDirectory && FileSize > 0)
                {
                    return FormatFileSize(FileSize);
                }

                return "";
            }
        }

        /// <summary>格式化的修改时间</summary>
        public string FormattedLastModified => LastModified.ToString("yyyy-MM-dd HH:mm");

        /// <summary>节点工具提示</summary>
        public string ToolTip
        {
            get
            {
                if (!string.IsNullOrEmpty(ErrorMessage))
                    return $"错误: {ErrorMessage}";

                if (IsDirectory)
                    return $"目录: {FullPath}\n子项: {Children.Count} 个";
                else
                    return $"文件: {FullPath}\n大小: {FormattedFileSize}\n修改: {FormattedLastModified}";
            }
        }

        /// <summary>属性变更事件</summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>触发属性变更通知</summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>格式化文件大小</summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }

            return $"{len:0.##} {sizes[order]}";
        }
    }
}