<UserControl
    d:DataContext="{d:DesignInstance dwg:NodeEditViewModel}"
    d:DesignHeight="500"
    d:DesignWidth="600"
    mc:Ignorable="d"
    mvvm:ViewModelLocator.AutoWireViewModel="True"
    x:Class="AlphaPM.Views.DWG.NodeEditView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dwg="clr-namespace:AlphaPM.ViewModels.DWG"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mvvm="http://prismlibrary.com/"
    xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!--  标签样式  -->
        <Style TargetType="TextBlock" x:Key="LabelStyle">
            <Setter Property="FontWeight" Value="Medium" />
            <Setter Property="Foreground" Value="{DynamicResource TextFillColorPrimaryBrush}" />
            <Setter Property="Margin" Value="0,0,0,6" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>


    </UserControl.Resources>

    <!--  使用 Card 容器提供现代化外观  -->
    <ui:Card Margin="16" Padding="24">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  标题区域  -->
            <StackPanel Grid.Row="0" Margin="0,0,0,24">
                <TextBlock
                    FontSize="20"
                    FontWeight="SemiBold"
                    Text="{Binding WindowTitle}" />
                <!--  Foreground="{DynamicResource TextFillColorPrimaryBrush}"  -->
                <TextBlock
                    FontSize="14"
                    Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                    Margin="0,4,0,0"
                    Text="{Binding NodeTypeDisplayName, StringFormat='类型: {0}'}" />
            </StackPanel>

            <!--  表单区域  -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!--  节点名称  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="节点名称 *" />
                    <ui:TextBox
                        PlaceholderText="请输入节点名称"
                        Margin="0,0,0,16"
                        MinHeight="36"
                        Text="{Binding NodeData.Name, UpdateSourceTrigger=PropertyChanged}">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="Tag24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  节点描述  -->
                    <TextBlock Style="{StaticResource LabelStyle}" Text="节点描述" />
                    <ui:TextBox
                        AcceptsReturn="True"
                        Height="80"
                        PlaceholderText="请输入节点描述（可选）"
                        Margin="0,0,0,16"
                        Text="{Binding NodeData.Description, UpdateSourceTrigger=PropertyChanged}"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto">
                        <ui:TextBox.Icon>
                            <ui:SymbolIcon Symbol="DocumentText24" />
                        </ui:TextBox.Icon>
                    </ui:TextBox>

                    <!--  人员信息 - 项目和栋号显示负责人  -->
                    <StackPanel Visibility="{Binding IsYearNode, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <TextBlock Style="{StaticResource LabelStyle}" Text="负责人" />
                        <ui:TextBox
                            PlaceholderText="请输入负责人姓名"
                            Margin="0,0,0,16"
                            MinHeight="36"
                            Text="{Binding NodeData.UserData.Manager, UpdateSourceTrigger=PropertyChanged}">
                            <ui:TextBox.Icon>
                                <ui:SymbolIcon Symbol="Person24" />
                            </ui:TextBox.Icon>
                        </ui:TextBox>
                    </StackPanel>

                    <!--  项目管理信息 - 项目和栋号显示  -->
                    <StackPanel Visibility="{Binding IsYearNode, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                        <TextBlock Style="{StaticResource LabelStyle}" Text="预算信息" FontSize="16" FontWeight="SemiBold" Margin="0,16,0,8" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="预算(万元)" />
                                <ui:NumberBox
                                    PlaceholderText="请输入预算金额"
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Value="{Binding Budget, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                    Minimum="0">
                                    <ui:NumberBox.Icon>
                                        <ui:SymbolIcon Symbol="Money24" />
                                    </ui:NumberBox.Icon>
                                </ui:NumberBox>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="项目状态" />
                                <ComboBox
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Text="{Binding ProjectStatus, UpdateSourceTrigger=PropertyChanged}"
                                    IsEditable="True">
                                    <ComboBoxItem Content="规划中"/>
                                    <ComboBoxItem Content="设计中"/>
                                    <ComboBoxItem Content="施工中"/>
                                    <ComboBoxItem Content="竣工"/>
                                    <ComboBoxItem Content="验收"/>
                                    <ComboBoxItem Content="交付"/>
                                    <ComboBoxItem Content="暂停"/>
                                    <ComboBoxItem Content="取消"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="开始时间" />
                                <DatePicker
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    SelectedDate="{Binding StartDate, UpdateSourceTrigger=PropertyChanged}" />
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="结束时间" />
                                <DatePicker
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    SelectedDate="{Binding EndDate, UpdateSourceTrigger=PropertyChanged}" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!--  建筑信息 - 仅栋号显示  -->
                    <StackPanel Visibility="{Binding IsBuildingNode, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Style="{StaticResource LabelStyle}" Text="建筑信息" FontSize="16" FontWeight="SemiBold" Margin="0,16,0,8" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="建筑类型" />
                                <ComboBox
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Text="{Binding BuildingType, UpdateSourceTrigger=PropertyChanged}"
                                    IsEditable="True">
                                    <ComboBoxItem Content="住宅"/>
                                    <ComboBoxItem Content="商业"/>
                                    <ComboBoxItem Content="办公"/>
                                    <ComboBoxItem Content="工业"/>
                                    <ComboBoxItem Content="公建"/>
                                    <ComboBoxItem Content="其他"/>
                                </ComboBox>

                                <TextBlock Style="{StaticResource LabelStyle}" Text="建筑面积(㎡)" />
                                <ui:NumberBox
                                    PlaceholderText="请输入建筑面积"
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Value="{Binding BuildingArea, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                    Minimum="0">
                                    <ui:NumberBox.Icon>
                                        <ui:SymbolIcon Symbol="Ruler24" />
                                    </ui:NumberBox.Icon>
                                </ui:NumberBox>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="楼层数" />
                                <ui:NumberBox
                                    PlaceholderText="请输入楼层数"
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Value="{Binding FloorCount, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                    Minimum="0" Maximum="200">
                                    <ui:NumberBox.Icon>
                                        <ui:SymbolIcon Symbol="Building24" />
                                    </ui:NumberBox.Icon>
                                </ui:NumberBox>

                                <TextBlock Style="{StaticResource LabelStyle}" Text="建筑状态" />
                                <ComboBox
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Text="{Binding BuildingStatus, UpdateSourceTrigger=PropertyChanged}"
                                    IsEditable="True">
                                    <ComboBoxItem Content="规划中"/>
                                    <ComboBoxItem Content="设计中"/>
                                    <ComboBoxItem Content="施工中"/>
                                    <ComboBoxItem Content="主体完工"/>
                                    <ComboBoxItem Content="装修中"/>
                                    <ComboBoxItem Content="竣工"/>
                                    <ComboBoxItem Content="验收"/>
                                    <ComboBoxItem Content="交付"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>

                        <TextBlock Style="{StaticResource LabelStyle}" Text="设计信息" FontSize="16" FontWeight="SemiBold" Margin="0,16,0,8" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="16"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="设计人员" />
                                <ui:TextBox
                                    PlaceholderText="请输入设计人员姓名"
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Text="{Binding Designer, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}">
                                    <ui:TextBox.Icon>
                                        <ui:SymbolIcon Symbol="PenSparkle24" />
                                    </ui:TextBox.Icon>
                                </ui:TextBox>

                                <TextBlock Style="{StaticResource LabelStyle}" Text="设计阶段" />
                                <ComboBox
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Text="{Binding DesignStage, UpdateSourceTrigger=PropertyChanged}"
                                    IsEditable="True">
                                    <ComboBoxItem Content="方案设计"/>
                                    <ComboBoxItem Content="初步设计"/>
                                    <ComboBoxItem Content="施工图设计"/>
                                    <ComboBoxItem Content="深化设计"/>
                                    <ComboBoxItem Content="竣工图"/>
                                    <ComboBoxItem Content="设计变更"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Style="{StaticResource LabelStyle}" Text="设计进度(%)" />
                                <ui:NumberBox
                                    PlaceholderText="请输入设计进度"
                                    Margin="0,0,0,16"
                                    MinHeight="36"
                                    Value="{Binding DesignProgress, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                    Minimum="0" Maximum="100">
                                    <ui:NumberBox.Icon>
                                        <ui:SymbolIcon Symbol="DataBarVertical24" />
                                    </ui:NumberBox.Icon>
                                </ui:NumberBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!--  提示信息  -->
                    <Border
                        Background="{DynamicResource InfoBarInformationalSeverityBackgroundBrush}"
                        BorderBrush="{DynamicResource InfoBarInformationalSeverityBorderBrush}"
                        BorderThickness="1"
                        CornerRadius="6"
                        Margin="0,16,0,0"
                        Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <ui:SymbolIcon
                                FontSize="16"
                                Foreground="{DynamicResource InfoBarInformationalSeverityIconForegroundBrush}"
                                Margin="0,0,8,0"
                                Symbol="Info24" />
                            <TextBlock
                                Foreground="{DynamicResource InfoBarInformationalSeverityTitleForegroundBrush}"
                                Text="* 标记为必填项，其他字段根据节点类型显示"
                                VerticalAlignment="Center" />
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>

            <!--  按钮区域  -->
            <Grid Grid.Row="2" Margin="0,24,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  取消按钮  -->
                <ui:Button
                    Appearance="Secondary"
                    Command="{Binding CancelCommand}"
                    Content="取消"
                    Grid.Column="1"
                    Icon="{ui:SymbolIcon Dismiss24}"
                    Margin="0,0,8,0"
                    MinWidth="100" />

                <!--  确认按钮  -->
                <ui:Button
                    Appearance="Primary"
                    Command="{Binding ConfirmCommand}"
                    Content="{Binding ConfirmButtonText}"
                    Grid.Column="2"
                    Icon="{ui:SymbolIcon Checkmark24}"
                    MinWidth="100" />
            </Grid>
        </Grid>
    </ui:Card>
</UserControl>
