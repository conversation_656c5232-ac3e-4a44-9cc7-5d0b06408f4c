# ContentDialog 使用指南

## 🎯 功能概述

新增的 `YContentDialog` 扩展类提供了专门用于 DWG 项目管理的对话框功能，支持动态创建和管理项目数据，并包含完整的输入验证和重复检查。

## 🔧 核心功能

### 1. 年份输入对话框
- **功能**：添加新年份，自动检查重复
- **验证**：年份范围 1900-2100
- **重复检查**：防止添加已存在的年份
- **默认值**：当前年份+1

### 2. 项目输入对话框
- **功能**：为指定年份添加项目
- **两步输入**：项目名称 → 项目描述
- **重复检查**：防止在同一年份下添加重复项目
- **返回功能**：支持返回上一步修改

### 3. 栋号输入对话框
- **功能**：为指定项目添加栋号
- **两步输入**：栋号名称 → 栋号描述
- **重复检查**：防止在同一项目下添加重复栋号
- **智能命名**：自动生成栋号序号

### 4. 删除确认对话框
- **功能**：删除前确认，显示影响范围
- **子项统计**：显示将被删除的子项目数量
- **安全设计**：默认选择"取消"按钮

## 📋 使用流程

### 添加年份流程
```
点击 ➕ 按钮
    ↓
📅 年份输入对话框
    ↓
输入年份（如：2026）
    ↓
验证：范围检查 + 重复检查
    ↓
确认添加 / 取消操作
```

### 添加项目流程
```
右键年份节点 → 添加子节点
    ↓
🏢 项目名称输入对话框
    ↓
输入项目名称（如：万科城市花园）
    ↓
重复检查 → 下一步
    ↓
🏢 项目描述输入对话框
    ↓
输入项目描述（如：高端住宅项目）
    ↓
确认添加 / 返回上一步
```

### 添加栋号流程
```
右键项目节点 → 添加子节点
    ↓
🏗️ 栋号名称输入对话框
    ↓
输入栋号名称（如：1#楼）
    ↓
重复检查 → 下一步
    ↓
🏗️ 栋号描述输入对话框
    ↓
输入栋号描述（如：高层住宅，32层）
    ↓
确认添加 / 返回上一步
```

### 删除确认流程
```
右键任意节点 → 删除节点
    ↓
🗑️ 删除确认对话框
    ↓
显示：要删除的项目 + 子项目数量
    ↓
确认删除 / 取消操作
```

## 🎨 界面特性

### 对话框样式
- **现代化设计**：使用 WPF-UI 组件
- **图标标识**：每种对话框都有专属图标
- **响应式布局**：自适应内容长度
- **焦点管理**：自动聚焦到输入框

### 错误处理
- **输入验证**：实时检查输入格式
- **错误提示**：红色文字显示错误信息
- **重试机制**：错误后保持用户输入，便于修改
- **异常捕获**：捕获并显示系统异常

## 🔍 验证规则

### 年份验证
- ✅ 不能为空
- ✅ 必须是数字
- ✅ 范围：1900-2100
- ✅ 不能重复

### 项目名称验证
- ✅ 不能为空
- ✅ 在同一年份下不能重复
- ✅ 支持中文、英文、数字

### 栋号名称验证
- ✅ 不能为空
- ✅ 在同一项目下不能重复
- ✅ 支持各种命名格式（1#楼、A栋、商业楼等）

## 💡 使用技巧

### 1. 快速输入
- 使用默认值作为起点
- 支持键盘导航（Tab、Enter）
- 输入框自动获得焦点

### 2. 错误处理
- 输入错误时，对话框会保持用户输入
- 可以直接修改错误部分，无需重新输入
- 错误信息会明确指出问题所在

### 3. 批量操作
- 先添加年份，再批量添加项目
- 利用默认值快速创建相似项目
- 使用描述字段记录重要信息

### 4. 数据管理
- 删除前会显示影响范围
- 支持撤销操作（通过取消按钮）
- 状态栏会显示操作结果

## 🚀 技术特性

### 异步操作
- 所有对话框操作都是异步的
- 不会阻塞主界面
- 支持取消操作

### 内存管理
- 对话框使用完毕后自动释放
- 避免内存泄漏
- 优化性能表现

### 扩展性
- 易于添加新的对话框类型
- 支持自定义验证规则
- 可以扩展更多输入字段

## 📞 常见问题

### Q: 为什么输入年份后提示重复？
A: 系统会检查已存在的年份，确保不会创建重复的年份节点。

### Q: 如何修改已添加的项目信息？
A: 目前支持删除重建，后续版本将添加编辑功能。

### Q: 删除年份会影响什么？
A: 删除年份会同时删除该年份下的所有项目和栋号，对话框会显示具体数量。

### Q: 输入框支持哪些字符？
A: 支持中文、英文、数字和常用符号，建议使用有意义的名称。

## 🔄 后续计划

- ✅ 基础输入对话框
- ✅ 重复检查功能
- ✅ 删除确认对话框
- 🔄 编辑现有项目功能
- 🔄 批量导入功能
- 🔄 数据导出功能
- 🔄 模板管理功能

---

*最后更新：2025年8月*
